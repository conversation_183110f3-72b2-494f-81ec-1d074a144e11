import CommonPage from './common_page'

class ProductListingPage extends CommonPage {
    url = '/furniture-c'
    
    selectors = {
        product_list: '[data-section=grid-v2-board]',
        plp_filters_bar: 'div[data-section=plp-filters-bar]',
        clear_filters: '[data-testid="filters-drawer-clear-filters-button"]',
        product_card: '[data-testid=product-card]',
        pill_with_filter: '[data-testid="filters-button"]',
    }

    selectProductFromGrid(item_index=0) {
        this.getListOfProducts().eq(item_index).find('a').first()
            .should('be.visible').click({ force: true })
    }

    selectProductFromGridOnMobile(item_index=0) {
        this.selectProductFromGrid(item_index)
    }

    getListOfProducts() {
        return cy.get(this.selectors.product_card, { timeout: 10000 })
    }

    openFilterDrawer() {
        cy.wait(1000)
        cy.get(this.selectors.pill_with_filter).click()
    }

    selectFilter(filter = new Filter) {
        cy.get(filter['drawerSection']).then(($tab) => {
            if (!$tab.hasClass('base-accordion--expanded')) {
                cy.wrap($tab).click({ timeout:10000 })
            }
        })
        cy.get(filter['button']).click()
        cy.wait(1000)
    }

    applyFilters() {
        cy.get('aside').find('button').last().click({ timeout: 10000 }) // lack of data-testid
    }

    clearFilters() {
        cy.get(this.selectors.pill_with_filter).click()
        cy.get(this.selectors.clear_filters).scrollIntoView()
        .should('be.visible').click({ timeout: 5000 })
    }
}

class Filter {
    constructor(name, drawerSection, button, attribute, results = []) {
        this.name = name
        this.drawerSection = drawerSection
        this.button = button
        this.attribute = attribute
        this.results = results
    }

    getExpectedResults() {
        return this.results
    }

    getComparableAttr() {
        return this.attribute
    }
}

const FILTERS = {
    red: new Filter('red',
        '[data-testid=filter-tab-colors]',
        '[data-testid=color-red]',
        'materialGroup',
        ['', 'red', 'terracotta', 'burgundy', 'clay_brown']
    ),
    blue: new Filter('blue',
        '[data-testid=filter-tab-colors]',
        '[data-testid=color-blue]',
        'materialGroup',
        [
            '', 'blue', 'sand', 'midnight_blue', 'sky_blue', 'white_misty_blue',
            'graphite_misty_blue', 'cashmere_misty_blue', 'inky_black',
            'rewool2_baby_blue', 'corduroy_blue_klein'
        ]
    ),
    originalModern: new Filter('originalModern',
        '[data-testid=filter-tab-productLines]',
        '[data-testid=product_lines_original_modern]',
        'shelfType',
        [1]
    ),
    premiumMatte: new Filter('premiumMatte',
        '[data-testid=filter-tab-materials]',
        '[data-testid=materials-matte]',
        'materialGroup',
        [
            'beige', 'beige_pink', 'cashmere_misty_blue', 'cashmere_sage_green', 'cashmere_stone_gray',
            'graphite', 'graphite_pink', 'graphite_misty_blue', 'graphite_sage_green', 'graphite_stone_gray',
            'white', 'white_pink', 'white_misty_blue', 'white_sage_green', 'white_stone_gray',
            'matte_black', 'inky_black', 'off_white', 'oyster_beige', 'powder_pink', 'pistachio_green'
        ]
    ),
}

export default new ProductListingPage()
export { FILTERS, Filter }
