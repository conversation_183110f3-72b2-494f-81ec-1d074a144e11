name: Lighthouse Score Summary

on:
  schedule:
    - cron: '0 17 * * *'  # 19:00 CEST (17 UTC)
  workflow_dispatch:

env:
  BASE_URL: https://tylko.com/de-de/configure/739,s
  PRODUCTION_LINE: Sofa

jobs:
  lighthouse:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: |
          npm install -g lighthouse@latest
          sudo apt-get update
          sudo apt-get install -y xvfb

      - name: Create report directory
        run: mkdir -p $GITHUB_WORKSPACE/lhci-reports

      - name: Run Lighthouse for Desktop
        run: |
          for i in {1..3}; do
            echo "Desktop run $i/3..."          
            xvfb-run --auto-servernum --server-args='-screen 0 1920x1080x24' \
              lighthouse ${{ env.BASE_URL }} \
              --screenEmulation.disabled \
              --throttling-method=provided \
              --output=json \
              --output-path=$GITHUB_WORKSPACE/lhci-reports/desktop-run$i.report.json \
              --chrome-flags="--no-sandbox --disable-dev-shm-usage --disable-extensions --disable-background-networking --js-flags=--max-old-space-size=4096 --enable-features=VaapiVideoDecoder,VaapiVideoEncoder --use-gl=desktop --enable-unsafe-webgpu --ignore-gpu-blocklist" \
              --max-wait-for-load=240000 \
              --timeout=360000 \
              --disable-storage-reset \
              --skip-audits=screenshot-thumbnails,final-screenshot \
              || { echo "Run $i failed with exit code $?"; true; }
            sleep 20
          done


      - name: Run Lighthouse for Mobile
        run: |
          for i in {1..3}; do
            echo "Mobile run $i/3..."
            xvfb-run --auto-servernum --server-args='-screen 0 390x844x24' \
              lighthouse ${{ env.BASE_URL }} \
              --screenEmulation.disabled \
              --throttling-method=simulate \
              --throttling.rttMs=150 \
              --throttling.throughputKbps=1638.4 \
              --throttling.downloadThroughputKbps=1638.4 \
              --throttling.uploadThroughputKbps=675 \
              --throttling.cpuSlowdownMultiplier=4 \
              --output=json \
              --output-path=$GITHUB_WORKSPACE/lhci-reports/mobile-run$i.report.json \
              --chrome-flags="--no-sandbox --disable-dev-shm-usage --disable-extensions --disable-background-networking --js-flags=--max-old-space-size=4096 --enable-features=VaapiVideoDecoder,VaapiVideoEncoder --use-gl=desktop --enable-unsafe-webgpu --ignore-gpu-blocklist" \
              --emulated-form-factor=none \
              --user-agent="Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1" \
              --screen-width=390 \
              --screen-height=844 \
              --device-scale-factor=3 \
              --max-wait-for-load=240000 \
              --timeout=360000 \
              --disable-storage-reset \
              --skip-audits=uses-http2,screenshot-thumbnails,final-screenshot \
              || { echo "Run $i failed with exit code $?"; true; }
            sleep 20
          done

      - name: Send scores to Slack
        run: |
          color() {
            if (( $(echo "$1 >= 90" | bc -l) )); then echo "🟢 $1"; \
            elif (( $(echo "$1 >= 50" | bc -l) )); then echo "🟡 $1"; \
            else echo "🔴 $1"; fi
          }

          # Function to calculate median, filtering out zeros (failed runs)
          median() {
            local values=$(echo "$1 $2 $3" | tr ' ' '\n' | grep -v "^0$" | grep -v "^$" | sort -n)
            local count=$(echo "$values" | wc -l)
            if [ "$count" -eq 0 ]; then
              echo "0"
            elif [ "$count" -eq 1 ]; then
              echo "$values"
            elif [ "$count" -eq 2 ]; then
              # Average of 2 values
              echo "$values" | awk '{sum+=$1} END {print sum/NR}'
            else
              # Median of 3 values
              echo "$values" | sed -n '2p'
            fi
          }

          # Function to round to integer
          round() {
            printf "%.0f" "$1"
          }

          M1=$GITHUB_WORKSPACE/lhci-reports/mobile-run1.report.json
          M2=$GITHUB_WORKSPACE/lhci-reports/mobile-run2.report.json
          M3=$GITHUB_WORKSPACE/lhci-reports/mobile-run3.report.json

          D1=$GITHUB_WORKSPACE/lhci-reports/desktop-run1.report.json
          D2=$GITHUB_WORKSPACE/lhci-reports/desktop-run2.report.json
          D3=$GITHUB_WORKSPACE/lhci-reports/desktop-run3.report.json

          echo "Checking if files exist:"
          ls -la $GITHUB_WORKSPACE/lhci-reports/
          echo ""

          # Extract mobile scores from all 3 runs
          M_PERF_1=$(jq '.categories.performance.score * 100' "$M1" 2>/dev/null || echo "0")
          M_PERF_2=$(jq '.categories.performance.score * 100' "$M2" 2>/dev/null || echo "0")
          M_PERF_3=$(jq '.categories.performance.score * 100' "$M3" 2>/dev/null || echo "0")
          M_PERF=$(round $(median $M_PERF_1 $M_PERF_2 $M_PERF_3))

          M_ACCESS_1=$(jq '.categories.accessibility.score * 100' "$M1" 2>/dev/null || echo "0")
          M_ACCESS_2=$(jq '.categories.accessibility.score * 100' "$M2" 2>/dev/null || echo "0")
          M_ACCESS_3=$(jq '.categories.accessibility.score * 100' "$M3" 2>/dev/null || echo "0")
          M_ACCESS=$(round $(median $M_ACCESS_1 $M_ACCESS_2 $M_ACCESS_3))

          M_BEST_1=$(jq '.categories["best-practices"].score * 100' "$M1" 2>/dev/null || echo "0")
          M_BEST_2=$(jq '.categories["best-practices"].score * 100' "$M2" 2>/dev/null || echo "0")
          M_BEST_3=$(jq '.categories["best-practices"].score * 100' "$M3" 2>/dev/null || echo "0")
          M_BEST=$(round $(median $M_BEST_1 $M_BEST_2 $M_BEST_3))

          M_SEO_1=$(jq '.categories.seo.score * 100' "$M1" 2>/dev/null || echo "0")
          M_SEO_2=$(jq '.categories.seo.score * 100' "$M2" 2>/dev/null || echo "0")
          M_SEO_3=$(jq '.categories.seo.score * 100' "$M3" 2>/dev/null || echo "0")
          M_SEO=$(round $(median $M_SEO_1 $M_SEO_2 $M_SEO_3))

          # Extract desktop scores from all 3 runs
          D_PERF_1=$(jq '.categories.performance.score * 100' "$D1" 2>/dev/null || echo "0")
          D_PERF_2=$(jq '.categories.performance.score * 100' "$D2" 2>/dev/null || echo "0")
          D_PERF_3=$(jq '.categories.performance.score * 100' "$D3" 2>/dev/null || echo "0")
          D_PERF=$(round $(median $D_PERF_1 $D_PERF_2 $D_PERF_3))

          D_ACCESS_1=$(jq '.categories.accessibility.score * 100' "$D1" 2>/dev/null || echo "0")
          D_ACCESS_2=$(jq '.categories.accessibility.score * 100' "$D2" 2>/dev/null || echo "0")
          D_ACCESS_3=$(jq '.categories.accessibility.score * 100' "$D3" 2>/dev/null || echo "0")
          D_ACCESS=$(round $(median $D_ACCESS_1 $D_ACCESS_2 $D_ACCESS_3))

          D_BEST_1=$(jq '.categories["best-practices"].score * 100' "$D1" 2>/dev/null || echo "0")
          D_BEST_2=$(jq '.categories["best-practices"].score * 100' "$D2" 2>/dev/null || echo "0")
          D_BEST_3=$(jq '.categories["best-practices"].score * 100' "$D3" 2>/dev/null || echo "0")
          D_BEST=$(round $(median $D_BEST_1 $D_BEST_2 $D_BEST_3))

          D_SEO_1=$(jq '.categories.seo.score * 100' "$D1" 2>/dev/null || echo "0")
          D_SEO_2=$(jq '.categories.seo.score * 100' "$D2" 2>/dev/null || echo "0")
          D_SEO_3=$(jq '.categories.seo.score * 100' "$D3" 2>/dev/null || echo "0")
          D_SEO=$(round $(median $D_SEO_1 $D_SEO_2 $D_SEO_3))

          # Count successful runs
          M_SUCCESS=$(echo "$M_PERF_1 $M_PERF_2 $M_PERF_3" | tr ' ' '\n' | grep -v "^0$" | wc -l | xargs)
          D_SUCCESS=$(echo "$D_PERF_1 $D_PERF_2 $D_PERF_3" | tr ' ' '\n' | grep -v "^0$" | wc -l | xargs)

          MESSAGE="🔬 *Lighthouse CI Benchmark*\n
          Production Line: ${PRODUCTION_LINE}\n
          URL: ${BASE_URL}\n
          _Note: CI scores measure performance trends. Lower than local due to CI hardware (~20pkt lower scores)._\n
          
          📱 *Mobile Report* (${M_SUCCESS}/3 runs succeeded, 4G throttled)\n
            • Performance: $(color $M_PERF)
            • Accessibility: $(color $M_ACCESS)
            • Best Practices: $(color $M_BEST)
            • SEO: $(color $M_SEO)

          🖥️ *Desktop Report* (${D_SUCCESS}/3 runs succeeded)\n
            • Performance: $(color $D_PERF)
            • Accessibility: $(color $D_ACCESS)
            • Best Practices: $(color $D_BEST)
            • SEO: $(color $D_SEO)"

          echo "$MESSAGE"
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\": \"$MESSAGE\"}" \
            ${{ secrets.SLACK_LIGHTHOUSE_WEBHOOK_URL }}