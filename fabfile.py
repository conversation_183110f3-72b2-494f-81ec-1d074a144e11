from contextlib import contextmanager
from distutils.util import strtobool
from time import localtime, sleep, strftime

from fabric.api import cd, env, execute, lcd, local, prefix, run, task
from fabric.contrib.files import exists
from fabric.decorators import roles
from fabric.utils import puts

env.release_name = None
env.release_dir = None
env.user = 'cstm'
env.ansible_ssh_user = 'cstm'
env.deploy_frontend = True
env.dry_deployment = False
env.skip_migrations = False
env.github_token = ''


@task
def gcp_dr():
    env.roledefs = {
        'django_master': ['***********'],
        'django_slave': [],
        'celery': ['***********'],
        'bastion': ['***********'],
        'proxy': ['ubuntu@***********'],
    }
    env.redis_ip = 'redis.dr'
    env.releases_to_keep = 4
    env.disable_known_hosts = True


@task
def gcp_staging():
    env.roledefs = {
        'django_master': ['***********'],
        'django_slave': [],
        'celery': ['***********'],
        'bastion': ['***********'],
        'proxy': ['ubuntu@***********'],
    }
    env.redis_ip = 'redis.staging'
    env.releases_to_keep = 2
    env.disable_known_hosts = True


@task
def gcp_prod():
    env.roledefs = {
        'django_master': ['***********'],
        'django_slave': ['***********'],
        'celery': ['***********'],
        'bastion': ['***********'],
        'proxy': ['ubuntu@***********', 'ubuntu@***********'],
    }
    env.redis_ip = 'redis.prod'
    env.releases_to_keep = 4
    env.disable_known_hosts = True


@task
def deploy(
    deploy_frontend='true',
    dry_deployment='false',
    skip_migrations='false',
    hard_restart='false',
    github_token='',
):
    parse_deployment_flags(
        deploy_frontend, dry_deployment, skip_migrations, hard_restart, github_token
    )
    generate_release_name()
    execute(deploy_backend_code)

    if env.deploy_frontend:
        execute(build_frontend)
        execute(deploy_frontend_code)

    execute(enable_nginx_overrides)

    execute(install_requirements)
    execute(fetch_envs)
    execute(collect_static)
    execute(sync_additional_django_machines)
    execute(collect_static_additional_machines)
    execute(refresh_symlinks)

    if env.dry_deployment:
        execute(stop_supervisor)
        execute(check_dry_deployment)
    else:
        execute(stop_celery)
        execute(run_migrations)
        if env.hard_restart:
            execute(hard_restart_django)
        else:
            execute(restart_django)
        execute(restart_celery)

    execute(del_cached_templates)
    execute(del_old_releases, releases_to_keep=env.releases_to_keep)

    execute(disable_nginx_overrides)

    puts(
        'Install_all_local finished {}'.format(
            strftime('%Y-%m-%d %H:%M:%S', localtime())
        )
    )


def parse_deployment_flags(
    deploy_frontend, dry_deployment, skip_migrations, hard_restart, github_token
):
    env.deploy_frontend = strtobool(deploy_frontend)
    env.dry_deployment = strtobool(dry_deployment)
    env.skip_migrations = strtobool(skip_migrations)
    env.hard_restart = strtobool(hard_restart)
    env.github_token = github_token


def generate_release_name():
    env.release_name = strftime('%y%m%d_%H%M%S')
    env.release_dir = '~/releases/{}'.format(env.release_name)
    puts('Deploying release {}'.format(env.release_name))


@task
@roles('django_master')
def deploy_backend_code():
    """
    1. Copy code from previous release to the new release (rsync) to speed things up
    2. Create new release dir if copy didn't work (mkdir - p)
    3. Sync fresh code from agent (rsync)
    """
    run('mkdir -p ~/releases')
    run(
        'ls ~/releases'
        '| egrep "[0-9]{{6}}_[0-9]{{6}}" '
        '| tail -1 '
        '| xargs -I{{}} '
        'rsync -a '
        '--stats '
        '--exclude="/src/static" '
        '--exclude="/src/r_static" '
        '--exclude="/src/media" '
        '--exclude="*.pyc" '
        ' ~/releases/"{{}}"/ {}'.format(env.release_dir)
    )
    run('mkdir -p {}'.format(env.release_dir))
    run('mkdir -p {}/src'.format(env.release_dir))
    # hack to change update date of new dir which is the same as previous release
    # dir (after rsync)
    run('touch {}/e'.format(env.release_dir))
    local(
        'rsync -az '
        '--stats '
        '--checksum '
        '--delete '
        '-e "ssh -l cstm -p 22 -o StrictHostKeyChecking=no" '
        '--exclude="/static" '
        '--exclude="/r_static" '
        '--exclude="/media" '
        '--exclude="/frontend_cms/static/dist" '  # do not sync frontend build -
        '--exclude="/frontend_cms/static/dist_vue" '  # we do that separately if needed
        '--exclude="*.pyc" '
        './src/ {host}:{release_dir}/src/'.format(
            host=env.host, release_dir=env.release_dir
        )
    )


@task
@roles('django_master')
def build_frontend():
    # rebuild coz npm i does not create vendor file (weird)
    # (npm is a piece of crap, so not weird at all)
    with lcd('frontend_src'):
        local(
            'printf "@tylkocom:registry=https://npm.pkg.github.com/\n//npm.pkg.github.com/:_authToken=%s" > .npmrc'
            % env.github_token
        )
        local(
            "export CXXFLAGS='-include /usr/include/c++/11/limits' "
            "&& npm ci --legacy-peer-deps && npm rebuild node-sass"
        )
        local('gulp build')
        local('npm run prod:part1')
        local('npm run prod:part2')
        local('npm run prod:part3')


@task
@roles('django_master')
def deploy_frontend_code():
    local(
        'rsync -az '
        '--stats '
        '--checksum '
        '--delete '
        '-e "ssh -l cstm -p 22 -o StrictHostKeyChecking=no" '
        './src/frontend_cms/static/ {host}:~/releases/{release}/src/frontend_cms/static'.format(  # noqa: E501
            host=env.host, release=env.release_name
        )
    )


@task
@roles('django_master', 'django_slave', 'celery', 'bastion')
def refresh_symlinks():
    run('ln -sfn {}/src ~/app'.format(env.release_dir))
    run('ln -sfn ~/app /home/<USER>/src')
    run('mkdir -p /home/<USER>/releases/logs')
    if exists('/home/<USER>/app/logs'):
        run('rm /home/<USER>/app/logs -rf')
    run('ln -s -f ~/releases/logs ~/app/logs')


@task
@roles('django_master')
def install_requirements():
    with _virtualenv():
        run('poetry install --sync')


@task
@roles('django_master')
def fetch_envs():
    run('cp ~/releases/.env.prod {}/src/.env'.format(env.release_dir))


@task
@roles('django_master')
def collect_static():
    with _virtualenv():
        if env.get('envname') == 'staging_tylko_django':
            run(
                'python manage.py message_about_unapplied_migrations --env=stg --plan=database'  # noqa: E501
            )

        run('python manage.py collectstatic --noinput --verbosity=0')
        run('ln -s r_static static')
        for proxy in env.roledefs['proxy']:
            run(
                'rsync -az --stats --delete -e "ssh -l ubuntu '
                '-i ~/.ssh/id_rsa -o StrictHostKeyChecking=no" '
                'r_static {}:/var/www/cstm'.format(
                    proxy,
                )
            )


@task
@roles('django_slave', 'celery', 'bastion')
def collect_static_additional_machines():
    with _virtualenv():
        run('python manage.py collectstatic --noinput --verbosity=0')
        run('ln -s r_static static')


@task
@roles('django_master', 'django_slave', 'celery')
def stop_supervisor():
    run('sudo supervisorctl stop all || true')


@task
@roles('django_master')
def check_dry_deployment():
    with _virtualenv():
        run('python manage.py check --deploy')


@task
@roles('django_master')
def run_migrations():
    if env.skip_migrations:
        puts('Skipping migrations.')
    else:
        with _virtualenv():
            run('python manage.py migrate --noinput')


@task
@roles('celery')
def stop_celery():
    run('sudo supervisorctl stop cstm:* || true')


@task
@roles('celery')
def restart_celery():
    while True:
        if run('[[ -e "/tmp/send_mail.lock" ]] && echo "1" || echo "0"') == '0':
            break
        sleep(5)
    run('sudo supervisorctl reread')
    run('sudo supervisorctl restart cstm:*')
    run('sudo supervisorctl restart cstm_aux:*')


@task
@roles('django_master', 'django_slave')
def restart_django():
    run('sudo supervisorctl reread')
    run('sudo supervisorctl start cstm:*')
    run(
        r"find /tmp -maxdepth 1 -name gunicorn_*.pid -exec sh -c 'kill -HUP $(cat $0)' {} \;"  # noqa: E501
    )


@task
@roles('django_master', 'django_slave')
def hard_restart_django():
    run('sudo supervisorctl update')
    run('sudo supervisorctl restart all')


@task
@roles('proxy')
def enable_nginx_overrides():
    run(
        'ln -s -f /etc/nginx/overrides/deployment_admin /etc/nginx/overrides/deployment'
    )
    run('sudo service nginx reload')


@task
@roles('proxy')
def disable_nginx_overrides():
    run(
        'ln -s -f /etc/nginx/overrides/deployment_blank /etc/nginx/overrides/deployment'
    )
    run('sudo service nginx reload')


@task
@roles('django_slave', 'celery', 'bastion')
def sync_additional_django_machines():
    run('mkdir -p ~/releases')
    run(
        'ls ~/releases'
        '| egrep "[0-9]{{6}}_[0-9]{{6}}" '
        '| tail -1 '
        '| xargs -I{{}} '
        'rsync -a '
        '--stats '
        '--exclude="/src/static" '
        '--exclude="/src/r_static" '
        '--exclude="/src/media" '
        ' ~/releases/"{{}}"/ {}'.format(env.release_dir)
    )
    run(
        'rsync -az '
        '--stats '
        '--checksum '
        '--delete '
        '-e "ssh -l cstm -p 22 -o StrictHostKeyChecking=no" '
        '--exclude="/src/static" '
        '--exclude="/src/r_static" '
        '--exclude="/src/media" '
        '{main_django_host}:{release_dir}/ {release_dir}/'.format(
            release_dir=env.release_dir,
            main_django_host=env.roledefs['django_master'][0],
        )
    )
    run(
        'rsync -az '
        '--stats '
        '--checksum '
        '--delete '
        '-e "ssh -l cstm -p 22 -o StrictHostKeyChecking=no" '
        '{main_django_host}:~/app_env/ ~/app_env/'.format(
            main_django_host=env.roledefs['django_master'][0]
        )
    )


@task
@roles('django_master')
def del_cached_templates():
    delete_keys_command_template = (
        'redis-cli -h {redis} -n {bucket} KEYS "{key_expression}" | '
        'xargs --no-run-if-empty redis-cli -h {redis} -n {bucket} DEL'
    )
    run(
        delete_keys_command_template.format(
            bucket=0,
            redis=env.redis_ip,
            key_expression='*template.cache*',
        )
    )
    run(
        delete_keys_command_template.format(
            bucket=0,
            redis=env.redis_ip,
            key_expression='*views.decorators.cache.cache_page*',
        )
    )
    run(
        delete_keys_command_template.format(
            bucket=0,
            redis=env.redis_ip,
            key_expression='*pricing_registry',
        )
    )


@task
@roles('django_master', 'django_slave', 'celery', 'bastion')
def del_old_releases(releases_to_keep=1):
    list_command = "ls ~/releases | grep -E '[0-9]{{6}}_[0-9]{{6}}' | head -n -{}"
    list_command = list_command.format(releases_to_keep)
    delete_command = 'xargs -I {} rm -r ~/releases/{}'
    run('{} | {}'.format(list_command, delete_command))


@task
@roles('django_master')
def use_latest_release():
    release_name = run(
        "ls /home/<USER>/releases | grep -E '[0-9]{6}_[0-9]{6}' | tail -n 1"
    )
    release_name = release_name.strip()
    env.release_name = release_name
    env.release_dir = '~/releases/{}'.format(env.release_name)
    return release_name


@task
@roles('django_master')
def purge_cloudflare_cache():
    execute(use_latest_release)
    with _virtualenv():
        run('python manage.py purge_cloudflare_cache')


@contextmanager
def _virtualenv():
    with cd('{}/src'.format(env.release_dir)):
        with prefix('source /home/<USER>/app_env/bin/activate '):
            yield
