from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    TYPE_CHECKING,
    TypeVar,
)

from gallery.types import MaterialItemType
from logistic.constants import AvailableServicesChoices
from logistic.schema import AvailableService
from regions.services.limitations import LimitationService
from regions.services.regionalized_price import RegionCalculationsObject
from services.enums import AdditionalServiceKind
from services.services.old_sofa_collection import OldSofaCollectionService
from services.services.white_gloves_delivery import WhiteGlovesDeliveryService
from vouchers.constants import (
    ASSEMBLY_VOUCHER_CODE,
    is_assembly_promo_active,
)

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from orders.models import (
        Order,
        OrderItem,
    )

TItem = TypeVar('TItem', bound='CartItem | OrderItem')
TParent = TypeVar('TParent', bound='Cart | Order')


class AdditionalServicesResolver:
    instance_object_field = None

    def __init__(self, items: list[TItem]) -> None:
        self.items = items
        self.instance: TParent = items[0].parent
        self.available_services = []
        self.region = self.instance.region
        self.rco = RegionCalculationsObject(region=self.instance.region)

    def resolve_services_for_items(self) -> None:
        delivery_service = self._create_delivery_service()
        if delivery_extra_service := self._create_delivery_extra_service(
            price=delivery_service.price, promo_price=delivery_service.promo_price
        ):
            # standard delivery OR delivery with extra services (e.g. assembly)
            delivery_service.active = not delivery_extra_service.active
            self.available_services.append(delivery_service)
            self.available_services.append(delivery_extra_service)
        else:
            self.available_services.append(delivery_service)
        self._add_old_sofa_collection()

    def _create_delivery_service(self) -> AvailableService:
        delivery_price = Decimal(
            sum(item.aggregate_region_delivery_price for item in self.items)
        )
        delivery_promo_price = Decimal(
            sum(
                item.aggregate_region_delivery_price
                - item.aggregate_region_delivery_promo_value
                for item in self.items
            )
        )

        return AvailableService(
            service_name=self._get_delivery_service_name(),
            price=delivery_price,
            promo_price=delivery_promo_price,
            active=True,
        )

    def _create_delivery_extra_service(
        self, price: Decimal, promo_price: Decimal
    ) -> AvailableService | None:
        """
        Extra delivery service should aggregate prices for:
            - delivery;
            - all the extra services
        """
        available_service = AvailableService(
            service_name=AvailableServicesChoices.ASSEMBLY,
            price=price,
            promo_price=promo_price,
            active=False,
        )
        if assembly_service := self._create_assembly_service():
            available_service.service_name = assembly_service.service_name
            available_service.price += assembly_service.price
            available_service.promo_price += assembly_service.promo_price
            available_service.active = assembly_service.active
        if white_gloves_delivery_service := self._create_white_glove_delivery():
            available_service.service_name = white_gloves_delivery_service.service_name
            available_service.price += white_gloves_delivery_service.price
            available_service.promo_price += white_gloves_delivery_service.promo_price
            available_service.active = (
                available_service.active or white_gloves_delivery_service.active
            )

        if assembly_service or white_gloves_delivery_service:
            return available_service

    def _create_assembly_service(self) -> AvailableService | None:
        """
        Promo price is already calculated in the item.
        Base price needs to be calculated 😏
        """
        region_assembly_promo_price = Decimal(
            sum(item.aggregate_region_assembly_price for item in self.items)
        )
        if self._assembly_promo_applies:
            region_assembly_price = self._calculate_region_assembly_price()
        else:
            # save doing all the calculations if not needed
            region_assembly_price = region_assembly_promo_price
        limitation_service = LimitationService(region=self.instance.region)
        if limitation_service.is_assembly_available and region_assembly_price:
            return AvailableService(
                service_name=AvailableServicesChoices.ASSEMBLY,
                price=region_assembly_price,
                promo_price=region_assembly_promo_price,
                active=self.instance.assembly,
            )

    def _calculate_region_assembly_price(self) -> Decimal:
        region_assembly_price = Decimal('0')
        for item in self.items:
            if item.is_service:
                continue
            material_item: MaterialItemType = item.sellable_item
            assembly_price = (
                material_item.get_assembly_price(region=self.region) * item.quantity
            )
            region_assembly_price += self.rco.calculate_regionalized(assembly_price)
        return region_assembly_price

    def _create_white_glove_delivery(self) -> AvailableService | None:
        if item := self._get_additional_service_item(
            AdditionalServiceKind.WHITE_GLOVES_DELIVERY
        ):
            region_promo_price = item.region_price
            if self._assembly_promo_applies:
                price = WhiteGlovesDeliveryService(self.instance).calculate_price(
                    discount_value=Decimal('1')
                )
                region_price = self.rco.calculate_regionalized(price)
            else:
                # save doing all the calculations if not needed
                region_price = region_promo_price
            return AvailableService(
                service_name=AvailableServicesChoices.WHITE_GLOVES_DELIVERY,
                price=region_price,
                promo_price=region_promo_price,
                active=True,
            )
        service = WhiteGlovesDeliveryService(self.instance)
        if not service.items_valid_for_service(self.items):
            return

        promo_price = service.calculate_price()
        if self._assembly_promo_applies:
            price = service.calculate_price(discount_value=Decimal('1'))
        else:
            price = promo_price
        return AvailableService(
            service_name=AvailableServicesChoices.WHITE_GLOVES_DELIVERY,
            price=self._get_region_price(price),
            promo_price=self._get_region_price(promo_price),
            active=False,
        )

    def _add_old_sofa_collection(self):
        if item := self._get_additional_service_item(
            AdditionalServiceKind.OLD_SOFA_COLLECTION
        ):
            self.available_services.append(
                AvailableService(
                    service_name=AvailableServicesChoices.OLD_SOFA_COLLECTION,
                    price=item.region_price,
                    promo_price=item.region_price,
                    active=True,
                )
            )
        service = OldSofaCollectionService(self.instance)
        if service.items_valid_for_service(self.items):
            price = service.calculate_price()
            self.available_services.append(
                AvailableService(
                    service_name=AvailableServicesChoices.OLD_SOFA_COLLECTION,
                    price=self._get_region_price(price),
                    promo_price=self._get_region_price(price),
                    active=False,
                )
            )

    def _get_additional_service_item(self, kind):
        for item in self.items:
            item_object = item.sellable_item
            if item.is_service and getattr(item_object, 'kind', None) == kind:
                return item
        return None

    def _get_delivery_service_name(self) -> AvailableServicesChoices:
        limitation_service = LimitationService(region=self.instance.region)
        if limitation_service.is_doorstep_sotty_delivery and any(
            item.is_legit_sotty for item in self.items
        ):
            return AvailableServicesChoices.DOORSTEP_DELIVERY
        return AvailableServicesChoices.HOME_DELIVERY

    def _get_region_price(self, base_price: Decimal) -> Decimal:
        return self._round_up_to_integer(self.rco.calculate_regionalized(base_price))

    @staticmethod
    def _round_up_to_integer(value: Decimal) -> Decimal:
        return Decimal(value).quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    @property
    def _assembly_promo_applies(self) -> bool:
        return is_assembly_promo_active() and ASSEMBLY_VOUCHER_CODE in list(
            self.instance.vouchers.values_list('code', flat=True)
        )
