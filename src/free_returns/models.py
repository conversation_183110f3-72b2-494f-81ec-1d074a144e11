from typing import Union

from django.db import (
    models,
    transaction,
)
from django.utils.html import format_html

from custom.tasks import send_to_n8n
from free_returns.enums import (
    FreeReturnStatusChoices,
    FreeReturnTransportChoices,
    ReasonChoices,
)
from loose_ends.models import ModelHasLooseEnd


class FreeReturn(ModelHasLooseEnd, models.Model):
    notification_date = models.DateField(
        default=None,
        null=True,
        blank=True,
        db_index=True,
    )
    is_packed = models.<PERSON>oleanField(
        verbose_name='Furniture already packed',
    )
    is_need_packaging = models.BooleanField(
        verbose_name='Send packaging materials',
    )
    # can delivery be shipped ASAP with or without date confirmation
    is_send_asap = models.BooleanField(
        default=False,
        verbose_name='Ready to pickup, send courier',
    )
    notes = models.TextField(
        blank=True,
    )
    note = models.CharField(
        max_length=1000,
    )
    reason = models.TextField()
    reason_tag = models.CharField(
        choices=ReasonChoices.choices,
        max_length=255,
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        db_index=True,
    )
    # end date of free_return for auto correction in the future
    finished_at = models.DateTimeField(
        blank=True,
        null=True,
        default=None,
        editable=False,
    )
    # cancellation data of free_return for auto correction in the future
    aborted_at = models.DateTimeField(
        blank=True,
        null=True,
        default=None,
        editable=False,
    )
    tracking_number = models.CharField(
        max_length=300,
        blank=True,
    )

    transport_method = models.PositiveSmallIntegerField(
        choices=FreeReturnTransportChoices.choices(),
        default=FreeReturnTransportChoices.UNKNOWN.value,
    )
    status = models.PositiveSmallIntegerField(
        choices=FreeReturnStatusChoices.choices(),
        default=FreeReturnStatusChoices.UNKNOWN.value,
    )
    correction_request = models.OneToOneField(
        'customer_service.CSCorrectionRequest',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='free_return',
    )

    class Meta:
        ordering = [
            '-aborted_at',
            '-finished_at',
            '-updated_at',
        ]

    def save(self, *args, **kwargs):
        exists = bool(self.pk)
        super().save(*args, **kwargs)
        action = 'created' if not exists else 'updated'
        transaction.on_commit(
            lambda: send_to_n8n.delay(
                model_name='FreeReturn',
                object_id=self.id,
                action=action,
            )
        )

    @property
    def orders_ids(self) -> list[int]:
        """Return list of Order ids related to given FreeReturn via OrderItem."""
        return [item.order_id for item in self.orderitem_set.all()]

    def get_order(self, return_plain_id=False) -> Union[str, None, list]:  # noqa: RUF036
        order = set()
        for item in self.orderitem_set.all():
            order.add(str(item.order_id))
        if len(order) == 0:
            return '-' if return_plain_id is False else None
        return (
            format_html('<br/>'.join(order))
            if return_plain_id is False
            else list(order)
        )

    get_order.short_description = 'Order id'

    def get_order_items(self) -> str:
        order_items = set()
        for item in self.orderitem_set.all():
            for product in item.product_set.all():
                order_items.add(
                    '{}: {}_{} - {}'.format(
                        product.manufactor,
                        product.cached_shelf_type,
                        product.id,
                        item.order_item.get_item_description()['material'],
                    )
                )
        if len(order_items) == 0:
            return '-'
        return format_html('<br/>'.join(order_items))

    get_order_items.short_description = 'Items returned'

    def get_paid_at(self) -> str:
        order = set()
        for item in self.orderitem_set.all():
            order.add(item)
        if len(order) == 0:
            return '-'
        paid_at_list = set()
        for item in order:
            paid_at_list.add(item.order.paid_at)
        return format_html(
            '<br/>'.join(
                ['-' if x is None else x.strftime('%Y-%m-%d') for x in paid_at_list]
            )
        )

    get_paid_at.short_description = 'Paid at'

    def get_delivery_dates(self) -> Union[set, str]:
        delivery_dates = {
            item.order.get_delivery_date() or '-' for item in self.orderitem_set.all()
        }
        return delivery_dates or '-'
