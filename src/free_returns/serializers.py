from rest_framework import serializers

from free_returns.models import FreeReturn
from orders.models import OrderItem


class ForLogisticFreeReturnSerializer(serializers.ModelSerializer):
    class Meta:
        model = FreeReturn
        fields = [
            'id',
            'status',
        ]


class OrderItemForFreeReturnSerializer(serializers.ModelSerializer):
    furniture_type = serializers.CharField(source='get_furniture_type')
    furniture_id = serializers.IntegerField(source='get_furniture_id')

    class Meta:
        model = OrderItem
        fields = [
            'id',
            'product_name',
            'invoice_product_name',
            'quantity',
            'furniture_type',
            'furniture_id',
            'price',
            'price_net',
        ]


class FreeReturnWebhookSerializer(serializers.ModelSerializer):
    order_items = OrderItemForFreeReturnSerializer(many=True)

    class Meta:
        model = FreeReturn
        fields = [
            'id',
            'notification_date',
            'is_packed',
            'is_need_packaging',
            'is_send_asap',
            'notes',
            'note',
            'reason',
            'reason_tag',
            'created_at',
            'updated_at',
            'finished_at',
            'aborted_at',
            'tracking_number',
            'transport_method',
            'status',
            'correction_request',
            'order_items',
        ]
