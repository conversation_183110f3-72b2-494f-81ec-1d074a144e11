from decimal import Decimal
from typing import Optional

from django.contrib.contenttypes.fields import GenericRelation
from django.db import models

from custom.enums import Furniture
from custom.models import Timestampable
from regions.cached_region import CachedRegionData
from regions.services.regionalized_price import RegionCalculationsObject
from regions.types import RegionLikeObject
from services.enums import AdditionalServiceKind


class AdditionalService(Timestampable):
    kind = models.CharField(choices=AdditionalServiceKind.choices, max_length=31)
    price = models.DecimalField(
        max_digits=6,
        decimal_places=0,
        default=0,
    )
    order_items = GenericRelation(
        'orders.OrderItem',
        related_query_name='additional_service',
    )
    cart_items = GenericRelation(
        'carts.CartItem', related_query_name='additional_service'
    )

    def __str__(self) -> str:
        return f'AdditionalService ({self.get_kind_display()})'

    def get_delivery_price(self, region=None) -> Decimal:
        # added to keep compatibility with SellableItem interface
        return Decimal('0')

    def get_pretty_id_name(self) -> str:
        return 'service'

    def get_pattern_name(self) -> str:
        return 'service'

    def is_t03_wardrobe(self) -> bool:
        return False

    def get_weight_from_cached_serialization(self) -> int:
        return 0

    def get_item_description(self) -> dict:
        return {
            'name': self.get_kind_display(),
            'material': 'service',
            'dimensions': 'service',
        }

    def get_delivery_time_days(self) -> int:
        # added to keep compatibility with SellableItem interface
        # it always will be with furniture, so in order delivery should be shown from it
        return 4

    @property
    def product_type(self):
        # WTF?!
        return Furniture.sotty.value

    @property
    def preview(self) -> None:
        return None

    @property
    def is_assembly_service_required(self) -> bool:
        return False

    def get_variant(self):
        # for compatibility with SellableItem interface
        return f'Service: {self.get_kind_display()}'

    def get_regionalized_price(
        self,
        region: RegionLikeObject | None = None,
        region_calculations_object: RegionCalculationsObject | None = None,
    ) -> Decimal:
        if not region_calculations_object:
            region_calculations_object = RegionCalculationsObject(region)

        return region_calculations_object.calculate_regionalized(self.price)

    def get_sale_price(self, *args, regular_price: Decimal, **kwargs) -> Decimal:
        # added to keep compatibility with SellableItem interface.
        # no promo for services right now
        return regular_price

    def get_regionalized_price_display(
        self,
        region: Optional[CachedRegionData] = None,
        region_calculations_object: Optional[RegionCalculationsObject] = None,
    ) -> str:
        # common with SellableFurnitureAbstract, might be extracted
        regionalized_price = self.get_regionalized_price(
            region=region,
            region_calculations_object=region_calculations_object,
        )
        return region.get_format_price(regionalized_price)
