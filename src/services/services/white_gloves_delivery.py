from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    TypeVar,
)

from custom.enums import Furniture
from gallery.constants import WHITE_GLOVES_DELIVERY_PRICE_MAP
from promotions.utils import get_active_promotion
from regions.services.limitations import LimitationService
from regions.services.regionalized_price import RegionCalculationsObject
from services.enums import AdditionalServiceKind
from services.errors import WhiteGlovesDeliveryValidationError
from services.models import AdditionalService
from services.services.base import BaseAdditionalService
from vouchers.constants import (
    ADDITIONAL_SERVICE_PROMO,
    ASSEMBLY_VOUCHER_CODE,
    is_assembly_promo_active,
)

if TYPE_CHECKING:
    from carts.models import CartItem
    from orders.models import OrderItem

TItem = TypeVar('TItem', bound='CartItem | OrderItem')


class WhiteGlovesDeliveryService(BaseAdditionalService):
    """
    Dedicated to adding and removing additional service called WhiteGlovesDelivery.
    Checks all conditions, dictates price.
    Currently, it only works per entire cart/order, not per single item
    """

    service_kind = AdditionalServiceKind.WHITE_GLOVES_DELIVERY
    error_class = WhiteGlovesDeliveryValidationError

    def calculate_price(self, **kwargs) -> Decimal:
        new_price = (
            WHITE_GLOVES_DELIVERY_PRICE_MAP[Furniture.sotty]
            * self.cart.get_total_legit_sotty_items()
        )
        if not is_assembly_promo_active() or not self.cart.assembly_promo_applies():
            return new_price

        discount_value = kwargs.get('discount_value')
        if discount_value is None:
            discount_value = self._calculate_discount_value()
        return new_price * discount_value

    def _calculate_discount_value(self) -> Decimal:
        """
        Work-around for the https://cstm-tasks.atlassian.net/browse/ECO-8000 task

        A new price needs to be calculated to check if the tape promo applies.
        """
        promotion = get_active_promotion(region=self.cart.region)
        if not promotion:
            return Decimal('1.0')
        rco = RegionCalculationsObject(region=self.cart.region)
        promo_amount_start = rco.calculate_regionalized(
            ADDITIONAL_SERVICE_PROMO[self.service_kind].amount_starts
        )
        region_material_items_price = self.cart.get_items_price()
        if region_material_items_price > promo_amount_start:
            return ADDITIONAL_SERVICE_PROMO[self.service_kind].discount_value
        else:
            return Decimal('1.0')

    def _check_conditions_add(self) -> None:
        limitation_service = LimitationService(region=self.cart.region)
        if not limitation_service.is_white_gloves_delivery_available:
            raise WhiteGlovesDeliveryValidationError(
                'White Gloves Delivery is not available in this region'
            )
        if not self.cart.has_legit_sotty:
            raise WhiteGlovesDeliveryValidationError(
                'Cart does not have legit sofa to deliver'
            )
        if self.cart.has_white_gloves_delivery:
            raise WhiteGlovesDeliveryValidationError(
                'Cart already has white gloves delivery'
            )

    def items_valid_for_service(self, items: list[TItem]) -> bool:
        limitation_service = LimitationService(region=self.cart.region)
        if not limitation_service.is_white_gloves_delivery_available:
            return False
        if self.cart.has_white_gloves_delivery:
            return False
        return any(item.is_s01 and item.is_legit_sotty for item in items)

    def _get_service_from_cart(self) -> AdditionalService | None:
        return self.cart.get_white_gloves_delivery_service()
