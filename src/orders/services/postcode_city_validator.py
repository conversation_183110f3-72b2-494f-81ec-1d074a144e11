import hashlib
import logging

from django.conf import settings
from django.core.cache import cache

import pgeocode
import requests

from unidecode import unidecode

from custom.enums import LanguageEnum
from custom.models import Countries
from custom.utils.in_memory_cache import expiring_lru_cache

logger = logging.getLogger('cstm')


GEONAMES_SEARCH = 'http://api.geonames.org/searchJSON'
GEONAMES_GET = 'http://api.geonames.org/getJSON'
GEOCODE_URL = 'https://maps.googleapis.com/maps/api/geocode/json'

CACHE_TIMEOUT = 60 * 60 * 24  # 24 hours
API_TIMEOUT = 10  # seconds
SUPPORTED_COUNTRIES_COUNT = len(Countries.get_all())


@expiring_lru_cache(maxsize=SUPPORTED_COUNTRIES_COUNT, ttl=CACHE_TIMEOUT)
def get_nominatim(country_code: str) -> pgeocode.Nominatim | None:
    """
    This initialised pandas and can be slow.
    """
    try:
        return pgeocode.Nominatim(country_code)
    except ValueError:
        return None


class PostcodeCityValidator:
    def __init__(self, postal_code: str, country_code: str, city: str):
        """
        Check if postal code, city, and country match.
        Strategy: first we try pgeocode which is offline and free.
        If it fails, we try google maps. If fails, we get alternative city names
        from geonames and compare them with google maps names.

        Cached on normalized postal code, country code and city for 24h.

        Tested with geoapify and geonames as a validator. Google maps are enough.

        Why not try alternative names with pgeocode? We checked on real data - it's
        faster this way, pgeocode is less reliable.
        """
        self.postal_code = postal_code.replace(' ', '').upper()
        self.country_code = self._normalize_country_code(country_code)
        self.city = self._normalize_city(city)

    @staticmethod
    def _normalize_city(city: str) -> str:
        """
        Convert accented letters to ASCII base + lower
        """
        return unidecode(city).strip().casefold()

    @staticmethod
    def _normalize_country_code(code: str) -> str:
        code = code.upper()
        return 'GB' if code == 'UK' else code

    @property
    def _cache_key(self) -> str:
        raw_key = f'{self.postal_code}_{self.country_code}_{self.city}'
        hashed_key = hashlib.md5(raw_key.encode(), usedforsecurity=False).hexdigest()
        class_name = self.__class__.__name__.lower()
        return f'{class_name}:{hashed_key}'

    def validate(self) -> bool:
        cached = cache.get(self._cache_key)
        if cached is not None:
            return cached

        # start with fast and offline
        if self._validate_via_pgeocode():
            cache.set(self._cache_key, True, timeout=CACHE_TIMEOUT)
            return True

        # then ask google
        if self._validate_via_google_maps():
            cache.set(self._cache_key, True, timeout=CACHE_TIMEOUT)
            return True

        cache.set(self._cache_key, False, timeout=CACHE_TIMEOUT)
        return False

    def _validate_via_pgeocode(self) -> bool:
        nominatim = get_nominatim(self.country_code)
        if not nominatim:
            return False

        result = nominatim.query_postal_code(self.postal_code)

        place_names = getattr(result, 'place_name', '')
        if not place_names or isinstance(place_names, float):
            # place_names can be NaN, which is a float
            return False

        # parse place names into candidates
        if isinstance(place_names, str):
            # for comma separated places
            candidates = place_names.split(',')
        else:
            candidates = [str(place_names)]

        return self.city in {self._normalize_city(city) for city in candidates}

    def _get_geoname_id(self) -> int | None:
        """Search for city and return its geoname ID"""
        data = self._safe_get_json(
            GEONAMES_SEARCH,
            params={
                'name': self.city,
                'country': self.country_code,
                'maxRows': 1,
                'featureClass': 'P',  # P = cities, villages, etc.
                'username': settings.GEONAMES_USER,
            },
        )
        if not data.get('geonames'):
            return None

        return data.get('geonames')[0]['geonameId']

    def _fetch_city_variants_from_geonames(self) -> set[str]:
        """Fetch possible city names in supported languages"""
        geoname_id = self._get_geoname_id()
        if not geoname_id:
            return set()

        city_data = self._safe_get_json(
            GEONAMES_GET,
            params={'geonameId': geoname_id, 'username': settings.GEONAMES_USER},
        )

        # get alternative names
        alternative_names = {self._normalize_city(city_data.get('name'))}
        for name_entry in city_data.get('alternateNames', []):
            if name_entry.get('lang') in LanguageEnum:
                alternative_names.add(self._normalize_city(name_entry.get('name')))

        return alternative_names

    def _get_city_names_from_google_maps(self) -> set[str]:
        data = self._safe_get_json(
            GEOCODE_URL,
            params={
                'address': self.postal_code,
                'components': f'country:{self.country_code}',
                'key': settings.GOOGLE_MAPS_API_KEY,
            },
        )

        names = set()
        relevant_types = {'locality', 'postal_town', 'administrative_area_level_1'}

        for result in data.get('results', []):
            for component in result.get('address_components', []):
                component_types = set(component['types'])
                if relevant_types & component_types:
                    names.add(self._normalize_city(component['long_name']))
                    names.add(self._normalize_city(component['short_name']))

        return names

    def _validate_via_google_maps(self) -> bool:
        if not settings.GOOGLE_MAPS_API_KEY:
            logger.error('PostcodeCityValidator: GOOGLE_MAPS_API_KEY is not set')
            return False

        names = self._get_city_names_from_google_maps()

        if self.city in names:
            return True

        return bool(names & self._fetch_city_variants_from_geonames())

    def _safe_get_json(self, url, **kwargs) -> dict:
        r = requests.get(url, timeout=API_TIMEOUT, **kwargs)
        try:
            r.raise_for_status()
        except requests.RequestException:
            logger.error('PostcodeCityValidator: Failed to get data from %s', url)
            return {}
        return r.json()
