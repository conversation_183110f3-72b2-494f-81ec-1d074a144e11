from datetime import (
    datetime,
    timedelta,
)

from django.core.cache import cache

import pandas as pd

from skus.internal_api.clients import LogisticShippingApiClient

from ..internal_api.clients import ShippersApiClient
from .serializers import (
    DeliveryOffsetSerialized,
    DeliveryOffsetSerializedSku,
)

DEFAULT_DELIVERY_OFFSET_WEEKS: tuple[int, int] = (3, 3)
COMPLAINT_DELIVERY_OFFSET_WEEKS: tuple[int, int] = (2, 2)
SKU_DEFAULT_DELIVERY_OFFSET_DAYS: tuple[int, int] = (2, 4)


class DeliveryOffsetHandler:
    country: str
    postal_code: str
    delivery_data: list[dict]

    def __init__(
        self, country: str, postal_code: str, is_complaint: bool = False
    ) -> None:
        self.postal_code = postal_code
        self.country = country
        self.is_complaint = is_complaint
        self.cache_key = 'delivery_regions_offsets'
        self.delivery_data = self.get_delivery_region_offset()
        self.region = self.get_delivery_region()

    def get_delivery_region_offset(self):
        region_offsets: list[dict] | None = cache.get(self.cache_key)

        if region_offsets:
            return region_offsets

        client = ShippersApiClient()
        delivery_data = client.get_delivery_region_info()

        if delivery_data:
            serializer = DeliveryOffsetSerialized(data=delivery_data, many=True)
            serializer.is_valid(raise_exception=True)
            region_offsets = serializer.data
        else:
            region_offsets = []
        cache.set(self.cache_key, region_offsets, timeout=60 * 60 * 24)

        return region_offsets

    def get_delivery_region(self) -> dict | None:
        for region in self.delivery_data:
            if region['country'] == self.country:
                if self.is_postal_code_excluded(
                    region['postal_codes_excluded'],
                    region['postal_codes_prefixes_excluded'],
                ):
                    continue

                if region['whole_country'] or self.is_postal_code_included(
                    region['postal_codes_prefixes']
                ):
                    return region
        return None

    def is_postal_code_excluded(
        self,
        postal_codes_excluded: list[str],
        postal_codes_prefixes_excluded: list[str],
    ) -> bool:
        if not self.postal_code:
            return False
        return self.postal_code in postal_codes_excluded or any(
            self.postal_code.startswith(prefix)
            for prefix in postal_codes_prefixes_excluded
        )

    def is_postal_code_included(self, postal_code_prefixes: list[str]) -> bool:
        if not self.postal_code:
            return False
        if self.country == 'united_kingdom':
            for prefix in postal_code_prefixes:
                if (
                    self.postal_code.startswith(prefix)
                    and len(self.postal_code) > len(prefix)
                    and self.postal_code[len(prefix)].isdigit()
                ):
                    return True
            return False
        return any(
            self.postal_code.startswith(prefix) for prefix in postal_code_prefixes
        )

    def get_offset(self) -> tuple[int, int]:
        if self.region:
            return (
                self.region['delivery_offset_min'],
                self.region['delivery_offset_max'],
            )
        if self.is_complaint:
            return COMPLAINT_DELIVERY_OFFSET_WEEKS

        return DEFAULT_DELIVERY_OFFSET_WEEKS

    def get_name(self) -> str:
        return self.region.get('name', '') if self.region else ''


class DeliveryOffsetHandlerSku:
    country: str

    def __init__(self, country: str, paid_at: datetime) -> None:
        self.country = country.upper()
        self.cache_key = 'delivery_regions_offsets_sku'
        self.delivery_data = self.get_delivery_region_offset()
        self.paid_at = paid_at

    def get_delivery_region_offset(self) -> list[dict]:
        region_offsets = cache.get(self.cache_key)

        if region_offsets is not None:
            return region_offsets
        client = LogisticShippingApiClient()
        delivery_data = client.get_shipping_configs()

        if delivery_data:
            serializer = DeliveryOffsetSerializedSku(data=delivery_data, many=True)
            serializer.is_valid(raise_exception=True)
            region_offsets = serializer.data
        else:
            region_offsets = []

        cache.set(self.cache_key, region_offsets, timeout=60 * 60 * 24)
        return region_offsets

    def _normalize_to_workday(self, sku_delivery_offset):
        start_date = pd.Timestamp(self.paid_at) + pd.tseries.offsets.BDay(
            sku_delivery_offset[0]
        )

        # If the start date falls on Friday or Saturday, move it to Sunday
        weekend_adjustment = {
            4: 2,  # Friday -> +2 days = Sunday
            5: 1,  # Saturday -> +1 day = Sunday
        }
        start_date += timedelta(days=weekend_adjustment.get(start_date.weekday(), 0))
        end_date = start_date + timedelta(
            days=sku_delivery_offset[1] - sku_delivery_offset[0]
        )
        return start_date.date().isoformat(), end_date.date().isoformat()

    def get_offset(self) -> tuple[str, str]:
        for region in self.delivery_data:
            if region.get('country', '').upper() == self.country:
                return self._normalize_to_workday(
                    [
                        region.get(
                            'transit_time_min', SKU_DEFAULT_DELIVERY_OFFSET_DAYS[0]
                        ),
                        region.get(
                            'transit_time_max', SKU_DEFAULT_DELIVERY_OFFSET_DAYS[1]
                        ),
                    ]
                )
        return self._normalize_to_workday(SKU_DEFAULT_DELIVERY_OFFSET_DAYS)
