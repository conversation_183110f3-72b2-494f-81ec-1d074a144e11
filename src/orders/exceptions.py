class InvalidOrderStatusChangeError(Exception):
    """Raise when someone change order status in unexpected way."""


class OrderItemAlreadyBatched(Exception):  # noqa: N818
    pass


class OrderAbortNotFoundInvoiceException(Exception):  # noqa: N818
    """Raise where not found invoice for"""

    message = 'Invoice for this order has not been found'


class WrongOrderToProductionException(Exception):  # noqa: N818
    """Base class for all Order data anomalies that block process_to_production"""


class OrderRegionPromoDiffThanSumOrderItemRegionPromo(WrongOrderToProductionException):
    """Order.region_promo_amount != OrderItem[].region_promo_value"""


class OrderRegionTotalDiffThanItems(WrongOrderToProductionException):
    """
    Order.region_total_price !=
    OrderItem[].region_price +
    OrderItem[].region_assembly_price +
    OrderItem[].region_delivery_price -
    OrderItem[].region_promo_value
    """


class OrderRegionPriceDiffThanPriceForEuroOrCHF(Exception):  # noqa: N818
    """
    When Euro, CHF

    Order.total_price != Order.region_total_price
    Order.total_price_net != Order.region_total_price_net
    Order.promo_amount != Order.region_promo_amount
    Order.promo_amount_net != Order.region_promo_amount_net

    OrderItem[].price != OrderItem[].region_price
    OrderItem[].price_net != OrderItem[].region_price_net
    OrderItem[].assembly_price != OrderItem[].region_assembly_price
    """
