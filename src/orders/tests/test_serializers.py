import pytest

from orders.serializers import GTMPurchaseEventCustomDataSerializer


@pytest.mark.django_db
def test_gtm_purchase_event_custom_data_serializer_with_additional_service(
    order_factory,
    order_item_factory,
):
    order = order_factory(items=[])
    order_item_factory(is_sofa_corduroy=True, order=order)
    order_item_factory(is_old_sofa_collection=True, order=order)

    serializer = GTMPurchaseEventCustomDataSerializer(order)
    data = serializer.data
    assert len(serializer.data['contents']) == 2
    assert 'sofa' in data['contents'][0]['variant']
    assert 'Service: Old Sofa Collection' in data['contents'][1]['variant']
