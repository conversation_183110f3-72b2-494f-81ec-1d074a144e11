import dataclasses
import operator

from collections import defaultdict
from typing import NamedTuple

from django.db.models import QuerySet

from custom.enums import (
    ColorEnum,
    ShelfType,
)
from producers.choices import BatchType
from producers.enums import Manufacturers
from producers.models import (
    BatchingSettings,
    Product,
)


class BatchingSettingTuple(NamedTuple):
    separate_desks: bool
    separate_s_plus: bool
    separate_extended: bool
    min_size: int
    max_size: int
    max_area: float

    def __str__(self) -> str:
        return (
            f'Separate: Desks: {self.separate_desks}, '
            f'S+: {self.separate_s_plus}, '
            f'Extended: {self.separate_extended}, '
            f'Min Size: {self.min_size}, '
            f'Max Size: {self.max_size}, '
            f'Max Area: {self.max_area}'
        )


class ProductGroupKey(NamedTuple):
    is_s_plus: bool
    shelf_type: ShelfType
    batch_type: BatchType
    color: ColorEnum
    settings: BatchingSettingTuple
    is_desk: bool
    is_priority: bool

    def __str__(self) -> str:
        text = 'S+, ' if self.is_s_plus else ''
        text += f'{self.shelf_type.name}, {self.color.name}, {self.batch_type.name}'
        if self.is_desk:
            text += ', Desk'
        if self.is_priority:
            text += ', Priority'
        return text

    def is_standard_equivalent_to(self, other: 'ProductGroupKey') -> bool:
        return (
            self.batch_type == BatchType.STANDARD
            and self.shelf_type == other.shelf_type
            and self.color == other.color
            and self.is_s_plus == other.is_s_plus
            and self.is_desk == other.is_desk
            and self.is_priority == other.is_priority
        )


@dataclasses.dataclass
class BatchInfo:
    product_group_key: ProductGroupKey
    area: float
    products: list[Product]

    @property
    def size(self) -> int:
        return len(self.products)

    @property
    def is_too_small(self):
        return self.size < self.product_group_key.settings.min_size

    @property
    def is_extended(self):
        return self.product_group_key.batch_type == BatchType.EXTENDED

    @property
    def is_standard(self):
        return self.product_group_key.batch_type == BatchType.STANDARD

    def is_standard_equivalent_to(self, other: 'BatchInfo') -> bool:
        return self.product_group_key.is_standard_equivalent_to(other.product_group_key)

    @property
    def ordering(self) -> tuple:
        return (
            self.product_group_key.shelf_type,
            self.product_group_key.color,
            self.product_group_key.is_s_plus,
            self.product_group_key.batch_type,
        )


class BatchSettingsTupleCache:
    def __init__(self, manufactor_id: int):
        self.manufactor_id = manufactor_id
        self.cache = {}

    def get(self, shelf_type: ShelfType) -> BatchingSettingTuple:
        key = shelf_type
        if key not in self.cache:
            settings = BatchingSettings.objects.get_or_return_default(
                self.manufactor_id, shelf_type
            )
            self.cache[shelf_type] = batching_settings_to_tuple(settings)
        return self.cache[key]


def get_batches(
    product_ids: list[int],
    manufactor_id: int,
    separate_priorities: list[int],
    separate_source_priorities: list[int],
) -> list[BatchInfo]:
    all_products = Product.objects.ready_for_batching(product_ids)
    grouped_products = group_products(
        all_products,
        manufactor_id,
        separate_priorities,
        separate_source_priorities,
    )
    batches = []
    for product_group_key, products in grouped_products.items():
        batches.extend(split_into_batches(products, product_group_key))
    batches.sort(key=operator.attrgetter('ordering'), reverse=True)
    move_products_to_extended_form_standard_for_drewtur(manufactor_id, batches)
    return batches


def move_products_to_extended_form_standard_for_drewtur(
    manufactor_id: int, batches: list[BatchInfo]
) -> None:
    if manufactor_id != Manufacturers.DREWTUR:
        return None
    small_extended_batches = [
        batch for batch in batches if batch.is_extended and batch.is_too_small
    ]
    for small_batch in small_extended_batches:
        standard_batches = [
            batch for batch in batches if batch.is_standard_equivalent_to(small_batch)
        ]
        standard_to_small = [batch for batch in standard_batches if batch.is_too_small]
        for standard_batch in standard_to_small:
            was_merged = merge_batches(small_batch, standard_batch)
            if was_merged:
                batches.remove(standard_batch)

        for standard_batch in standard_batches:
            if not small_batch.is_too_small:
                break
            shift_products_between_batches(small_batch, standard_batch)


def shift_products_between_batches(small_batch: BatchInfo, standard_batch: BatchInfo):
    needed_amount = small_batch.product_group_key.settings.min_size - small_batch.size
    amount_to_shift = min(
        needed_amount,
        standard_batch.size - small_batch.product_group_key.settings.min_size,
    )
    if amount_to_shift <= 0:
        return
    small_batch.products.extend(standard_batch.products[-amount_to_shift:])
    standard_batch.products = standard_batch.products[:-amount_to_shift]


def merge_batches(small_batch: BatchInfo, standard_batch: BatchInfo) -> bool:
    max_amount_to_add = (
        small_batch.product_group_key.settings.max_size - small_batch.size
    )
    if standard_batch.size < max_amount_to_add:
        small_batch.products.extend(standard_batch.products)
        return True
    return False


def group_products(
    products: QuerySet[Product],
    manufactor_id: int,
    separate_priorities: list,
    separate_source_priorities: list,
) -> dict[ProductGroupKey, list[Product]]:
    grouped_products = defaultdict(list)
    settings_cache = BatchSettingsTupleCache(manufactor_id)
    for product in products:
        settings = settings_cache.get(product.shelf_type_option)
        is_s_plus = bool(settings.separate_s_plus and product.has_plus_feature)
        is_desk = bool(settings.separate_desks and product.is_desk)
        batch_type = get_product_main_feature(
            product,
            is_desk=is_desk,
            separate_extended=settings.separate_extended,
        )
        is_priority = (
            product.priority in separate_priorities
            or product.source_priority in separate_source_priorities
        )
        key = ProductGroupKey(
            batch_type=batch_type,
            color=product.color_option,
            shelf_type=product.shelf_type_option,
            settings=settings,
            is_s_plus=is_s_plus,
            is_desk=is_desk,
            is_priority=is_priority,
        )
        grouped_products[key].append(product)
    return grouped_products


def get_product_main_feature(
    product: Product,
    is_desk: bool,
    separate_extended: bool,
) -> BatchType:
    if is_desk:
        return BatchType.STANDARD
    features = product.order_item.order_item.get_item_features(product.order.order_type)
    if separate_extended and 'extended' in features:
        return BatchType.EXTENDED
    return BatchType.STANDARD


def split_into_batches(
    products: list[Product],
    product_group_key: ProductGroupKey,
) -> list[BatchInfo]:
    batches = []
    last_batch = []
    current_area = 0
    for product in products:
        current_area += product.cached_area
        if (
            len(last_batch) >= product_group_key.settings.max_size
            or current_area > product_group_key.settings.max_area
        ):
            batches.append(last_batch)
            last_batch = []
            current_area = product.cached_area
        last_batch.append(product)

    if batches and len(last_batch) < product_group_key.settings.min_size:
        extend_small_batch(batches, last_batch, product_group_key.settings.min_size)
    batches.append(last_batch)
    return [get_batch_info(batch, product_group_key) for batch in batches]


def extend_small_batch(
    batches: list[list[Product]], last_batch: list[Product], min_batch_size: int
):
    last_full_batch = batches[-1]
    if len(last_full_batch) <= min_batch_size:
        return
    amount_to_shift = min_batch_size - len(last_batch)
    last_batch.extend(last_full_batch[-amount_to_shift:])
    batches[-1] = last_full_batch[:-amount_to_shift]


def get_batch_info(
    batch: list[Product],
    product_group_key: ProductGroupKey,
) -> BatchInfo:
    return BatchInfo(
        products=batch,
        product_group_key=product_group_key,
        area=sum(product.cached_area for product in batch),
    )


def batching_settings_to_tuple(
    settings: BatchingSettings,
) -> BatchingSettingTuple:
    return BatchingSettingTuple(
        separate_desks=settings.separate_desks,
        separate_s_plus=settings.separate_s_plus,
        separate_extended=settings.separate_extended,
        min_size=settings.min_size,
        max_size=settings.max_size,
        max_area=settings.max_area,
    )
