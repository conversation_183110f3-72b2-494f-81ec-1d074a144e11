import operator
import typing

from copy import deepcopy
from itertools import chain

from custom.enums import Sofa01Color

if typing.TYPE_CHECKING:
    from orders.models import Order
    from producers.gh.moduly_glowne.ivy_elements import Pack
    from producers.models import (
        Product,
        ProductBatch,
    )


class CoverPackagingService:
    MAX_PACKAGES_IN_BOX = 4
    MIN_PACKAGES_IN_BOX = 2
    BOX_DIM_X = 550
    BOX_DIM_Y = 430
    BOX_DIM_Z = 200

    def __init__(self, products: list['Product']):
        self.current_pack_id = 1
        self.products = sorted(products, key=operator.attrgetter('id'))

    @classmethod
    def get_for_batch(cls, batch: 'ProductBatch') -> dict[int, list[int]]:
        """
        Returns simplified packaging info: {pack_id: [product_ids]}.
        Used to send packaging info to PS when creating packaging labels.
        """
        covers = [product for product in batch.batch_items.all() if product.cover_only]
        cover_packaging_service = cls(covers)
        return cover_packaging_service.get_simplified_pack_info()

    def get_simplified_pack_info(self) -> dict[int, list[int]]:
        return {pack.pack_id: pack.product_ids for pack in self.get_packaging()}

    @classmethod
    def get_for_order(cls, order: 'Order') -> list['Pack']:
        """
        Returns full packaging with all pack details.
        Used for ProductInfoSerializer.
        """
        covers = [product for product in order.product_set.all() if product.cover_only]
        cover_packaging_service = cls(covers)
        return cover_packaging_service.get_packaging()

    def get_packaging(self):
        """Leather covers are packed separately, other covers are packed in boxes."""
        packs = []
        leather_products, other_products = self._separate_leather_covers()

        leather_packs = self._extract_packaging(leather_products)
        packs.extend(self._pack_leather_covers(leather_packs))

        other_packs = self._extract_packaging(other_products)
        packs.extend(self._pack_other_covers(other_packs))
        return packs

    def _separate_leather_covers(self) -> tuple[list['Product'], list['Product']]:
        leather_colors = Sofa01Color.get_leather_colors()

        leather_covers = []
        other_covers = []
        for cover in self.products:
            if cover.color_option in leather_colors:
                leather_covers.append(cover)
            else:
                other_covers.append(cover)
        return leather_covers, other_covers

    @staticmethod
    def _extract_packaging(cover_products: list['Product']) -> list['Pack']:
        return list(
            chain.from_iterable(
                [product.get_jetty_serialized().packaging for product in cover_products]
            )
        )

    def _pack_leather_covers(self, covers_packs: list['Pack']) -> list['Pack']:
        leather_packs = []
        for leather_pack in covers_packs:
            leather_pack.pack_id = self.current_pack_id
            leather_pack.product_ids = [leather_pack.product.id]
            self.current_pack_id += 1
            leather_packs.append(leather_pack)
        return leather_packs

    def _pack_other_covers(self, covers_packs: list['Pack']) -> list['Pack']:
        unwrap_packages = covers_packs
        wrap_packages = []
        while len(unwrap_packages) >= self.MIN_PACKAGES_IN_BOX:
            packages_in_box = self.MAX_PACKAGES_IN_BOX
            if (
                0
                < len(unwrap_packages) % self.MAX_PACKAGES_IN_BOX
                < self.MIN_PACKAGES_IN_BOX
            ):
                # if there are 5 covers, we need to pack them in two boxes 2 + 3
                packages_in_box = self.MIN_PACKAGES_IN_BOX
            pack = self.pack_to_box(unwrap_packages[:packages_in_box])
            pack.pack_id = self.current_pack_id
            self.current_pack_id += 1
            wrap_packages.append(pack)
            unwrap_packages = unwrap_packages[packages_in_box:]
        if unwrap_packages:
            pack = self.pack_to_foliage(unwrap_packages[0])
            pack.pack_id = self.current_pack_id
            wrap_packages.append(pack)

        return wrap_packages

    def pack_to_box(self, packages: list['Pack']) -> 'Pack':
        box = deepcopy(packages[0])
        box.is_group_pack = True
        box.dim_x = self.BOX_DIM_X
        box.dim_y = self.BOX_DIM_Y
        box.dim_z = self.BOX_DIM_Z
        box.weight = 0
        box.adjusted_weight = 0
        for package in packages:
            box.product_ids.append(package.product.id)
            box.weight += package.weight
            box.adjusted_weight += package.adjusted_weight
            box.all_elements.extend(package.all_elements)
        return box

    @staticmethod
    def pack_to_foliage(package: 'Pack') -> 'Pack':
        package.product_ids = [package.product.id]
        package.is_group_pack = True
        return package
