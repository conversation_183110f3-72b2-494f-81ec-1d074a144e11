import types

from unittest.mock import (
    PropertyMock,
    patch,
)

import pytest

from custom.enums import Sofa01Color
from producers.gh.moduly_glowne.ivy_elements import Pack
from producers.services.cover_packaging import CoverPackagingService


class MockPack(Pack):
    def __init__(self, product):
        self.dim_x = 200
        self.dim_y = 200
        self.dim_z = 50
        self.pack_id = 1
        self.product = product
        self.product_ids = [product.id]
        self.all_elements = []
        self.weight = 1.5
        self.adjusted_weight = 2


class MockProduct:
    def __init__(self, product, color_option):
        self.id = product.id
        self.color_option = color_option

    def get_jetty_serialized(self):
        # Return a dummy object with a .packaging attribute
        return types.SimpleNamespace(packaging=[MockPack(self)])


@pytest.mark.django_db
@pytest.mark.parametrize(
    'color',
    [Sofa01Color.LEATHER_COGNAC, Sofa01Color.CORDUROY_ECRU],
)
@patch('producers.models.Product.color_option', new_callable=PropertyMock)
def test_single_cover_packed_individually(color_option_mock, color, product):
    color_option_mock.return_value = color
    service = CoverPackagingService([MockProduct(product, color_option=color)])
    packages = service.get_packaging()
    assert len(packages) == 1


@pytest.mark.django_db
@pytest.mark.parametrize(
    'product_count, expected_boxes',  # noqa: PT006
    [(2, 1), (4, 1), (5, 2), (8, 2), (9, 3)],
)
def test_wool_covers_packed_in_boxes(product_factory, product_count, expected_boxes):
    wool_products = [
        MockProduct(product, color_option=Sofa01Color.CORDUROY_ECRU)
        for product in product_factory.create_batch(product_count)
    ]

    service = CoverPackagingService(wool_products)
    packages = service.get_packaging()
    assert len(packages) == expected_boxes
    assert all(package.is_group_pack for package in packages)


@pytest.mark.parametrize(
    ('leather_count', 'wool_count', 'expected_total'),
    [
        (1, 1, 2),
        (2, 4, 3),
        (2, 5, 4),
    ],
)
def test_wool_and_leather_covers_separated(
    product_factory, leather_count, wool_count, expected_total
):
    leather_products = [
        MockProduct(product, color_option=Sofa01Color.LEATHER_COGNAC)
        for product in product_factory.create_batch(leather_count)
    ]
    wool_products = [
        MockProduct(product, color_option=Sofa01Color.CORDUROY_ECRU)
        for product in product_factory.create_batch(wool_count)
    ]
    service = CoverPackagingService(leather_products + wool_products)
    packages = service.get_packaging()
    assert len(packages) == expected_total
