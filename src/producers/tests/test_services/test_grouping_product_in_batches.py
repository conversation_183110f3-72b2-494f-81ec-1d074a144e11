import pytest

from custom.enums import (
    ShelfType,
    Type01Color,
)
from producers.choices import (
    BatchType,
    ProductPriority,
    ProductStatus,
)
from producers.enums import Manufacturers
from producers.models import Product
from producers.services.group_products_in_batches import get_batches


@pytest.fixture
def extended_products_5(product_factory):
    return product_factory.create_batch(
        order_item__order_item__material=Type01Color.WHITE,
        order_item__order_item__shelf_type=ShelfType.TYPE01,
        order_item__order_item__width=2500,
        status=ProductStatus.ASSIGNED_TO_PRODUCTION,
        cached_area=5,
        size=5,
    )


@pytest.fixture
def custom_and_extended_products_5(product_factory):
    return product_factory.create_batch(
        order_item__order_item__material=Type01Color.WHITE,
        order_item__order_item__shelf_type=ShelfType.TYPE01,
        order_item__order_item__width=250,
        order_item__order_item__depth=250,
        cached_area=5,
        status=ProductStatus.ASSIGNED_TO_PRODUCTION,
        size=5,
    )


@pytest.fixture
def standard_products_5(product_factory):
    return product_factory.create_batch(
        order_item__order_item__material=Type01Color.WHITE,
        order_item__order_item__shelf_type=ShelfType.TYPE01,
        order_item__order_item__width=200,
        order_item__order_item__depth=240,
        cached_area=5,
        status=ProductStatus.ASSIGNED_TO_PRODUCTION,
        size=5,
    )


@pytest.fixture
def standard_products_10(product_factory):
    return product_factory.create_batch(
        order_item__order_item__material=Type01Color.WHITE,
        order_item__order_item__shelf_type=ShelfType.TYPE01,
        order_item__order_item__width=200,
        order_item__order_item__depth=240,
        cached_area=5,
        status=ProductStatus.ASSIGNED_TO_PRODUCTION,
        size=10,
    )


@pytest.mark.django_db
def test_grouping_product_by_feature_in_batches(
    extended_products_5,
    custom_and_extended_products_5,
    standard_products_5,
    manufactor,
):
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches) == 2  # all products from fixtures
    standard_batches = [
        batch
        for batch in batches
        if batch.product_group_key.batch_type == BatchType.STANDARD
    ]
    assert len(standard_batches) == 1
    extended_batches = [
        batch
        for batch in batches
        if batch.product_group_key.batch_type == BatchType.EXTENDED
    ]
    assert len(extended_batches) == 1


@pytest.mark.django_db
def test_grouping_product_by_feature_in_batches_extended_with_standard(
    batching_settings_factory,
    extended_products_5,
    standard_products_5,
    manufactor,
):
    batching_settings_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        separate_extended=False,
    )
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches) == 1  # all products from fixtures
    standard_batches = [
        batch
        for batch in batches
        if batch.product_group_key.batch_type == BatchType.STANDARD
    ]
    assert len(standard_batches) == 1
    extended_batches = [
        batch
        for batch in batches
        if batch.product_group_key.batch_type == BatchType.EXTENDED
    ]
    assert len(extended_batches) == 0


@pytest.mark.django_db
def test_grouping_product_by_color_in_batches(extended_products_5, manufactor):
    product = Product.objects.first()
    product.order_item.order_item.material = Type01Color.BLUE
    product.order_item.order_item.save()
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches) == 2


@pytest.mark.django_db
def test_grouping_product_by_shelf_type_in_batches(
    custom_and_extended_products_5, manufactor
):
    product = Product.objects.first()
    product.order_item.order_item.shelf_type = ShelfType.TYPE02
    product.order_item.order_item.save()
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches) == 2


@pytest.mark.django_db
def test_grouping_product_with_max_size(
    extended_products_5, manufactor, batching_settings_factory
):
    batching_settings_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        max_size=4,
    )
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches) == 2


@pytest.mark.django_db
def test_grouping_product_with_min_size(
    extended_products_5, manufactor, batching_settings_factory
):
    batching_settings_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        max_size=4,
        min_size=2,
    )
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches[1].products) == 2
    assert len(batches[0].products) == 3


@pytest.mark.django_db
def test_moving_products_from_standard_to_extended_for_drewtur(
    extended_products_5,
    standard_products_10,
    manufactor_factory,
    batching_settings_factory,
):
    manufacturer = manufactor_factory(
        id=Manufacturers.DREWTUR,
        name='Drewtur',
    )
    batching_settings_factory(
        manufactor=manufacturer,
        shelf_type=ShelfType.TYPE01,
        separate_extended=True,
        max_size=100,
        min_size=7,
    )
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufacturer.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches) == 2
    extended = next((batch for batch in batches if batch.is_extended))
    standard = next((batch for batch in batches if batch.is_standard))

    assert len(extended.products) == 7
    assert len(standard.products) == 8


@pytest.mark.django_db
def test_merging_products_standard_to_extended_for_drewtur(
    extended_products_5,
    standard_products_5,
    manufactor_factory,
    batching_settings_factory,
):
    manufacturer = manufactor_factory(
        id=Manufacturers.DREWTUR,
        name='Drewtur',
    )
    batching_settings_factory(
        manufactor=manufacturer,
        shelf_type=ShelfType.TYPE01,
        separate_extended=True,
        max_size=20,
        min_size=7,
    )
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufacturer.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches) == 1
    extended = next((batch for batch in batches if batch.is_extended))

    assert len(extended.products) == 10


@pytest.mark.django_db
def test_grouping_product_with_max_area(
    extended_products_5, manufactor, batching_settings_factory
):
    batching_settings_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        max_area=15,
    )
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert len(batches[0].products) == 3
    assert len(batches[1].products) == 2


@pytest.mark.django_db
def test_grouping_product_separate_desk_and_s_plus(
    extended_products_5, manufactor, batching_settings_factory
):
    batching_settings_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        separate_desks=True,
        separate_s_plus=True,
    )
    product = Product.objects.first()
    product.is_desk = True
    product.save()
    product = Product.objects.last()
    product.has_plus_feature = True
    product.save()
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[],
        separate_source_priorities=[],
    )
    assert {
        len(batches[0].products),
        len(batches[1].products),
        len(batches[2].products),
    } == {1, 3}


def test_grouping_chosen_priorities(
    extended_products_5,
    manufactor,
    batching_settings_factory,
):
    batching_settings_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        separate_desks=True,
        separate_s_plus=True,
    )
    product = Product.objects.first()
    product.priority = ProductPriority.BIG_ORDERS.value
    product.save()
    batches = get_batches(
        product_ids=Product.objects.values_list('id', flat=True),
        manufactor_id=manufactor.id,
        separate_priorities=[ProductPriority.BIG_ORDERS.value],
        separate_source_priorities=[],
    )
    assert {
        len(batches[0].products),
        len(batches[1].products),
    } == {1, 4}
