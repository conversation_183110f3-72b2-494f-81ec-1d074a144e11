import logging

from datetime import datetime
from urllib.parse import urljoin

from django.conf import settings
from django.contrib.admin.utils import flatten
from django.db import transaction
from django.db.models import (
    Count,
    Q,
)
from django.urls import reverse
from django.utils import timezone
from rest_framework import serializers

from cstm_be.media_storage import private_media_storage
from custom.enums import LanguageEnum
from custom.enums.colors import Sofa01Color
from custom.secure_ids import generate_secure_token
from orders.models import Order
from producers.choices import (
    BatchNeededActions,
    ProductPriority,
    ProductStatus,
)
from producers.models import (
    Manufactor,
    ManufacturerReleasedPackage,
    Product,
    ProductBatch,
    QualityHoldReleaseRequest,
)
from producers.utils import send_quality_release_emails
from production_margins.choices import MeasurementUnit
from production_margins.models import (
    CustomPricingFactor,
    CustomPricingFactorChangeRequest,
    CustomPricingFactorChangeRequestAttachment,
    CustomPricingFactorChangeRequestComment,
    ElementsOrder,
)

logger = logging.getLogger('producers')


class ElementOrderProducerViewSerializer(serializers.ModelSerializer):
    production_progress = serializers.SerializerMethodField()
    batches = serializers.PrimaryKeyRelatedField(
        queryset=ProductBatch.objects.all(),
        many=True,
    )
    batches_type = serializers.SerializerMethodField()
    delayed_items = serializers.SerializerMethodField()
    file_alert = serializers.SerializerMethodField()
    production_files_statuses = serializers.SerializerMethodField()
    packaging_files_statuses = serializers.SerializerMethodField()
    cnc_files_statuses = serializers.SerializerMethodField()

    @classmethod
    def get_file_alert(cls, obj):
        return {
            batch.id: BatchNeededActions(batch.actions_needed).name
            for batch in obj.batches.all()
        }

    @classmethod
    def get_production_progress(cls, obj):
        products_count = obj.batches.aggregate(
            completed_products=Count(
                'batch_items',
                filter=Q(batch_items__status__gte=ProductStatus.SENT_TO_CUSTOMER),
            ),
            all_products=Count(
                'batch_items', filter=~Q(batch_items__status=ProductStatus.ABORTED)
            ),
        )
        return {
            'completed': products_count['completed_products'],
            'all': products_count['all_products'],
        }

    @classmethod
    def get_batches_type(cls, obj):
        return ', '.join(
            {
                batch.get_batch_type()
                for batch in obj.batches.all()
                if batch.get_batch_type()
            }
        )

    @classmethod
    def get_delayed_items(cls, obj):
        return flatten([batch.get_delayed_items() for batch in obj.batches.all()])

    @classmethod
    def get_production_files_statuses(cls, obj):
        return {batch.production_files_status for batch in obj.batches.all()}

    @classmethod
    def get_packaging_files_statuses(cls, obj):
        return {batch.packaging_files_status for batch in obj.batches.all()}

    @classmethod
    def get_cnc_files_statuses(cls, obj):
        return {batch.cnc_files_status for batch in obj.batches.all()}

    class Meta:
        model = ElementsOrder
        fields = (
            'id',
            'file_alert',
            'production_progress',
            'batches',
            'batches_type',
            'generated_at',
            'delayed_items',
            'order_file',
            'production_files_statuses',
            'packaging_files_statuses',
            'cnc_files_statuses',
            'file_status',
        )


class ReleasedPackageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ManufacturerReleasedPackage
        fields = (
            'package',
            'product',
            'released_at',
        )

    def create(self, validated_data):
        user = self.context['request'].user
        manufacturer = Manufactor.objects.get(owner=user)
        validated_data['manufacturer'] = manufacturer
        instance = super().create(validated_data)
        return instance


class ProductWithPackageDashDelimitedField(serializers.Field):
    EXPECTED_FORMAT_1 = 'ShelfType-ProductID-PackageNumber-TotalPackages'
    EXPECTED_FORMAT_2 = 'ProductID-PackageNumber'
    EXPECTED_SPLIT_PARTS_1 = 4
    EXPECTED_SPLIT_PARTS_2 = 2

    def to_internal_value(self, data):
        product_with_package = data.split('-')
        if len(product_with_package) not in {
            self.EXPECTED_SPLIT_PARTS_1,
            self.EXPECTED_SPLIT_PARTS_2,
        }:
            raise serializers.ValidationError(
                f'Invalid format. Expected format: '
                f'{self.EXPECTED_FORMAT_1} or {self.EXPECTED_FORMAT_2}'
            )
        if len(product_with_package) == self.EXPECTED_SPLIT_PARTS_1:
            _, product_id, package_number, _ = product_with_package
        else:
            product_id, package_number = product_with_package

        try:
            product = Product.objects.get(pk=int(product_id))
        except Product.DoesNotExist:
            raise serializers.ValidationError('Wrong Product ID')

        try:
            return {
                'product': product,
                'package': int(package_number),
                'released_at': timezone.now(),
            }
        except ValueError:
            raise serializers.ValidationError('Invalid format for product data')


class ReleasedProductWithPackageSerializer(serializers.ModelSerializer):
    product_with_package = ProductWithPackageDashDelimitedField(required=True)

    class Meta:
        model = ManufacturerReleasedPackage
        fields = ('product_with_package',)

    def create(self, validated_data):
        user = self.context['request'].user
        product_with_package = validated_data['product_with_package']

        (
            released_package,
            created,
        ) = ManufacturerReleasedPackage.objects.update_or_create(
            manufacturer=Manufactor.objects.get(owner=user),
            product=product_with_package['product'],
            package=product_with_package['package'],
            defaults={'released_at': product_with_package['released_at']},
        )
        if created:
            product_status_updater = released_package.product.status_updater
            if product_status_updater.can_change_status_to_be_shipped():
                product_status_updater.change_status_to_be_shipped_after_release()

        return released_package


class CustomPricingFactorChangeRequestCommentSerializer(serializers.ModelSerializer):
    is_manufacturer_comment = serializers.SerializerMethodField()

    class Meta:
        model = CustomPricingFactorChangeRequestComment
        fields = ['text', 'is_manufacturer_comment']

    def get_is_manufacturer_comment(self, obj):
        user = self.context['request'].user
        return user == obj.author


class CustomPricingFactorChangeRequestCreateCommentSerializer(
    serializers.ModelSerializer
):
    class Meta:
        model = CustomPricingFactorChangeRequestComment
        fields = ['text', 'author', 'custom_pricing_factor_change_request']


class CustomPricingFactorChangeRequestSerializer(serializers.ModelSerializer):
    comments = CustomPricingFactorChangeRequestCommentSerializer(
        many=True, read_only=True, source='custompricingfactorchangerequestcomment_set'
    )
    created_at = serializers.DateTimeField(format='%d.%m.%Y %H:%M:%S')
    updated_at = serializers.DateTimeField(format='%d.%m.%Y %H:%M:%S')

    class Meta:
        model = CustomPricingFactorChangeRequest
        fields = [
            'id',
            'manufacturer_code',
            'price',
            'date_from',
            'status',
            'created_at',
            'updated_at',
            'request_number',
            'comments',
        ]


class CustomPricingFactorListSerializer(serializers.ModelSerializer):
    manufacturer_code = serializers.SerializerMethodField()
    codename = serializers.SerializerMethodField()
    unit = serializers.SerializerMethodField()
    requests = CustomPricingFactorChangeRequestSerializer(
        many=True, read_only=True, source='custompricingfactorchangerequest_set'
    )
    available_codenames = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()

    class Meta:
        model = CustomPricingFactor
        fields = (
            'id',
            'price',
            'loss_factor',
            'date_from',
            'date_to',
            'manufacturer_code',
            'unit',
            'codename',
            'requests',
            'available_codenames',
            'name',
        )

    @staticmethod
    def get_manufacturer_code(obj):
        if obj.manufacturer_code:
            return obj.manufacturer_code.code
        return '-'

    @staticmethod
    def get_codename(obj):
        if obj.pricing_factor_item:
            return obj.pricing_factor_item.codename
        return '-'

    @staticmethod
    def get_unit(obj):
        return MeasurementUnit(
            obj.pricing_factor_item.measurement_unit
        ).to_polish_admin_display_only()

    @staticmethod
    def get_available_codenames(obj):
        return [
            {'code': code, 'name': name}
            for code, name in obj.pricing_factor_item.manufacturercode_set.values_list(
                'code', 'name'
            )
        ]

    @staticmethod
    def get_name(obj):
        return obj.manufacturer_code.name if obj.manufacturer_code else ''


class CustomPricingFactorChangeRequestAttachmentSerializer(serializers.ModelSerializer):
    file = serializers.FileField(allow_null=True, required=False)

    class Meta:
        model = CustomPricingFactorChangeRequestAttachment
        fields = ['file']


class CustomPricingFactorSingleRequestSerializer(serializers.ModelSerializer):
    custom_pricing_factor_id = serializers.IntegerField()
    comment = serializers.CharField(allow_blank=True, required=False)

    class Meta:
        model = CustomPricingFactorChangeRequest
        fields = [
            'manufacturer_code',
            'price',
            'date_from',
            'comment',
            'custom_pricing_factor_id',
            'status',
        ]


class CustomPricingFactorChangeRequestsSerializer(serializers.Serializer):
    attachment = CustomPricingFactorChangeRequestAttachmentSerializer()

    class Meta:
        fields = ['attachment']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._requests_data = []

    def to_internal_value(self, data):
        self._requests_data = data.get('requests', [])  # Capture the requests data
        return super().to_internal_value(data)

    @transaction.atomic
    def create(self, validated_data):
        attachment_data = validated_data.pop('attachment', None)

        user = self.context['request'].user
        created_attachment = (
            self.create_attachment(attachment_data) if attachment_data else None
        )
        created_requests = []

        for request_data in self._requests_data:
            comment_text = request_data.pop('comment', None)
            request_serializer = CustomPricingFactorSingleRequestSerializer(
                data=request_data
            )
            request_serializer.is_valid(raise_exception=True)
            request_number = (
                CustomPricingFactorChangeRequest.objects.generate_request_number(user)
            )
            custom_pricing_factor_change_request = request_serializer.save(
                attachment=created_attachment,
                request_number=request_number,
            )
            if comment_text:
                self.create_comment(
                    comment_text, custom_pricing_factor_change_request, user
                )

            created_requests.append(custom_pricing_factor_change_request)

        created_requests_data = CustomPricingFactorSingleRequestSerializer(
            created_requests, many=True
        ).data
        created_attachment_data = (
            CustomPricingFactorChangeRequestAttachmentSerializer(
                created_attachment
            ).data
            if created_attachment
            else None
        )

        return {
            'requests': created_requests_data,
            'attachment': created_attachment_data,
        }

    def update(self, instance, validated_data):
        attachment_data = validated_data.pop('attachment', None)
        created_attachment = None
        if attachment_data and attachment_data['file']:
            created_attachment = self.create_attachment(attachment_data)

        user = self.context['request'].user
        for request_data in self._requests_data:
            request_id = request_data.get('id')
            if request_id:
                request_instance = CustomPricingFactorChangeRequest.objects.get(
                    id=request_id
                )
                if created_attachment:
                    request_instance.attachment = created_attachment
                    request_instance.save()
                request_serializer = CustomPricingFactorSingleRequestSerializer(
                    request_instance, data=request_data, partial=True
                )
                request_serializer.is_valid(raise_exception=True)
                custom_pricing_factor_change_request = request_serializer.save()
                comment_text = request_data.get('comment')
                if comment_text:
                    self.create_comment(
                        comment_text, custom_pricing_factor_change_request, user
                    )

        return instance

    @staticmethod
    def create_attachment(attachment_data):
        current_date = datetime.now()
        file_path = (
            f'production_margins/custom_pricing_factor_change_request/'
            f'{current_date.year}/{current_date.month}/'
        )

        attachment_file = attachment_data.pop('file')
        file_to_save = private_media_storage.save(
            file_path + attachment_file.name, attachment_file
        )

        return CustomPricingFactorChangeRequestAttachment.objects.create(
            file=file_to_save
        )

    @staticmethod
    def create_comment(comment_text, custom_pricing_factor_change_request, user):
        comment_data = {
            'text': comment_text,
            'custom_pricing_factor_change_request': (
                custom_pricing_factor_change_request.id
            ),
            'author': user.id,
        }
        comment_serializer = CustomPricingFactorChangeRequestCreateCommentSerializer(
            data=comment_data
        )
        if comment_serializer.is_valid(raise_exception=True):
            comment_serializer.save()


class ProductReleaseSerializer(serializers.Serializer):
    product = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.all(),
    )


class OrderAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = [
            'id',
            'country',
            'country_area',
            'first_name',
            'last_name',
            'postal_code',
            'street_address_1',
            'street_address_2',
            'company_name',
            'floor_number',
            'order_notes',
            'hard_parking',  # remove after gala confirmation
            'above_3rd_floor',  # remove after gala confirmation
            'no_elevator',
            'city',
            'phone',
            'phone_prefix',
            'email',
        ]


class ProductInfoSerializer(serializers.ModelSerializer):
    material_info = serializers.SerializerMethodField()
    order_info = OrderAddressSerializer(source='order', read_only=True)
    front_view = serializers.SerializerMethodField()
    labels_packaging = serializers.SerializerMethodField()
    labels_elements = serializers.SerializerMethodField()
    instruction = serializers.SerializerMethodField()
    elements_order = serializers.SerializerMethodField()
    is_prioritized = serializers.SerializerMethodField()
    manual_language = serializers.SerializerMethodField()
    is_complaint = serializers.BooleanField(read_only=True)
    original_product = serializers.IntegerField(source='copy_of_id', read_only=True)
    is_manufactor_fault = serializers.BooleanField(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id',
            'order_info',
            'material_info',
            'batch',
            'front_view',
            'labels_packaging',
            'labels_elements',
            'instruction',
            'is_prioritized',
            'elements_order',
            'gala_status',
            'manual_language',
            'cover_only',
            'is_complaint',
            'original_product',
            'is_manufactor_fault',
        ]

    @classmethod
    def get_manual_language(cls, obj: Product) -> str:
        supported_languages = {LanguageEnum.EN, LanguageEnum.DE}
        if obj.order and obj.order.owner and obj.order.owner.profile:
            language = obj.order.owner.profile.language
            if language in supported_languages:
                return language.upper()
        return LanguageEnum.EN.upper()

    @classmethod
    def get_is_prioritized(cls, obj):
        priority = ProductPriority(obj.priority)
        return priority.is_prioritized()

    @classmethod
    def get_material_info(cls, obj):
        serialization = obj.get_serialized_product_info()
        group_packaging = obj.order.get_group_packaging()
        elements = serialization.get('item', {}).get('elements', [])
        serialized_elements = []
        is_complaint = obj.is_complaint()
        for element in elements:
            material_usage = element.get('material_usage', {})
            if not material_usage or (
                is_complaint and not element.get('complaint', False)
            ):
                continue
            # filter out material_fabric - it's sum of material used in covers
            material_usage = [
                {code_name: usage}
                for code_name, usage in material_usage.items()
                if not code_name.startswith('material_fabric')
            ]
            group_pack_id = next(
                (
                    pack.pack_id
                    for pack in group_packaging
                    if obj.id in pack.product_ids
                ),
                None,
            )
            serialized_elements.append(
                {
                    'name': cls.get_element_name(element, is_complaint, obj.cover_only),
                    'material_usage': material_usage,
                    'pack_id': cls.get_pack_id(element),
                    'color': cls.get_color_name(element),
                    'position': element.get('position_index', 0),
                    'fabric_direction': cls.get_fabric_direction(element),
                    'is_fireproof': cls.is_element_fireproof(material_usage),
                    'group_pack_id': group_pack_id,
                }
            )
        if is_complaint:
            complaint = obj.serialization_updater._complaint_serialization_kwargs
            if complaint.get('complaint_fittings', False):
                elements = [
                    {element['codename']: element['count']}
                    for element in complaint.get('complaint_fittings', [])
                ]
                serialized_elements.append(
                    {
                        'name': 'ADDITIONAL_PARTS',
                        'material_usage': elements,
                        'pack_id': -1,
                        'color': '',
                        'position': 0,
                        'fabric_direction': '',
                        'is_fireproof': False,
                        'group_pack_id': None,
                    }
                )
        return serialized_elements

    @classmethod
    def is_element_fireproof(cls, material_usage: list[dict[str, float]]) -> bool:
        for materials in material_usage:
            for material_name in materials.keys():
                if 'fireproof' in material_name.lower():
                    return True
        return False

    @classmethod
    def get_fabric_direction(cls, element) -> str:
        return element.get('fabric_direction', '')

    @classmethod
    def get_color_name(cls, element):
        material = element.get('material')
        color = Sofa01Color(material).name
        return color

    @classmethod
    def get_element_name(cls, element: dict, is_complaint: bool, cover_only: bool):
        name = element.get('id', element.get('surname', '-'))
        if cover_only or is_complaint:
            name += '_parts'
        return name

    @classmethod
    def get_pack_id(cls, element: dict) -> int:
        package_info = element.get('package_info', {})
        return package_info.get('pack_id') if package_info else -1

    @classmethod
    def get_front_view(cls, obj):
        if obj.details and obj.details.front_view:
            relative_url = reverse(
                'product-front-view',
                kwargs={'token': generate_secure_token(obj.id)},
            )
            return urljoin(settings.SITE_URL, relative_url)

    @classmethod
    def get_labels_elements(cls, obj):
        if (
            obj.cover_only
            and obj.batch
            and obj.batch.details
            and obj.batch.details.labels_elements
        ):
            relative_url = reverse(
                'product-batch-labels-elements',
                kwargs={'token': generate_secure_token(obj.batch.id)},
            )
            return urljoin(settings.SITE_URL, relative_url)

    @classmethod
    def get_labels_packaging(cls, obj):
        if (
            obj.cover_only
            and obj.batch
            and obj.batch.details
            and obj.batch.details.labels_packaging
        ):
            relative_url = reverse(
                'product-batch-labels-packaging',
                kwargs={'token': generate_secure_token(obj.batch.id)},
            )
            return urljoin(settings.SITE_URL, relative_url)

        if obj.details and obj.details.labels_packaging:
            relative_url = reverse(
                'product-labels-packaging',
                kwargs={'token': generate_secure_token(obj.id)},
            )
            return urljoin(settings.SITE_URL, relative_url)

    @classmethod
    def get_instruction(cls, obj: Product) -> str | None:
        if obj.details and obj.details.instruction:
            relative_url = reverse(
                'product-instruction',
                kwargs={'token': generate_secure_token(obj.id)},
            )
            return urljoin(settings.SITE_URL, relative_url)

    @classmethod
    def get_elements_order(cls, obj) -> int | None:
        if obj.batch and obj.batch.elementsorders:
            return obj.batch.elementsorders[0].id


class GalaProductStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = ['gala_status']


class QualityHoldReleaseRequestSerializer(serializers.Serializer):
    products = serializers.ListField(
        child=serializers.IntegerField(), required=True, allow_empty=False
    )
    status = serializers.ChoiceField(
        choices=[ProductStatus.QUALITY_CONTROL, ProductStatus.QUALITY_BLOCKER],
        required=True,
    )

    def validate_products(self, product_ids: list[int]) -> list[int]:
        batch_id = self.context.get('batch_id')
        if not batch_id:
            raise serializers.ValidationError('Batch ID is required')

        products = Product.objects.filter(id__in=product_ids, batch_id=batch_id)

        if len(products) != len(product_ids):
            raise serializers.ValidationError(
                'Some products do not exist or do not belong to the specified batch'
            )

        return product_ids

    def validate(self, data):
        batch_id = self.context.get('batch_id')
        batch = ProductBatch.objects.get(id=batch_id)
        if QualityHoldReleaseRequest.objects.pending_quality_hold_requests(
            batch=batch
        ).exists():
            raise serializers.ValidationError(
                'There is already a pending release request for this batch'
            )

        products = Product.objects.filter(id__in=data['products'], batch_id=batch_id)

        invalid_status_products = products.exclude(status=data['status'])
        if invalid_status_products.exists():
            raise serializers.ValidationError(
                f"Some products are not in {ProductStatus(data['status']).label} status"
            )

        return data

    def create(self, validated_data):
        batch_id = self.context.get('batch_id')
        batch = ProductBatch.objects.get(id=batch_id)

        release_request = QualityHoldReleaseRequest.objects.create(
            batch=batch, status=validated_data['status']
        )

        products = Product.objects.filter(id__in=validated_data['products'])
        products.update(quality_hold_release_request=release_request)
        product_ids = list(products.values_list('id', flat=True))

        send_quality_release_emails(
            product_status=validated_data['status'],
            product_ids=product_ids,
            batch_id=batch.id,
        )

        return release_request
