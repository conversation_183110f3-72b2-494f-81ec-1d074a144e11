import calendar
import logging
import operator

from datetime import (
    date,
    timedelta,
)
from itertools import groupby

from django.contrib import admin
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from django.utils import timezone

from complaints.admin_filters import (
    MultipleChoiceProductionComplaintReproductionElementsFilter,
)
from custom.enums.colors import Sofa01Color
from custom.filters import BaseMultipleChoiceListFilter
from gallery.enums import She<PERSON><PERSON><PERSON><PERSON>E<PERSON>
from gallery.models import (
    Jetty,
    Sotty,
    Watty,
)
from orders.enums import OrderType
from producers.choices import (
    DeliveryPriority,
    ProductPriority,
    ProductStatus,
    SourcePriority,
)
from producers.models import (
    Product,
    ProductStatusHistory,
)
from producers.models_split.product_details import ProductDetailsJetty

logger = logging.getLogger('producers')


def get_current_week():
    today = date.today()
    start = today - timedelta(days=today.weekday())
    end = start + timedelta(days=6)
    return f'{start}_{end}'


class ToBeShippedFilter(admin.SimpleListFilter):
    title = 'date to be shipped'
    parameter_name = 'to_be_shipped_range'

    def lookups(self, request, model_admin):
        return ((get_current_week(), 'Current Week'),)

    def queryset(self, request, queryset):
        if self.value() is not None:
            quality_control_product_ids = ProductStatusHistory.objects.filter(
                status=ProductStatus.QUALITY_CONTROL,
                created_at__range=self.value().split('_'),
            ).values_list('product_id', flat=True)
            quality_control_products = queryset.filter(
                id__in=quality_control_product_ids
            )

            previous_quality_control_product_ids = ProductStatusHistory.objects.filter(
                status=ProductStatus.QUALITY_CONTROL,
                created_at__lt=self.value().split('_')[0],
            ).values_list('product_id', flat=True)

            product_ids = ProductStatusHistory.objects.filter(
                status__in=[ProductStatus.TO_BE_SHIPPED, ProductStatus.ABORTED_DONE],
                created_at__range=self.value().split('_'),
            ).values_list('product_id', flat=True)

            products = queryset.filter(
                id__in=set(product_ids) - set(previous_quality_control_product_ids)
            )

            return quality_control_products | products


class AcceptedToProductionFilter(admin.SimpleListFilter):
    title = 'date accepted to production'
    parameter_name = 'accepted_to_production_range'

    def lookups(self, request, model_admin):
        return ((get_current_week(), 'Current Week'),)

    def queryset(self, request, queryset):
        if self.value() is not None:
            ids = ProductStatusHistory.objects.filter(
                status=ProductStatus.NEW,
                created_at__range=self.value().split('_'),
            ).values_list('product_id', flat=True)
            return queryset.filter(id__in=ids)


class CreatedAtFilter(admin.SimpleListFilter):
    title = 'Created At'
    parameter_name = 'created_at'

    def lookups(self, request, model_admin):
        return (
            ('this_year', 'This Year'),
            ('last_year', 'Last Year'),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        if self.value() == 'this_year':
            return queryset.filter(created_at__year=timezone.now().year)
        if self.value() == 'last_year':
            return queryset.filter(created_at__year=timezone.now().year - 1)


class WithoutProductionFilesFilter(admin.SimpleListFilter):
    title = 'Production Files'
    parameter_name = 'production_files'

    def lookups(self, request, model_admin):
        return (('withoutinstruction', 'without instruction'),)

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if self.value() == 'withoutinstruction':
            ids = ProductDetailsJetty.objects.filter(instruction='')
            ids = ids.values_list('product_id', flat=True)
            return queryset.filter(id__in=ids)


class WithoutDeliveryDateFilter(admin.SimpleListFilter):
    title = 'Delivery date'
    parameter_name = 'deliverydate'

    def lookups(self, request, model_admin):
        return (('withoutdeliverydate', 'without deliverydate'),)

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if self.value() == 'withoutdeliverydate':
            return queryset.filter(delivered_at__isnull=True)


class EstimatedDeliveryDateFilter(admin.SimpleListFilter):
    title = 'Estimated delivery date'
    parameter_name = 'estimated_deliverydate'

    def advance_one_month(self, original_date):
        new_month = original_date.month + 1
        new_year = original_date.year

        if new_month > 12:
            new_year += 1
            new_month -= 12

        last_day_of_month = calendar.monthrange(new_year, new_month)[1]
        new_day = min(original_date.day, last_day_of_month)

        return original_date.replace(year=new_year, month=new_month, day=new_day)

    def lookups(self, request, model_admin):
        return [
            ('current', 'Current month'),
            ('next', 'Next month'),
        ]

    def queryset(self, request, queryset):
        if self.value() is not None:
            lookup_date = timezone.now()

            if self.value() == 'next':
                lookup_date = self.advance_one_month(lookup_date)

            return queryset.filter(
                order__estimated_delivery_time__year=lookup_date.year,
                order__estimated_delivery_time__month=lookup_date.month,
            )
        return queryset


class ProductStatusFilter(admin.SimpleListFilter):
    title = 'Product Status'
    parameter_name = 'product_mateo_status_filter'

    def lookups(self, request, model_admin):
        return ProductStatus.statuses_for_admin_filter()

    def queryset(self, request, queryset):
        if self.value():
            value = [int(self.value())]
            if value == [ProductStatus.ASSIGNED_TO_PRODUCTION.value]:
                value.append(ProductStatus.IN_PRODUCTION.value)
            return queryset.filter(status__in=value)


class ProductDescriptionFilter(BaseMultipleChoiceListFilter):
    title = 'Extra description'
    parameter_name = 'extradescription'
    template = 'admin/custom_filters.html'

    FILTERS = {
        'with_assembly': Q(order__assembly=True),
        'doors': Q(has_doors=True),
        'double_doors': Q(  # TODO - add to cached features
            product_details_jetty__cached_serialization__item__elements__contains=[
                {'handle': 1}
            ],
        )
        | Q(
            product_details_watty__cached_serialization__item__elements__contains=[
                {'handle': 1}
            ],
        ),
        'extended': Q(is_extended=True),
        'sidebohr': Q(is_sidebohr=True),
        # TODO: standard depths differ per shelf type (see: ShelfType.standard_depths)
        'custom': Q(
            Q(
                Q(
                    Q(cached_product_type__exact='watty')
                    & ~Q(cached_depth__in=Watty.STANDARD_DEPTHS)
                )
                | Q(
                    Q(cached_product_type__exact='jetty')
                    & ~Q(cached_depth__in=Jetty.STANDARD_DEPTHS)
                )
            )
            | ~Q(order__order_type=OrderType.CUSTOMER)
        ),
        'drawers': Q(has_drawers=True),
        'desk': Q(is_desk=True),
        'storage': Q(cached_has_top_or_bottom_storage=True) & Q(is_desk=False),
        'errors': Q(has_errors__exact=True),
        'no_errors': ~Q(has_errors__exact=True),
    }

    def lookups(self, request, model_admin):
        return (
            ('custom', 'custom'),
            ('doors', 'doors'),
            ('double_doors', 'double doors [*Slow]'),
            ('extended', 'extended'),
            ('sidebohr', 'sidebohr'),
            ('drawers', 'drawers'),
            ('desk', 'desk'),
            ('storage', 'storage'),
            ('errors', 'errors'),
            ('no_errors', 'no errors'),
            ('with_assembly', 'with paid assembly'),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        filters = Q()
        for applied_filter in self.value_as_list():
            filters &= self.FILTERS.get(applied_filter, Q())
        return queryset.filter(filters)


class DnaStyleFilter(admin.SimpleListFilter):
    title = 'Dna Style'
    parameter_name = 'dna_style'

    def lookups(self, request, model_admin):
        return ShelfPatternEnum.choices

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(cached_dna_style=self.value())
        return queryset


class ProductBatchingFilter(admin.SimpleListFilter):
    title = 'Extra description'
    parameter_name = 'batchingfilter'

    PRODUCT_STATUS_IN_PROGRESS = [
        ProductStatus.NEW,
        ProductStatus.ASSIGNED_TO_PRODUCTION,
    ]
    CACHED_TYPE = 'jetty'

    def lookups(self, request, model_admin):
        return (
            ('standard', 'standard+standard with doors'),
            ('nonstandard', 'whole rest'),
        )

    def queryset(self, request, queryset):
        probably_product = (
            Product.objects.filter(
                cached_product_type=self.CACHED_TYPE,
                status__in=self.PRODUCT_STATUS_IN_PROGRESS,
            )
            .prefetch_related(
                'order_item',
                'order_item__order_item',
            )
            .select_related(
                'order',
            )
        )
        if self.value() in [
            'standard',
            'nonstandard',
        ]:
            filtered_items = []
            standard = self.value() == 'standard'
            self._get_filtered_products(
                filtered_items,
                probably_product,
                standard=standard,
            )
            return queryset.filter(id__in=[product.id for product in filtered_items])
        return queryset

    def _get_filtered_products(self, filtered_items, probably_product, standard=True):
        for item in probably_product:
            features = item.order_item.order_item.get_item_features(
                item.order.order_type,
            )
            features_needed = (
                'extended' not in features
                and 'custom' not in features
                and 'drawers' not in features
            )
            if standard and features_needed:
                filtered_items.append(item)
            elif not standard and not features_needed:
                filtered_items.append(item)


class ShelfTypeFilter(admin.SimpleListFilter):
    title = 'Shelf Type'
    parameter_name = 'shelftypefilter'
    query_filter = None

    cached_shelf_type = {
        'type1': 'T1',
        'type2': 'T2',
        'veneer1': 'F1',
        'type3': 'W3',
        'type13': 'W13',
        'veneer13': 'F13',
        'type23': 'T23',
        'type24': 'T24',
        'type25': 'T25',
        'sofa1': 'S1',
    }

    def lookups(self, request, model_admin):
        return (
            ('type1', 'Type01'),
            ('type2', 'Type02'),
            ('veneer1', 'Veneer Type01'),
            ('type3', 'Type03'),
            ('type13', 'Type13'),
            ('veneer13', 'Veneer Type13'),
            ('type23', 'Type23'),
            ('type24', 'Type24'),
            ('type25', 'Type25'),
            ('sofa1', 'Sofa Type01'),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        cached_shelf_type = self.cached_shelf_type.get(self.value())
        if cached_shelf_type:
            query = {self.query_filter: cached_shelf_type}
            return queryset.filter(**query)
        return queryset


class ShelfTypeFilterProduct(ShelfTypeFilter):
    query_filter = 'cached_shelf_type'


class ShelfTypeFilterProductBatch(ShelfTypeFilter):
    query_filter = 'batch_items__cached_shelf_type'


class ComplaintShelfTypeFilter(ShelfTypeFilter):
    def queryset(self, request, queryset):
        cached_shelf_type = self.cached_shelf_type.get(self.value())
        if cached_shelf_type:
            return queryset.filter(product__cached_shelf_type=cached_shelf_type)
        return queryset


class ReproductionTimeFilterProductBatch(admin.SimpleListFilter):
    title = 'Reproduction time in days'
    parameter_name = 'reproduction_time_in_days'
    query_filter = 'batch_items__reproduction_time_in_days'

    lookups_tuple = (
        ('1', '1'),
        ('3', '3'),
        ('5', '5'),
        ('10', '10'),
    )

    def lookups(self, request, model_admin):
        return self.lookups_tuple

    def queryset(self, request, queryset):
        reproduction_time = {val[0]: val[1] for val in self.lookups_tuple}.get(
            self.value()
        )
        if reproduction_time:
            query = {self.query_filter: reproduction_time}
            return queryset.filter(**query)
        return queryset


class MultipleChoiceListCustomProductInProductionFilter(BaseMultipleChoiceListFilter):
    title = 'Filters'
    parameter_name = 'filters'
    template = 'admin/custom_filters.html'
    filter_lookups = [
        ('depth_240', 'Depth 240'),
        ('depth_320', 'Depth 320'),
        ('depth_400', 'Depth 400'),
        ('depth_500', 'Depth 500'),
        ('depth_600', 'Depth 600'),
        ('depth_420', 'Depth 420 (wardrobe)'),
        ('depth_630', 'Depth 630 (wardrobe)'),
        # NOTE: sofa depths don't work for corners and chaise longues
        ('depth_1000', 'Depth 1000 (sofa)'),
        ('depth_1125', 'Depth 1125 (sofa)'),
        ('depth_other', 'Depth Custom'),
        ('t1_0', 'T1 Color white'),
        ('t1_1', 'T1 Color black'),
        ('t1_4', 'T1 Color aubergine'),
        ('t1_5', 'T1 Color natural'),
        ('t1_3', 'T1 Color grey'),
        ('t1_6', 'T1 Color red'),
        ('t1_7', 'T1 Color yellow'),
        ('t1_8', 'T1 Color dusty pink'),
        ('t1_9', 'T1 Color blue'),
        ('t1_10', 'T1 Color dark brown'),
        ('t1_11', 'T1 Color green agava'),
        ('t2_0', 'T2 Color snow white'),
        ('t2_1', 'T2 Color terracotta'),
        ('t2_2', 'T2 Color midnight blue'),
        ('t2_3', 'T2 Color sand'),
        ('t2_4', 'T2 Color mint'),
        ('t2_6', 'T2 Color mat black'),
        ('t2_7', 'T2 Color blue'),
        ('t2_8', 'T2 Color burgundy'),
        ('t2_9', 'T2 Color cotton'),
        ('t2_10', 'T2 Color gray'),
        ('t2_11', 'T2 Color gray + dark gray'),
        ('t2_12', 'T2 Color sand + mustard yellow'),
        ('t2_15', "T2 Color Reisinger's pink"),
        ('t2_16', 'T2 Color sage green'),
        ('t2_17', 'T2 Color stone gray'),
        ('t2_18', 'T2 Color stone gray + walnut'),
        ('t2_19', 'T2 Color black'),
        ('f1_0', 'F1 Veneer ash'),
        ('f1_1', 'F1 Veneer oak'),
        ('f1_2', 'F1 Veneer dark oak'),
        ('w3_0', 'W3 white'),
        ('w3_1', 'W3 beige'),
        ('w3_2', 'W3 graphite'),
        ('w3_3', 'W3 beige pink'),
        ('w3_4', 'W3 white pink'),
        ('w3_5', 'W3 graphite white'),
        ('w3_6', 'W3 graphite pink'),
        ('w3_7', 'W3 graphite beige'),
        ('w3_8', 'W3 white beige'),
        ('w3_9', 'W3 white graphite'),
        ('w3_10', 'W3 beige graphite'),
        ('w3_11', 'W3 beige white'),
        ('w3_12', 'W3 graphite stone gray'),
        ('w3_13', 'W3 graphite sage green'),
        ('w3_14', 'W3 graphite misty blue'),
        ('w3_15', 'W3 white stone gray'),
        ('w3_16', 'W3 white sage green'),
        ('w3_17', 'W3 white misty blue'),
        ('w3_18', 'W3 cashmere stone gray'),
        ('w3_19', 'W3 cashmere sage green'),
        ('w3_20', 'W3 cashmere misty blue'),
        ('w13_0', 'W13 white'),
        ('w13_1', 'W13 sand + midnight blue'),
        ('w13_2', 'W13 sand + mustard yellow'),
        ('w13_3', 'W13 gray'),
        ('w13_4', 'W13 gray + dark gray'),
        ('w13_5', 'W13 white plywood'),
        ('w13_6', 'W13 gray plywood'),
        ('w13_7', 'W13 black plywood'),
        ('w13_8', 'W13 clay brown'),
        ('w13_9', 'W13 olive green'),
        ('w13_10', 'W13 beige'),
        ('w13_11', 'W13 black'),
        ('w13_12', 'W13 denim blue'),
        ('w13_13', 'W13 stone gray'),
        ('w13_14', 'W13 indigo blue'),
        ('f13_0', 'F13 light wood'),
        ('f13_1', 'F13 dark wood'),
        ('t23_0', 'T23 white'),
        ('t23_1', 'T23 beige'),
        ('t23_2', 'T23 pistachio green'),
        ('t23_3', 'T23 black'),
        ('t23_4', 'T23 powder pink'),
        ('t24_0', 'T24 white'),
        ('t24_1', 'T24 beige'),
        ('t24_2', 'T24 pistachio green'),
        ('t24_3', 'T24 black'),
        ('t24_4', 'T24 powder pink'),
        ('t25_0', 'T25 white'),
        ('t25_1', 'T25 beige'),
        ('t25_2', 'T25 pistachio green'),
        ('t25_3', 'T25 black'),
        ('t25_4', 'T25 powder pink'),
        ('s1_0', 'S01 rewool2 brown'),
        ('s1_1', 'S01 rewool2 olive green'),
        ('s1_2', 'S01 rewool2 light gray'),
        ('s1_3', 'S01 rewool2 butter yellow'),
        ('s1_4', 'S01 rewool2 shadow pink'),
        ('s1_5', 'S01 rewool2 green'),
        ('s1_6', 'S01 rewool2 baby_blue'),
        ('s1_7', 'S01 corduroy ecru'),
        ('s1_8', 'S01 corduroy rock'),
        ('s1_9', 'S01 corduroy dark_brown'),
        ('s1_10', 'S01 corduroy steel'),
        ('s1_11', 'S01 corduroy tobacco'),
        ('s1_12', 'S01 corduroy pink'),
        ('s1_13', 'S01 corduroy camouflage'),
        ('s1_14', 'S01 corduroy blue klein'),
        ('s1_15', 'S01 leather cognac'),
        ('s1_16', 'S01 leather anthracite'),
        ('s1_17', 'S01 leather oat beige'),
        ('s1_18', 'S01 leather willow green'),
    ]

    def lookups(self, request, model_admin):
        return self.filter_lookups

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        return queryset.filter(*self._get_filter_query_values())

    def _get_filter_query_values(self):
        filter_query = Q(
            order_item__content_type__in=(
                ContentType.objects.get_for_model(Jetty),
                ContentType.objects.get_for_model(Watty),
                ContentType.objects.get_for_model(Sotty),
            ),
        )
        _inner_query = Q()
        filter_values = sorted(value.rsplit('_', 1) for value in self.value_as_list())
        for field_slug, query_values in groupby(
            filter_values,
            operator.itemgetter(0),
        ):
            values = set(value for _, value in query_values)  # noqa: C401
            if field_slug == 'depth':
                should_include_other = 'other' in values
                values.discard('other')
                values = {int(value) for value in values}
                depths = {240, 320, 400, 500, 420, 600, 1000, 1125}
                if should_include_other and depths - values:
                    filter_query &= ~Q(
                        cached_depth__in=(depths - values),
                    )
                else:
                    filter_query &= Q(cached_depth__in=values)
            elif field_slug.startswith('s'):
                for value in values:
                    _inner_query |= Q(
                        cached_shelf_type=field_slug.upper(),
                        cached_materials__contains=[int(value)],
                    )
            else:
                _inner_query |= Q(
                    cached_shelf_type=field_slug.upper(),
                    cached_material__in={int(value) for value in values},
                )
        return [filter_query & _inner_query]


class MultipleChoiceListCustomProductBatch(
    MultipleChoiceListCustomProductInProductionFilter,
):
    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        product_queryset = super().queryset(request, Product.objects.all())
        batch_ids = product_queryset.values_list('batch_id', flat=True)
        return queryset.filter(Q(id__in=batch_ids))


class MultipleChoicePriorityFilter(BaseMultipleChoiceListFilter):
    title = 'Priority'
    parameter_name = 'priority'
    template = 'admin/custom_filters.html'

    def lookups(self, request, model_admin):
        priorities = [
            (val, name if name else 'Normal') for val, name in ProductPriority.choices
        ]
        priorities.insert(
            1,
            (
                ProductPriority.on_hold_switch_label(),
                ProductPriority.on_hold_switch_label(),
            ),
        )
        return priorities

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        values = self.value().split(',')
        query = Q()
        if ProductPriority.on_hold_switch_label() in values:
            values.remove(ProductPriority.on_hold_switch_label())
            query |= Q(
                priority=ProductPriority.ON_HOLD, order_item__deleted__isnull=False
            )
        if str(ProductPriority.ON_HOLD) in values:
            values.remove(str(ProductPriority.ON_HOLD))
            query |= Q(
                priority=ProductPriority.ON_HOLD, order_item__deleted__isnull=True
            )

        if values:
            query |= Q(priority__in=values)
        queryset = queryset.filter(query)
        return queryset


class MultipleChoiceSourcePriorityFilter(BaseMultipleChoiceListFilter):
    title = 'Source priority'
    parameter_name = 'source_priority'
    template = 'admin/custom_filters.html'

    def lookups(self, request, model_admin):
        priorities = [
            (val, name if name else 'Normal') for val, name in SourcePriority.choices
        ]
        return priorities

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        values = self.value().split(',')
        return queryset.filter(source_priority__in=values)


class MultipleChoiceDeliveryPriorityFilter(BaseMultipleChoiceListFilter):
    title = 'Delivery priority'
    parameter_name = 'delivery_priority'
    template = 'admin/custom_filters.html'

    def lookups(self, request, model_admin):
        priorities = [
            (val, name if name else 'Normal') for val, name in DeliveryPriority.choices
        ]
        return priorities

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        values = self.value().split(',')
        return queryset.filter(delivery_priority__in=values)


class MultipleChoiceProductComplaintReproductionElementsFilter(
    MultipleChoiceProductionComplaintReproductionElementsFilter,
):
    query_parameter = (
        'reproduction_complaints__reproduction_element_categories__contains'
    )


class MultipleColoursComplaintsFilterMixin:
    title = 'By colours'
    parameter_name = 'colours'

    filter_lookups = [
        ('t1_0', 'T1 Color white'),
        ('t1_1', 'T1 Color black'),
        ('t1_4', 'T1 Color aubergine'),
        ('t1_5', 'T1 Color natural'),
        ('t1_3', 'T1 Color grey'),
        ('t1_6', 'T1 Color red'),
        ('t1_7', 'T1 Color yellow'),
        ('t1_8', 'T1 Color dusty pink'),
        ('t2_0', 'T2 Color snow white'),
        ('t2_1', 'T2 Color terracotta'),
        ('t2_2', 'T2 Color midnight blue'),
        ('t2_3', 'T2 Color sand'),
        ('t2_4', 'T2 Color mint'),
        ('t2_6', 'T2 Color mat black'),
        ('t2_7', 'T2 Color blue'),
        ('t2_8', 'T2 Color burgundy'),
        ('t2_9', 'T2 Color cotton'),
        ('f1_0', 'F1 Veneer ash'),
        ('f1_1', 'F1 Veneer oak'),
        ('w3_0', 'W3 white'),
        ('w3_1', 'W3 beige'),
        ('w3_2', 'W3 graphite'),
        ('w3_3', 'W3 beige pink'),
        ('w3_4', 'W3 white pink'),
        ('w3_5', 'W3 graphite white'),
        ('w3_6', 'W3 graphite pink'),
        ('w3_7', 'W3 graphite beige'),
        ('w3_8', 'W3 white beige'),
        ('w3_9', 'W3 white graphite'),
        ('w3_10', 'W3 beige graphite'),
        ('w3_11', 'W3 beige white'),
    ]


class MultipleColoursProductBatchComplaintFilter(
    MultipleColoursComplaintsFilterMixin,
    MultipleChoiceListCustomProductBatch,
):
    pass


class ManufactorFaultFilter(admin.SimpleListFilter):
    title = 'Manufactor fault'
    parameter_name = 'man_fault'

    def lookups(self, request, model_admin):
        return (
            (True, 'Yes'),
            (False, 'No'),
        )


class ProductComplaintManufactorFaultFilter(ManufactorFaultFilter):
    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(reproduction_complaints__manufactor_fault=self.value())


class ProductBatchComplaintManufactorFaultFilter(ManufactorFaultFilter):
    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(
            batch_items__reproduction_complaints__manufactor_fault=self.value()
        )


class ProductQualityComplaintAssemblyServiceFilter(admin.SimpleListFilter):
    title = 'Complaint Assembly Service'
    parameter_name = 'complaint_assembly_service'

    def lookups(self, request, model_admin):
        return (
            (True, 'Yes'),
            (False, 'No'),
        )

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(complaint__assembly_team_intervention=self.value())


class ProductQualityHasComplaintFilter(admin.SimpleListFilter):
    title = 'Has complaint'
    parameter_name = 'has_complaint'

    def lookups(self, request, model_admin):
        return (
            (True, 'Yes'),
            (False, 'No'),
        )

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(complaint__isnull=not self.value())


class ProductQualityHasFreeReturnFilter(admin.SimpleListFilter):
    title = 'Has free return'
    parameter_name = 'has_free_return'

    def lookups(self, request, model_admin):
        return (
            (True, 'Yes'),
            (False, 'No'),
        )

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(order_item__free_return__isnull=not self.value())


class ProductQualityReviewScoreFilter(admin.SimpleListFilter):
    title = 'Review Score'
    parameter_name = 'q_review_score'

    def lookups(self, request, model_admin):
        return (
            ('1', '1'),
            ('2', '2'),
            ('3', '3'),
            ('4', '4'),
            ('5', '5'),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(order__review__score=self.value())
        return queryset


class ProductFeaturesFilter(BaseMultipleChoiceListFilter):
    title = 'Other Features'
    parameter_name = 'features'
    template = 'admin/custom_filters.html'

    FILTERS = {
        'grommet': Q(
            product_details_jetty__cached_serialization__item__elements_amount__Bg__gt=(
                0
            ),
        ),
        'insert': Q(
            product_details_jetty__cached_serialization__item__elements_amount__Ih__gt=(
                0
            ),
        )
        | Q(
            product_details_jetty__cached_serialization__item__elements_amount__Iv__gt=(
                0
            ),
        ),
        'long_legs': Q(
            product_details_jetty__cached_serialization__item__elements_amount__Ll__gt=(
                0
            ),
        ),
        'plinth': Q(has_plinth__exact=True),
        'support': Q(
            product_details_jetty__cached_serialization__item__elements_amount__S__gt=0,
        ),
        'plinth_standard': Q(
            **{
                '__'.join(
                    [
                        'product_details_jetty',
                        'cached_serialization',
                        'materials',
                        'services_data',
                        'has_key',
                    ]
                ): 'service_T1_DTR_element-plinth-beam',
            }
        ),
        'plinth_short': Q(
            **{
                '__'.join(
                    [
                        'product_details_jetty',
                        'cached_serialization',
                        'materials',
                        'services_data',
                        'has_key',
                    ]
                ): 'service_T1_DTR_element-plinth-short-beam',
            }
        ),
        'left_door_with_handle': Q(
            has_left_door_with_handle=True,
        ),
        'standard': Q(
            is_extended__exact=False,
            has_drawers__exact=False,
            has_doors__exact=False,
        ),
        'double_lamello': Q(
            has_double_lamello=True,
        ),
        'slab_top_short': Q(
            product_details_watty__cached_serialization__item__elements_amount__St__gt=(
                0
            ),
        ),
        'back_top_b': Q(has_back_top_b=True),
        'slab_push': Q(
            product_details_watty__cached_serialization__item__elements_amount__Sp__gt=(
                0
            ),
        ),
        'wall_ee': Q(has_wall_ee=True),
        't13_plywood': Q(cached_shelf_type='W13', cached_material__in=[5, 6, 7]),
        't13_chipboard': Q(
            cached_shelf_type='W13',
            cached_material__in=[0, 1, 2, 3, 4],
        ),
        'lighting': Q(has_lighting=True),
    }

    def lookups(self, request, model_admin):
        return (
            ('grommet', 'grommet'),
            ('insert', 'insert(horizontal or vertical)'),
            ('long_legs', 'long legs'),
            ('plinth', 'plinth'),
            ('support', 'support'),
            ('plinth_standard', 'plinth standard'),
            ('plinth_short', 'plinth short'),
            ('left_door_with_handle', 'left door with handle'),
            ('standard', 'standard (without drawers, extended, doors)'),
            ('double_lamello', 'double lamello'),
            ('slab_top_short', 'slab top short'),
            ('back_top_b', 'back top b'),
            ('slab_push', 'slab push'),
            ('wall_ee', 'wall ee'),
            ('t13_plywood', 'T13 plywood'),
            ('t13_chipboard', 'T13 chipboard'),
            ('lighting', 'lighting'),
        )

    def queryset(self, request, queryset):
        filters = Q()
        for applied_filter in self.value_as_list():
            filters &= self.FILTERS.get(applied_filter, Q())
        return queryset.filter(filters)


class W3DrawerTypeFilter(BaseMultipleChoiceListFilter):
    title = 'W3 Drawer Type'
    parameter_name = 'w3_drawer'
    template = 'admin/custom_filters.html'

    FILTERS = {
        'interior_drawer': Q(
            product_details_watty__cached_serialization__item__elements_amount__Ti__gt=(
                0
            ),
        ),
        'exterior_drawer': Q(
            product_details_watty__cached_serialization__item__elements_amount__Te__gt=(
                0
            ),
        ),
        'interior_bottom_drawer': Q(
            product_details_watty__cached_serialization__item__elements_amount__Tb__gt=(
                0
            ),
        ),
    }

    def lookups(self, request, model_admin):
        return (
            ('interior_drawer', 'interior drawer'),
            ('exterior_drawer', 'exterior drawer'),
            ('interior_bottom_drawer', 'interior bottom drawer'),
        )

    def queryset(self, request, queryset):
        filters = Q()
        for applied_filter in self.value_as_list():
            filters &= self.FILTERS.get(applied_filter, Q())
        return queryset.filter(filters)


class VerticalHeightsFilter(BaseMultipleChoiceListFilter):
    title = 'Vertical Heights'
    parameter_name = 'vertical_heights'
    template = 'admin/custom_filters.html'

    FILTERS = {
        'A': Q(
            vertical_heights__contains='A',
        ),
        'B': Q(
            vertical_heights__contains='B',
        ),
        'C': Q(
            vertical_heights__contains='C',
        ),
        'D': Q(
            vertical_heights__contains='D',
        ),
        'E': Q(
            vertical_heights__contains='E',
        ),
        'F': Q(
            vertical_heights__contains='F',
        ),
        'G': Q(
            vertical_heights__contains='G',
        ),
    }

    def lookups(self, request, model_admin):
        return ((height, height) for height in self.FILTERS)

    def queryset(self, request, queryset):
        filters = Q()
        for applied_filter in self.value_as_list():
            filters &= self.FILTERS.get(applied_filter, Q())
        return queryset.filter(filters)


class IsRequestAcceptedFilter(admin.SimpleListFilter):
    title = 'Accepted Status'
    parameter_name = 'is_accepted'

    def lookups(self, request, model_admin):
        return (
            (True, 'Accepted'),
            (False, 'Not Accepted'),
        )

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(accepted_at__isnull=not self.value())


class IsRequestRejectedFilter(admin.SimpleListFilter):
    title = 'Rejected Status'
    parameter_name = 'is_rejected'

    def lookups(self, request, model_admin):
        return (
            (True, 'Rejected'),
            (False, 'Not Rejected'),
        )

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(rejected_at__isnull=not self.value())


class SofaMaterialTypeFilter(admin.SimpleListFilter):
    title = 'Sofa Material Type'
    parameter_name = 'sofa_material_type'

    def lookups(self, request, model_admin):
        return (
            ('wool', 'Wool (Rewool2)'),
            ('corduroy', 'Corduroy'),
            ('leather', 'Leather'),
        )

    def get_materials_filter(self):
        material_ids = []
        if self.value() == 'wool':
            material_ids = [color.value for color in Sofa01Color.get_wool_colors()]
        elif self.value() == 'corduroy':
            material_ids = [color.value for color in Sofa01Color.get_corduroy_colors()]
        elif self.value() == 'leather':
            material_ids = [color.value for color in Sofa01Color.get_leather_colors()]

        q = Q()
        for color_id in material_ids:
            q |= Q(cached_materials__contains=[color_id])
        return q

    def queryset(self, request, queryset):
        q = self.get_materials_filter()
        return queryset.filter(q)


class SofaMaterialTypeBatchFilter(SofaMaterialTypeFilter):
    def queryset(self, request, queryset):
        q = self.get_materials_filter()
        product_ids = Product.objects.filter(q).values_list('batch_id', flat=True)
        if product_ids:
            return queryset.filter(id__in=product_ids)
        return queryset
