import enum

from custom.enums import ChoicesMixin
from gallery.enums import ShelfPatternEnum


class WardrobeElemTypeEnum(enum.StrEnum):
    FRAME_TOP = 'Ft'
    FRAME_SIDE = 'Fs'
    FRAME_BUTTON = 'Fb'
    SLAB_STANDARD = 'Ss'
    SLAB_DRAWER = 'Sd'
    SLAB_BASE = 'Sb'
    SLAB_TOP = 'St'
    SLAB_PUSH = 'Sp'
    SLAB_LOCK = 'Sl'
    SLAB_FAT = 'Sf'
    BACK_BOTTOM = 'Bb'
    BACK_MIDDLE = 'Bm'
    BACK_TOP = 'Bt'
    INSERT_WARDROBE = 'Iw'
    WALL = 'W'
    WALL_SIDE = 'Ws'
    WALL_MIDDLE = 'Wm'
    DOOR_EXTERIOR = 'De'
    DOOR_EXTERIOR_TOP = 'Dt'
    DOOR_INTERIOR = 'Di'
    DRAWER_EXTERIOR = 'Te'
    DRAWER_INTERIOR = 'Ti'
    DRAWER_INTERIOR_BOTTOM = 'Tb'
    DRAWER_INTERIOR_WITH_DISTANCE = 'Td'
    DRAWER_DOUBLE_FRONT = 'Tf'
    DRAWER_FRONT = 'T_front'
    DRAWER_SIDE_LEFT = 'T_side1'
    DRAWER_SIDE_RIGHT = 'T_side2'
    DRAWER_BOTTOM = 'T_bottom'
    DRAWER_BACK = 'T_back'
    DRAWER_ADAPTER = 'Ta'
    HINGE = 'N'
    MASK = 'M'
    BAR = 'R'
    CROSSBAR = 'Rz'
    KIELBASA_SHORT = 'Lm'

    ELEMENTS = ''

    @property
    def regular(self):
        return {
            self.FRAME_TOP,
            self.FRAME_SIDE,
            self.FRAME_BUTTON,
            self.SLAB_STANDARD,
            self.SLAB_DRAWER,
            self.SLAB_BASE,
            self.SLAB_LOCK,
            self.SLAB_TOP,
            self.SLAB_PUSH,
            self.SLAB_FAT,
            self.BACK_BOTTOM,
            self.BACK_MIDDLE,
            self.BACK_TOP,
            self.INSERT_WARDROBE,
            self.WALL,
            self.WALL_SIDE,
            self.WALL_MIDDLE,
            self.HINGE,
            self.MASK,
            self.BAR,
            self.CROSSBAR,
        }

    @property
    def feature(self):
        return {
            self.DOOR_EXTERIOR,
            self.DOOR_EXTERIOR_TOP,
            self.DOOR_INTERIOR,
            self.DRAWER_EXTERIOR,
            self.DRAWER_INTERIOR,
            self.DRAWER_INTERIOR_BOTTOM,
            self.DRAWER_INTERIOR_WITH_DISTANCE,
            self.DRAWER_FRONT,
            self.DRAWER_SIDE_LEFT,
            self.DRAWER_SIDE_RIGHT,
            self.DRAWER_BOTTOM,
            self.DRAWER_BACK,
            self.DRAWER_ADAPTER,
            self.DRAWER_DOUBLE_FRONT,
            self.KIELBASA_SHORT,
        }


class ShelfElemTypeEnum(enum.StrEnum):
    HORIZONTAL = 'H'
    VERTICAL = 'V'
    VERTICAL_SOLO = 'Vd'
    SUPPORT = 'S'
    BACK = 'B'
    DOOR = 'D'
    DRAWER = 'T'
    JOINT = 'J'
    LEG = 'L'
    SHELF = 'Sh'

    # Elements "+"
    INSERTS = 'I'
    INSERT_HORIZONTAL = 'Ih'
    INSERT_VERTICAL = 'Iv'
    INSERT_DRAWER = 'It'
    INSERT_BACK = 'Bi'
    PLINTH = 'P'
    PLINTH_BEAM = 'Px'
    PLINTH_BAR = 'Pz'
    PLINTH_END = 'Pe'
    PLINTH_BAR_FRONT = 'Pf'
    PLINTH_BAR_BACK = 'Pb'
    LONG_LEG = 'Ll'
    GROMMET = 'G'
    WALL_FITTING = 'Wf'
    BACK_WITH_GROMMET = 'Bg'
    BACK_DESK = 'Bd'
    SUPPORTING_BACK = 'Bs'

    ELEMENTS = ''

    @property
    def regular(self):
        return {
            self.HORIZONTAL,
            self.VERTICAL,
            self.SUPPORT,
            self.BACK,
            self.BACK_WITH_GROMMET,
            self.SUPPORTING_BACK,
            self.INSERTS,
            self.PLINTH,
            self.INSERT_VERTICAL,
            self.INSERT_HORIZONTAL,
            self.INSERT_DRAWER,
            self.INSERT_BACK,
            self.PLINTH_BEAM,
            self.PLINTH_BAR,
            self.PLINTH_END,
            self.PLINTH_BAR_FRONT,
            self.PLINTH_BAR_BACK,
            self.BACK_DESK,
            self.VERTICAL_SOLO,
        }

    @property
    def feature(self):
        return {
            self.DOOR,
            self.DRAWER,
            self.LONG_LEG,
        }


class FileStatus(ChoicesMixin, enum.IntEnum):
    NOT_NEEDED = 0
    READY = 1
    PENDING = 3
    MISSING = 5
    ERROR = 10

    @property
    def color(self):
        return {
            FileStatus.NOT_NEEDED: 'gray',
            FileStatus.READY: 'green',
            FileStatus.PENDING: 'yellow',
            FileStatus.MISSING: 'red',
            FileStatus.ERROR: 'black',
        }[self]


# Deleted manufacturer IDs
DELETED_MANUFACTURER_IDS = [
    28,
]


class Manufacturers(ChoicesMixin, enum.IntEnum):
    UNKNOWN = 0

    DREWTUR = 1
    MEBLE_PL = 24
    NOVUM = 25
    STUDIO_93 = 29
    AMIR = 30
    INEX = 31
    CENTER_MEBEL = 33
    TELMEX = 34
    GALA = 35

    @classmethod
    def _missing_(cls, value):
        if value is None:
            return cls.UNKNOWN
        if value in DELETED_MANUFACTURER_IDS:
            return cls.UNKNOWN
        raise ValueError(
            f'{value} is not a valid {cls.__name__} value. '
            'If Manufacturer was added to the system, '
            'remember to add it to the enum as well.'
        )


class FeatureEnum(ChoicesMixin, enum.IntEnum):
    DESK = 0
    S_PLUS = 1

    @classmethod
    def is_product_desk(cls, product):
        return product.is_desk

    @classmethod
    def has_product_s_plus(cls, product):
        return product.has_plus_feature

    def does_product_have_feature(self, product):
        feature_validators = {
            FeatureEnum.DESK: self.is_product_desk,
            FeatureEnum.S_PLUS: self.has_product_s_plus,
        }
        return feature_validators[self](product)


class BatchFeatureEnum(ChoicesMixin, enum.IntEnum):
    WITH_EXTRA_FEATURE = 0
    BOTH = 1
    NONE = 2

    def does_batch_have_feature(self, batch):
        if self == self.BOTH:
            return True
        is_batch_with_extra_feature = self.is_batch_with_extra_feature(batch)
        if self == self.NONE and not is_batch_with_extra_feature:
            return True
        if self == self.WITH_EXTRA_FEATURE and is_batch_with_extra_feature:
            return True
        return False

    @classmethod
    def is_batch_with_extra_feature(cls, batch):
        for product in batch.batch_items.all():
            if cls.does_product_have_extra_feature(product):
                return True
        return False

    @classmethod
    def does_product_have_extra_feature(cls, product):
        return (
            product.is_desk
            or product.has_plus_feature
            or product.cached_dna_style == ShelfPatternEnum.PIXEL
        )


class BatchMailingStatusEnum(ChoicesMixin, enum.IntEnum):
    NOT_SENT = 0
    IN_PROGRESS = 1
    SENT = 2
    ERROR = 3


class CustomPricingFactorChangeRequestStatusEnum(ChoicesMixin, enum.IntEnum):
    NEW = 0
    ACCEPTED = 1
    REJECTED = 2
    CHANGES_REQUESTED = 3
