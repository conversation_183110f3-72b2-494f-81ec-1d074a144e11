import errno
import json
import os
import time

from django.conf import settings
from django.core.management import call_command

from celery import shared_task
from celery.utils.log import get_task_logger
from google.cloud import bigquery
from google.cloud.exceptions import BadRequest

from custom.api_clients.n8n import send_model_data_to_n8n
from custom.metrics import task_metrics
from custom.shelf_states_interactor import (
    FailedShelfStatesInteractor,
    ShelfStatesInteractor,
)
from kpi.big_query import upload_list_to_big_query
from product_feeds.tasks import send_jetties_to_facebook_feed

task_logger = get_task_logger('celery_task')


@shared_task
def check_sendmail_lock():
    lock_file_path = '/tmp/send_mail.lock'  # noqa: S108
    try:
        lock_file_age_minutes = (time.time() - os.stat(lock_file_path).st_mtime) // 60
        if lock_file_age_minutes > 10:
            os.remove(lock_file_path)
    except OSError as e:
        if e.errno == errno.ENOENT:
            pass
        else:
            raise e


@shared_task
@task_metrics
def export_shelf_states_to_bigquery():
    interactor = ShelfStatesInteractor()
    states_list = interactor.get_all_states_on_queue()
    ids = [item['id'] for item in states_list]
    for country, catalog_id in settings.FB_PRODUCT_CATALOGS.items():
        send_jetties_to_facebook_feed.delay(ids, catalog_id, country)
    try:
        upload_list_to_big_query(
            'sold_items',
            'shelf_states',
            states_list,
            write=bigquery.WriteDisposition.WRITE_APPEND,
        )
    except BadRequest as ex:
        interactor = FailedShelfStatesInteractor()
        interactor.add_to_queue(states_list)
        for er in ex.errors:
            task_logger.exception(
                'Error while uploading shelf states to bigquery %s', er
            )


@shared_task
def export_survey_data_to_bigquery(space, table, serialized_data):
    data = json.loads(serialized_data)
    upload_list_to_big_query(
        space=space,
        table=table,
        my_list=[data],
        write=bigquery.WriteDisposition.WRITE_APPEND,
    )


@shared_task
def clear_sessions():
    call_command('clearsessions')


@shared_task
def send_to_n8n(model_name, object_id, action):
    send_model_data_to_n8n(model_name, object_id, action)
