import enum
import logging

from django.conf import settings

import requests

from custom.utils.import_object import import_object

logger = logging.getLogger('cstm')


class N8nModel(enum.Enum):
    REVIEW = 'Review'
    FREE_RETURN = 'FreeReturn'

    @property
    def model(self):
        model_path = {
            'Review': 'reviews.models.Review',
            'FreeReturn': 'free_returns.models.FreeReturn',
        }[self.value]
        return import_object(model_path)

    @property
    def serializer(self):
        serializer_path = {
            'Review': 'reviews.serializers.ReviewWebhookSerializer',
            'FreeReturn': 'free_returns.serializers.FreeReturnWebhookSerializer',
        }[self.value]
        return import_object(serializer_path)


def send_model_data_to_n8n(n8n_model: N8nModel, object_id: int, action: str) -> None:
    if not settings.N8N_WEBHOOK:
        return

    instance = n8n_model.model.objects.get(id=object_id)
    serializer = n8n_model.serializer(instance=instance)
    serialized_data = serializer.data

    data = {
        'model': n8n_model.value,
        'action': action,
        'data': serialized_data,
    }
    response = requests.post(
        settings.N8N_WEBHOOK,
        json=data,
        timeout=10,
    )
    response.raise_for_status()
