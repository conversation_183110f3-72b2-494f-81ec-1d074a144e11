import hashlib

from functools import wraps
from typing import (
    Callable,
    Final,
    Iterable,
)
from urllib.parse import (
    parse_qsl,
    urlencode,
    urlsplit,
)

from django.core.cache import cache
from rest_framework.response import Response

ONE_HOUR: Final[int] = 60 * 60 * 1


# AI generated
def _normalized_querystring(request, include_params: Iterable[str] | None) -> str:
    """
    Builds a stable, normalized querystring for cache keys.

    Args:
        request: DRF request object.
        include_params: Specific query params to include in the key. If None, all
            params are included (sorted alphabetically).

    Returns:
        str: Normalized querystring.
    """
    if include_params:
        items: list[tuple[str, str]] = []
        for name in include_params:
            items.extend((name, v) for v in request.query_params.getlist(name))
    else:
        # take all params, but sorted for stability
        qs_pairs = parse_qsl(
            urlsplit(request.get_full_path()).query, keep_blank_values=True
        )
        items = sorted(qs_pairs)

    return urlencode(items, doseq=True)


def cache_response(
    ttl: int = ONE_HOUR,
    *,
    skip_if: Callable[..., bool] | None = None,
    key_from_params: Iterable[str] | None = None,
):
    """Decorator for caching DRF action responses.

    Works only for GET requests. Allows skipping cache conditionally and building
    stable keys based on selected query params.

    Args:
        ttl: Cache timeout in seconds.
        skip_if: Callable that decides whether to skip caching for the request.
        key_from_params: Query params to build the cache key from. If None, all
            params are used.

    Returns:
        Callable: Wrapped view function with caching applied.
    """

    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            if request.method != 'GET':
                return func(self, request, *args, **kwargs)

            if skip_if and skip_if(self, request, *args, **kwargs):
                return func(self, request, *args, **kwargs)

            qs = _normalized_querystring(request, key_from_params)
            hashed_qs = hashlib.md5(qs.encode()).hexdigest()  # noqa: S324

            action_name = getattr(self, 'action', func.__name__)
            key = f'response_cache:{self.__class__.__name__}:{action_name}:{hashed_qs}'
            cached_response = cache.get(key)

            if cached_response:
                return Response(
                    cached_response['data'], status=cached_response['status']
                )

            response = func(self, request, *args, **kwargs)
            if isinstance(response, Response):
                cache.set(
                    key,
                    {'data': response.data, 'status': response.status_code},
                    timeout=ttl,
                )
            return response

        return wrapper

    return decorator


def cache_function_result(key: str | Callable, ttl: int = ONE_HOUR):
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            if cached_result := cache.get(key):
                return cached_result

            result = func(self, request, *args, **kwargs)
            cache.set(key, value=result, timeout=ttl)
            return result

        return wrapper

    return decorator
