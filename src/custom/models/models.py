import logging

from datetime import (
    date,
    datetime,
    timedelta,
)
from decimal import Decimal
from typing import Dict

from django.conf import settings
from django.core.cache import cache
from django.db import models
from django.urls import reverse

from celery import states

from cstm_be.media_storage import private_media_storage
from custom.api_clients.nbp import (
    NBPAPIClient,
    NBPApiClientException,
    NbpExchangeTable,
)
from custom.encoder import DecimalToFloatDjangoJSONEncoder
from custom.models import Timestampable
from custom.models.managers import SingletonManager

logger = logging.getLogger('cstm')


ALL_STATES = sorted(states.ALL_STATES)
TASK_STATE_CHOICES = sorted(zip(ALL_STATES, ALL_STATES))


class GlobalSettings(models.Model):
    """
    Model for variables that would traditionally go to settings,
    but we want them to be easily editable
    """

    is_live_payment = models.BooleanField(default=False)
    is_deployment_in_progress = models.BooleanField(default=False)
    this_month_target = models.IntegerField(
        'Actual month target in k euros',
        default=1800,
    )
    exchange_pln = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=4.3,
    )
    exchange_gbp = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=0.77,
    )
    exchange_chf = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=1.1,
    )
    shipping_cost_kg = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=3.66,
    )
    out_of_stock_cache_timeout = models.IntegerField(
        default=60,
        help_text='Cache timeout for stock level data, in minutes.',
    )
    out_of_stock_minimum_stock = models.IntegerField(
        default=10,
        help_text='Minimum stock quantity required '
        'for a product to be considered in stock.',
    )
    assembly_promo_active = models.BooleanField(default=False)

    objects = SingletonManager()

    def save(self, *args, **kwargs):
        from skus.services.sku_stock import SkuStockCache
        from vouchers.constants import clean_assembly_promo_cache

        SkuStockCache.clear_settings_cache()
        clean_assembly_promo_cache()
        super().save(*args, **kwargs)

    @classmethod
    def live_payment_switch(cls):
        return (
            cls.objects.first().is_live_payment if not settings.IS_PRODUCTION else True
        )

    @classmethod
    def factor_euro(cls):
        factor_euro = cache.get('factor_euro')
        if not factor_euro:
            factor_euro = cls.objects.first().exchange_pln
            cache.set('factor_euro', factor_euro, 86400)
        return Decimal(factor_euro)

    @classmethod
    def get_shipping_cost(cls):
        shipping_cost = cache.get('shipping_cost')
        if not shipping_cost:
            shipping_cost = cls.objects.first().shipping_cost_kg
            cache.set('shipping_cost', shipping_cost, 86400)
        return Decimal(shipping_cost)

    @classmethod
    def actual_month_target(cls):
        return cls.objects.first().this_month_target

    @classmethod
    def set_payment_sandbox(cls):
        if settings.IS_PRODUCTION:
            logger.error(
                'Trying to set payments to sandbox on production. Nothing will happen.'
            )
            return
        obj = cls.objects.first()
        obj.is_live_payment = False
        obj.save()

    @classmethod
    def set_payment_live(cls):
        obj = cls.objects.first()
        obj.is_live_payment = True
        obj.save()

    @classmethod
    def set_deployment_in_progress(cls):
        obj = cls.objects.first()
        obj.is_deployment_in_progress = True
        obj.save()

    @classmethod
    def set_deployment_finished(cls):
        obj = cls.objects.first()
        obj.is_deployment_in_progress = False
        obj.save()

    @classmethod
    def is_assembly_promo_active(cls) -> bool:
        return cls.objects.first().assembly_promo_active

    class Meta:  # noqa: DJ012
        verbose_name_plural = 'Global Settings'


class ExchangeRate(models.Model):
    exchange_date = models.DateField(unique=True)
    rates = models.JSONField(encoder=DecimalToFloatDjangoJSONEncoder)

    created_at = models.DateTimeField(auto_now_add=True)
    set_with = models.TextField(null=True, blank=True)  # noqa: DJ001

    def __str__(self):
        return 'Exchange for {}'.format(self.exchange_date)

    @staticmethod
    def _get_exchange_rates(year, month, day) -> Dict[str, Decimal]:
        check_for_date = date(year=int(year), month=int(month), day=int(day))
        if ExchangeRate.objects.filter(exchange_date=check_for_date).exists():
            return ExchangeRate.objects.get(exchange_date=check_for_date).rates
        else:
            client = NBPAPIClient()

            try:
                exchange_table = client.get_exchange_table(check_for_date)
            except NBPApiClientException as ex:
                set_with = (
                    f'Set {datetime.now()}, for date {check_for_date}, '
                    f'with error {ex.message}'
                )

                empty_rates = {}
                if check_for_date < date.today():
                    ExchangeRate.objects.create(
                        exchange_date=check_for_date,
                        rates=empty_rates,
                        set_with=set_with,
                    )

                return empty_rates

            rates = ExchangeRate._map_exchange_rates_to_dict(exchange_table)

            ExchangeRate.objects.create(
                exchange_date=check_for_date,
                rates=rates,
                set_with=f'Set {datetime.now()}, for date {check_for_date}, '
                f'response 200',
            )

            return rates

    @staticmethod
    def get_safe_exchange(year, month, day) -> Dict[str, Decimal]:
        current_day = datetime(year=year, month=month, day=day)
        rates = {}
        counter = 0
        while True:
            counter += 1
            if counter > 9:
                raise StopIteration
            rates = ExchangeRate._get_cached_exchange(
                current_day.year, current_day.month, current_day.day
            )
            if len(rates) > 0:
                break
            current_day = current_day - timedelta(days=1)
        return rates

    @staticmethod
    def _map_exchange_rates_to_dict(
        exchange_table: NbpExchangeTable,
    ) -> Dict[str, Decimal]:
        exchange_table_rates = exchange_table.daily_rates
        rates = {'PLN': Decimal(1.0)}  # noqa: RUF032

        if len(exchange_table_rates) > 0:
            for rate in exchange_table_rates[0].currency_rates:
                rates[rate.code] = rate.mid

        return rates

    @staticmethod
    def _get_cached_exchange(year, month, day) -> Dict[str, Decimal]:
        currency_rates = cache.get(f'currency_rates_{year}-{month}-{day}')
        if not currency_rates:
            actual_rates = ExchangeRate._get_exchange_rates(year, month, day)
            cache.set(
                'currency_rates_%s-%s-%s'
                % (
                    year,
                    month,
                    day,
                ),
                actual_rates,
                60 * 60 * 2,
            )
            return actual_rates
        return currency_rates

    def weekday(self):
        return self.exchange_date.strftime('%A')

    def number_of_entries(self) -> int:
        return len(self.rates)


class DocumentRequest(Timestampable):
    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    status = models.CharField(
        max_length=50,
        default=states.PENDING,
        choices=TASK_STATE_CHOICES,
        verbose_name='Task State',
    )
    file = models.FileField(
        upload_to='document_requests/%Y/%m',
        storage=private_media_storage,
        null=True,
        blank=True,
        max_length=150,
    )
    action_name = models.CharField(blank=True, max_length=150)
    exception_meta = models.JSONField(blank=True, default=dict)

    def get_admin_url(self):
        url = reverse('admin:custom_documentrequest_change', args=(self.id,))
        return f'<a href="{url}">{url}</a>'

    def __str__(self):
        return (
            f'DocumentRequest[id={self.id} status={self.status} '
            f'action_name={self.action_name}'
        )
