from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from unittest.mock import patch

from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from rest_framework import status

import pytest

from pytest_cases import parametrize_with_cases

from carts.services.cart_service import CartService
from skus.enums import (
    AttributeTypeChoices,
    SkuStatusChoices,
)
from skus.models import SkuVariant
from skus.services import SkuStockService
from wishlist.models import WishlistItem


@pytest.fixture
def strikethrough_promotion(
    voucher_factory,
    promotion_factory,
    promotion_config_factory,
    item_discount_factory,
    sku_category_factory,
):
    sku_category = sku_category_factory(can_be_added_to_wishlist=True)
    item_discount = item_discount_factory(sku_category=sku_category)
    voucher = voucher_factory(is_percentage=True)
    voucher.discounts.add(item_discount)
    promotion = promotion_factory(promo_code=voucher, strikethrough_pricing=True)
    promotion_config_factory(promotion=promotion)
    return promotion, sku_category


class SkuDetailPricingCases:
    def case_with_strikethrough_promotion(self, promotion_factory):
        promotion = promotion_factory(strikethrough_pricing=True)
        expected_pricing = {
            'price': Decimal('100.0'),
            'region_price': Decimal('100.0'),
            'price_with_discount': (
                Decimal('100.0') - Decimal(promotion.promo_code.value)
            ).quantize(Decimal('1.'), rounding=ROUND_HALF_UP),
            'region_price_with_discount': (
                Decimal('100.0') - Decimal(promotion.promo_code.value)
            ).quantize(Decimal('1.'), rounding=ROUND_HALF_UP),
            'omnibus_price': None,
        }
        return expected_pricing

    def case_with_normal_promotion(self, promotion_factory):
        promotion_factory(strikethrough_pricing=False)
        expected_pricing = {
            'price': Decimal('100.0'),
            'region_price': Decimal('100.0'),
            'price_with_discount': Decimal('100.0'),
            'region_price_with_discount': Decimal('100.0'),
            'omnibus_price': None,
        }
        return expected_pricing


@pytest.mark.django_db
class TestSkuVariantViewSet:
    def test_filter_by_category(
        self,
        sku_category_factory,
        sku_factory,
        sku_variant_factory,
        api_client,
        django_assert_max_num_queries,
        mocker,
    ):
        category = 'Samples'
        sku_category = sku_category_factory(name=category)
        sample_sku = sku_factory(
            sku_category=sku_category, status=SkuStatusChoices.ACTIVE
        )
        sample_sku_variant = sku_variant_factory(sku=sample_sku)

        sku = sku_factory(status=SkuStatusChoices.ACTIVE)
        sku_variant = sku_variant_factory(sku=sku)

        mocker.patch.object(
            SkuStockService,
            'get_sku_variant_available_quantity',
            return_value={
                variant.sku_code: 5 for variant in [sample_sku_variant, sku_variant]
            },
        )

        url = reverse('sku-variants-list')
        with django_assert_max_num_queries(35):
            response = api_client.get(url, {'category': category})

        returned_ids = {item['id'] for item in response.data['results']}
        assert response.status_code == status.HTTP_200_OK
        assert len(returned_ids) == 1
        assert sample_sku_variant.id in returned_ids

    def test_filter_by_region(
        self, sku_factory, sku_variant_factory, api_client, region_uk, mocker
    ):
        sku = sku_factory(status=SkuStatusChoices.ACTIVE)
        excluded_sku_variant = sku_variant_factory(sku=sku)
        excluded_sku_variant.excluded_regions.add(region_uk)
        sku_variant = sku_variant_factory(sku=sku)

        mocker.patch.object(
            SkuStockService,
            'get_sku_variant_available_quantity',
            return_value={sku_variant.sku_code: 5},
        )

        url = reverse('sku-variants-list')
        response = api_client.get(url, {'regionName': 'united_kingdom'})

        returned_ids = {item['id'] for item in response.data['results']}
        assert response.status_code == status.HTTP_200_OK
        assert len(returned_ids) == 1
        assert excluded_sku_variant.id not in returned_ids

    @pytest.mark.parametrize(
        ('in_stock_level', 'out_of_stock_level'),
        [
            (5, 0),
            (10, 0),
            (1, 0),
        ],
    )
    def test_out_of_stock_list_view(
        self,
        sku_factory,
        sku_variant_factory,
        api_client,
        mocker,
        in_stock_level,
        out_of_stock_level,
    ):
        # assign
        sku = sku_factory(status=SkuStatusChoices.ACTIVE)
        in_stock_sku_variant = sku_variant_factory(sku=sku)
        out_of_stock_sku_variant = sku_variant_factory(sku=sku)

        # patch service
        mocker.patch.object(
            SkuStockService,
            'get_sku_variant_available_quantity',
            return_value={
                in_stock_sku_variant.sku_code: in_stock_level,
                out_of_stock_sku_variant.sku_code: out_of_stock_level,
            },
        )
        mocker.patch.object(
            SkuStockService,
            'get_unavailable_sku_variant_ids',
            return_value=[out_of_stock_sku_variant.id],
        )

        # act
        url = reverse('sku-variants-list')
        response = api_client.get(url)

        # assert
        assert response.status_code == status.HTTP_200_OK
        results = {
            item['sku_code']: item['in_stock'] for item in response.data['results']
        }
        assert results[in_stock_sku_variant.sku_code]
        assert not results[out_of_stock_sku_variant.sku_code]
        assert response.data['results'][0]['unavailable_sku_variant_ids'] == [
            out_of_stock_sku_variant.id
        ]

    def test_return_only_active_sku_variants(
        self, sku_factory, sku_variant_factory, api_client, mocker
    ):
        active_sku = sku_factory(status=SkuStatusChoices.ACTIVE)
        active_sku_variant = sku_variant_factory(sku=active_sku)
        sku = sku_factory()
        sku_variant = sku_variant_factory(sku=sku)

        mocker.patch.object(
            SkuStockService,
            'get_sku_variant_available_quantity',
            return_value={
                variant.sku_code: 5 for variant in [active_sku_variant, sku_variant]
            },
        )

        url = reverse('sku-variants-list')
        response = api_client.get(url)

        returned_ids = {item['id'] for item in response.data['results']}
        assert response.status_code == status.HTTP_200_OK
        assert len(returned_ids) == 1
        assert active_sku_variant.id in returned_ids

    def test_skus_details_view(
        self,
        sku_factory,
        sku_variant_factory,
        attribute_factory,
        attribute_value_factory,
        api_client,
    ):
        # assign
        sample_box = sku_factory(name='Sample Box', status=SkuStatusChoices.ACTIVE)
        red_sku_variant = sku_variant_factory(
            sku=sample_box, name='Red wooden sample box'
        )
        blue_sku_variant = sku_variant_factory(
            sku=sample_box, name='Blue wooden sample Box'
        )
        color_attribute = attribute_factory(
            help_text='Sample colors',
            name='color',
            creates_sku_variant=True,
            type=AttributeTypeChoices.NUMERIC,
        )
        sample_box.attributes.set([color_attribute])
        red_attribute_value = attribute_value_factory(
            attribute=color_attribute, assigned_variant=red_sku_variant, value='1'
        )
        blue_attribute_value = attribute_value_factory(
            attribute=color_attribute, assigned_variant=blue_sku_variant, value='2'
        )
        attributes = {
            'color': [
                {red_attribute_value.get_value(): red_sku_variant.id},
                {blue_attribute_value.get_value(): blue_sku_variant.id},
            ]
        }
        active_attributes = [{red_attribute_value.attribute.name: red_sku_variant.id}]
        # act
        url = reverse('sku-variants-detail', args=[red_sku_variant.id])
        response = api_client.get(url)
        # assert
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == red_sku_variant.id
        assert response.data['sku'] == sample_box.id
        assert response.data['sku_code'] == red_sku_variant.sku_code
        assert sorted(
            response.data['attributes']['color'], key=lambda x: next(iter(x.keys()))
        ) == sorted(attributes['color'], key=lambda x: next(iter(x.keys())))
        assert response.data['active_attributes'] == active_attributes

    @parametrize_with_cases('expected_pricing', cases=SkuDetailPricingCases)
    def test_sku_pricing(
        self,
        expected_pricing,
        sku_factory,
        sku_variant_factory,
        api_client,
    ):
        # assign
        sample_box = sku_factory(name='Sample Box', status=SkuStatusChoices.ACTIVE)
        red_sku_variant = sku_variant_factory(
            price=Decimal('100.0'),
            sku=sample_box,
            name='Red wooden sample box',
        )
        # act
        url = reverse('sku-variants-detail', args=[red_sku_variant.id])
        response = api_client.get(url)
        # assert
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == red_sku_variant.id
        assert response.data['pricing'] == expected_pricing

    def test_out_of_stock_details_view(
        self, sku_factory, sku_variant_factory, api_client, mocker
    ):
        # assign
        sku = sku_factory(status=SkuStatusChoices.ACTIVE)
        in_stock_sku_variant = sku_variant_factory(sku=sku)
        out_of_stock_sku_variant = sku_variant_factory(sku=sku)
        mocker.patch.object(
            SkuStockService,
            'get_sku_variant_available_quantity',
            return_value={
                in_stock_sku_variant.sku_code: 5,
                out_of_stock_sku_variant.sku_code: 0,
            },
        )

        # act
        url = reverse('sku-variants-detail', args=[in_stock_sku_variant.id])
        response = api_client.get(url)
        # assert
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == in_stock_sku_variant.id
        assert response.data['available_quantity'] == 5

    def test_add_to_cart(
        self,
        sku_variant_factory,
        api_client,
        user,
    ):
        # assign
        skuvariant_content_type = ContentType.objects.get_for_model(SkuVariant)
        red_sku_variant = sku_variant_factory(
            price=Decimal('100.0'),
            sku__status=SkuStatusChoices.ACTIVE,
            name='Red wooden sample box',
        )
        blue_sku_variant = sku_variant_factory(
            price=Decimal('120.0'),
            sku__status=SkuStatusChoices.ACTIVE,
            name='Blue wooden sample box',
        )
        skuvariants_ids = [red_sku_variant.id, blue_sku_variant.id]
        data = {
            'skuvariants_ids': skuvariants_ids,
        }
        # act
        api_client.force_authenticate(user=user)
        url = reverse('sku-variants-add-to-cart')
        response = api_client.post(url, data=data, format='json')
        # assert
        cart = CartService.get_or_create_cart(user)
        assert response.status_code == status.HTTP_201_CREATED
        assert list(cart.items.values('object_id', 'content_type').order_by('id')) == [
            {'object_id': skuvariant_id, 'content_type': skuvariant_content_type.id}
            for skuvariant_id in skuvariants_ids
        ]
        assert cart.total_price == red_sku_variant.price + blue_sku_variant.price

    def test_remove_from_wishlist(
        self,
        wishlist_item_factory,
        sku_variant_factory,
        api_client,
        user,
    ):
        # assign
        sku_variant = sku_variant_factory(
            sku__status=SkuStatusChoices.ACTIVE,
            sku__sku_category__name='Samples',
            sku__sku_category__can_be_added_to_wishlist=True,
        )
        wishlist_item_factory(
            owner=user,
            content_type=ContentType.objects.get_for_model(sku_variant),
            object_id=sku_variant.id,
        )
        api_client.force_authenticate(user=user)
        url = reverse(
            'sku-variants-remove-from-wishlist', kwargs={'pk': sku_variant.id}
        )
        # act
        response = api_client.delete(path=url)
        # assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert not WishlistItem.objects.filter(
            owner=user,
            content_type__model='skuvariant',
            object_id=sku_variant.id,
        ).exists()

    def test_add_to_wishlist(
        self, sku_variant_factory, api_client, user, strikethrough_promotion
    ):
        # assign
        promotion, sku_category = strikethrough_promotion
        sku_variant = sku_variant_factory(
            sku__status=SkuStatusChoices.ACTIVE,
            sku__sku_category=sku_category,
            sku__sku_category__can_be_added_to_wishlist=True,
        )
        api_client.force_authenticate(user=user)
        url = reverse(
            'sku-variants-add-by-id-to-wishlist', kwargs={'pk': sku_variant.id}
        )
        # act
        response = api_client.post(path=url)
        # assert
        assert response.status_code == status.HTTP_201_CREATED
        assert WishlistItem.objects.filter(
            owner=user,
            content_type__model='skuvariant',
            object_id=response.data['sku_variant_id'],
        ).exists()
        assert (
            response.data['strikethrough_promo_value']
            == promotion.promo_code.discounts.filter(
                sku_category=sku_variant.sku.sku_category
            )
            .first()
            .value
        )

    def test_add_to_wishlist_popup_by_id(
        self, sku_variant_factory, api_client, user, strikethrough_promotion
    ):
        # assign
        sku_variant = sku_variant_factory(
            sku__status=SkuStatusChoices.ACTIVE,
            sku__sku_category__name='Samples',
            sku__sku_category__can_be_added_to_wishlist=True,
        )
        api_client.force_authenticate(user=user)
        url = reverse(
            'sku-variants-add-by-id-to-wishlist-popup', kwargs={'pk': sku_variant.id}
        )
        data = {
            'email': user.email,
            'popup_src': 'test_source',
            'has_marketing_permissions': True,
        }
        # act
        with patch('marketing.services.save_for_later.emit_saved_item_related_events'):
            response = api_client.post(path=url, data=data, format='json')
        # assert
        assert response.status_code == status.HTTP_201_CREATED
        assert WishlistItem.objects.filter(
            owner=user,
            content_type__model='skuvariant',
            object_id=response.data['sku_variant_id'],
        ).exists()
