from datetime import datetime
from decimal import Decimal
from typing import Any

from django.contrib.contenttypes.fields import GenericRelation
from django.db import (
    models,
    transaction,
)
from django.db.models import (
    Case,
    DecimalField,
    F,
    Prefetch,
    QuerySet,
    When,
)
from django.db.models.fields.files import ImageFieldFile
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

import inject

from slugify import slugify
from taggit.managers import TaggableManager
from unidecode import unidecode

from custom.enums import (
    Furniture,
    ShelfType,
)
from custom.models.behaviours import Timestampable
from custom.utils.dimensions import Dimensions
from custom.utils.in_memory_cache import expiring_lru_cache
from gallery.models.furniture_abstract import SellableItemAbstract
from regions.models import (
    Currency,
    Region,
)
from regions.services.limitations import LimitationService
from regions.services.regionalized_price import RegionCalculationsObject
from regions.types import RegionLikeObject
from skus.enums import SkuStatusChoices
from skus.models import AttributeValue
from skus.models.abstracts import (
    Sku<PERSON>artAbstract,
    SkuCheckoutAbstract,
)
from skus.models.mixins import SkuFeedMixin
from skus.schema import AttributeValueSchema
from skus.tasks import send_sku_refresh_event


class SkuCategoryManager(models.Manager):
    @expiring_lru_cache(ttl=60 * 60 * 24)
    def get_choices(self) -> list[tuple[int, str]]:
        return [
            (id_, name) for id_, name in self.values_list('id', 'name').order_by('name')
        ]

    def get_ids(self) -> set[int]:
        return {choice[0] for choice in self.get_choices()}

    def get_names(self) -> list[str]:
        return [name for _, name in self.get_choices()]

    def get_id_to_slug_mapping(self) -> dict[int, str]:
        return {
            id_: slugify(name).replace('-', '_') for id_, name in self.get_choices()
        }

    def get_slug_to_id_mapping(self) -> dict[str, int]:
        return {
            slugify(name).replace('-', '_'): id_ for id_, name in self.get_choices()
        }


class SkuCategory(SkuFeedMixin, Timestampable):
    name = models.CharField(max_length=250)
    hts_code = models.CharField(
        max_length=20,
        blank=True,
    )
    can_be_added_to_wishlist = models.BooleanField(default=False)
    can_be_added_to_waitinglist = models.BooleanField(default=False)
    objects = SkuCategoryManager()

    class Meta:
        verbose_name = 'Sku Category'
        verbose_name_plural = 'Sku Categories'

    def __str__(self):
        return self.name

    @property
    def slug(self) -> str:
        return slugify(self.name).replace('-', '_')

    @cached_property
    def price(self) -> Decimal:
        return self.trackable_fields.price

    @cached_property
    def translated_name(self) -> str:
        return str(_(self.name.lower()))

    def get_price(self, dt: datetime | None = None) -> Decimal:
        if dt is None or dt < self.created_at:
            return self.price
        elif historical_price := self._get_historical_price(dt=dt):
            return historical_price
        else:
            return self.price

    def _get_historical_price(self, dt: datetime) -> Decimal | None:
        """Get historical price for the given datetime, returns None if not found."""
        from skus.models.trackable_models import TrackableSkuCategory

        if not hasattr(self, 'trackable_fields'):
            return None

        try:
            history_trackable_fields = self.trackable_fields.history.as_of(dt)
            return history_trackable_fields.price
        except TrackableSkuCategory.DoesNotExist:
            return None


class Sku(Timestampable):
    name = models.CharField(max_length=250)
    status = models.CharField(
        choices=SkuStatusChoices.choices,
        default=SkuStatusChoices.DRAFT,
        max_length=36,
    )
    sku_category = models.ForeignKey(
        SkuCategory, related_name='products', on_delete=models.CASCADE
    )
    attributes = models.ManyToManyField('skus.Attribute', blank=True)

    def __str__(self):
        return f'{self.name} ({self.sku_category})'

    @cached_property
    def price(self) -> Decimal:
        if (
            hasattr(self, 'trackable_fields')
            and self.trackable_fields.price is not None
        ):
            return self.trackable_fields.price
        else:
            return self.sku_category.trackable_fields.price

    def get_price(self, dt: datetime | None = None) -> Decimal:
        if dt is None or dt < self.created_at:
            return self.price
        elif historical_price := self._get_historical_price(dt=dt):
            return historical_price
        else:
            return self.sku_category.get_price(dt=dt)

    def _get_historical_price(self, dt: datetime) -> Decimal | None:
        """Get historical price for the given datetime, returns None if not found."""
        from skus.models.trackable_models import TrackableSku

        if not hasattr(self, 'trackable_fields'):
            return None

        try:
            history_trackable_fields = self.trackable_fields.history.as_of(dt)
            return history_trackable_fields.price
        except TrackableSku.DoesNotExist:
            return None


class SkuImage(models.Model):
    image = models.ImageField(upload_to='sku_products')
    sku_variant = models.ForeignKey(
        'SkuVariant', related_name='images', on_delete=models.CASCADE
    )
    sort_order = models.IntegerField(null=True)
    tags = TaggableManager()

    class Meta:
        ordering = ['sort_order']
        indexes = [
            models.Index(fields=['sort_order'], name='sort_order_idx'),
        ]


class SkuVariantQuerySet(models.QuerySet):
    def with_price(self) -> QuerySet['SkuVariant']:
        return self.annotate(
            price=Case(
                When(
                    trackable_fields__price__isnull=False,
                    then=F('trackable_fields__price'),
                ),
                When(
                    sku__trackable_fields__price__isnull=False,
                    then=F('sku__trackable_fields__price'),
                ),
                default=F('sku__sku_category__trackable_fields__price'),
                output_field=DecimalField(),
            )
        )

    def with_prefetched_objects(self) -> QuerySet['SkuVariant']:
        return self.select_related('sku', 'sku__sku_category').prefetch_related(
            Prefetch(
                'attributes',
                queryset=AttributeValue.objects.select_related('attribute'),
            ),
            Prefetch(
                'images',
                queryset=SkuImage.objects.filter(tags__name__in=['preview']),
                to_attr='prefetched_previews',
            ),
        )

    def all_for_region(
        self, region: RegionLikeObject | None = None
    ) -> QuerySet['SkuVariant']:
        """
        Some products are not available in all regions.
        Use this method to exclude them for given region
        """
        qs = self

        if region is None:
            return qs
        return qs.exclude(excluded_regions=region)


class SkuVariant(
    Timestampable,
    SellableItemAbstract,
    SkuCartAbstract,
    SkuCheckoutAbstract,
):
    sku = models.ForeignKey(Sku, related_name='variants', on_delete=models.CASCADE)
    sku_code = models.CharField(max_length=40)
    name = models.CharField(max_length=255)
    translation_key = models.CharField(max_length=255)
    weight = models.FloatField(
        blank=True,
        null=True,
    )
    height = models.IntegerField(default=0)
    width = models.IntegerField(default=0)
    depth = models.IntegerField(default=0)

    excluded_regions = models.ManyToManyField(
        'regions.Region',
        blank=True,
        related_name='excluded_sku_variants',
    )

    cart_items = GenericRelation('carts.CartItem', related_query_name='sku_variant')
    order_items = GenericRelation('orders.OrderItem', related_query_name='sku_variant')
    catalogue_items = GenericRelation(
        'catalogue.CatalogueEntry', related_query_name='sku_variant'
    )
    wishlist_items = GenericRelation(
        'wishlist.WishlistItem', related_query_name='sku_variant'
    )

    objects = SkuVariantQuerySet.as_manager()

    class Meta:
        verbose_name = 'Sku Variant'
        verbose_name_plural = 'Sku Variants'
        constraints = [
            models.UniqueConstraint(
                fields=['sku_code'],
                name='unique_sku_code',
                condition=~models.Q(sku_code=''),
            ),
        ]

    def __str__(self):
        return f'{self.name}'

    def get_recycle_tax_value(self, region: Region) -> Decimal | None:
        limitation_service = LimitationService(region=region)
        if limitation_service.is_recycle_tax_available:
            return Decimal('0')
        return None

    @property
    def sku_category(self) -> SkuCategory:
        return self.sku.sku_category

    @property
    def sku_category_name(self) -> str:
        return self.sku.sku_category.name

    @property
    def is_sample(self) -> bool:
        return self.sku.sku_category.name.lower() == 'samples'

    @property
    def preview(self) -> ImageFieldFile | None:
        preview_image = self.images.filter(tags__name__in=['preview']).first()
        if preview_image and preview_image.image:
            return preview_image.image
        return None

    @property
    def preview_url(self) -> str:
        return self.preview.url if self.preview else ''

    def get_title(self) -> str:
        return self.translated_name

    @property
    def translated_name(self) -> str:
        return _(self.translation_key)

    @cached_property
    def price(self) -> Decimal:
        if (
            hasattr(self, 'trackable_fields')
            and self.trackable_fields.price is not None
        ):
            return self.trackable_fields.price
        elif (
            hasattr(self.sku, 'trackable_fields')
            and self.sku.trackable_fields.price is not None
        ):
            return self.sku.trackable_fields.price
        else:
            return self.sku.sku_category.trackable_fields.price

    def get_price(self, dt: datetime | None = None) -> Decimal:
        if dt is None or dt < self.created_at:
            return self.price
        elif historical_price := self._get_historical_price(dt=dt):
            return historical_price
        else:
            return self.sku.get_price(dt=dt)

    def _get_historical_price(self, dt: datetime) -> Decimal | None:
        """Get historical price for the given datetime, returns None if not found."""
        from skus.models.trackable_models import TrackableSkuVariant

        if not hasattr(self, 'trackable_fields'):
            return None

        try:
            history_trackable_fields = self.trackable_fields.history.as_of(dt)
            return history_trackable_fields.price
        except TrackableSkuVariant.DoesNotExist:
            return None

    def get_regionalized_price(
        self,
        region: RegionLikeObject | None = None,
        currency: Currency | None = None,
        region_calculations_object: RegionCalculationsObject | None = None,
    ) -> Decimal:
        if not region_calculations_object:
            region_calculations_object = RegionCalculationsObject(region)

        return region_calculations_object.calculate_regionalized(self.price)

    def get_item_url(self):
        return self.get_absolute_url()

    def get_absolute_url(self):
        translated_category = self.sku_category.translated_name
        normalized_category = unidecode(str(translated_category))
        normalized_category = unidecode(str(translated_category))
        return reverse(
            'front-product-skuvariant',
            args=(normalized_category.lower(), self.pk),
            urlconf='frontend_cms.frontend_urls',
        )

    @cached_property
    def attribute_mapper(self) -> dict[str, Any]:
        return {item.attribute.name: item.get_value() for item in self.attributes.all()}

    def get_shelf_price_as_number(self, *args, **kwargs) -> Decimal:
        return self.price

    @property
    def can_be_added_to_wishlist(self) -> bool:
        return self.sku.sku_category.can_be_added_to_wishlist

    @property
    def is_sku_product(self) -> bool:
        return True

    @property
    def color(self) -> AttributeValueSchema | None:
        if color := str(self.attribute_mapper.get('color')):
            return AttributeValueSchema(
                value=color, translated_value=str(_(str(color)))
            )

    @property
    def fabric(self) -> AttributeValueSchema | None:
        if fabric := str(self.attribute_mapper.get('fabric')):
            return AttributeValueSchema(
                value=fabric, translated_value=str(_(str(fabric)))
            )

    def get_app_deeplink(self):
        return f'tylkoapp://skuvariant/{self.id}/'

    def get_delivery_time_days(
        self, *args, region_name: str | None = None, **kwargs
    ) -> int:
        from skus.services import LogisticShippingConfigsServiceProtocol

        logistic_shipping_configs_service = inject.instance(
            LogisticShippingConfigsServiceProtocol
        )
        delivery_time_days = logistic_shipping_configs_service.get_transit_time_max(
            country=region_name
        )
        return delivery_time_days or 0

    def get_delivery_time_max_week(self, *args, **kwargs):
        return 0

    @property
    def is_type_03_variant(self) -> bool:
        if self.is_sample:
            if shelf_type := self.attribute_mapper.get('shelf_type'):
                return shelf_type == ShelfType.TYPE03
        return False

    @property
    def furniture_type(self):
        return self.get_furniture_type()

    def get_furniture_type(self) -> str:
        return Furniture.skuvariant

    def get_weight_from_cached_serialization(self):
        # TODO: unify
        return self.get_weight()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # make sure the task gets fresh version of the object
        transaction.on_commit(lambda: send_sku_refresh_event.delay(self.id))

    def get_weight(self):
        return float(self.weight) if self.weight else 0.0

    def get_accurate_weight_gross(self):
        return self.get_weight()

    def get_dimensions(self):
        ds = Dimensions()
        ds.set_dimension(Dimensions.DimensionType.HEIGHT, self.get_height())
        ds.set_dimension(Dimensions.DimensionType.WIDTH, self.get_width())
        ds.set_dimension(Dimensions.DimensionType.DEPTH, self.get_depth())
        return ds

    def get_delivery_time_summary(self):
        return []

    def get_delivery_time_weeks_range(self):
        return {'min': 1, 'max': 1}

    def get_pretty_id_name(self) -> str:
        return self.get_furniture_type()

    def get_variant(self):
        if self.is_sample:
            return 'SampleBox: {}'.format(self.name.upper())
        return self.name
