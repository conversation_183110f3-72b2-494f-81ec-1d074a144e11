import os
import typing

from datetime import (
    date,
    datetime,
    timedelta,
)
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from django.core.paginator import Paginator
from django.db import transaction
from django.utils import timezone

from celery import shared_task
from celery.utils.log import get_task_logger
from openpyxl import Workbook
from openpyxl.writer.excel import save_workbook

from custom.enums import Furniture
from custom.utils.emails import send_html_mail
from custom.utils.exports import dump_list_as_csv
from invoice.choices import (
    InvoiceSource,
    InvoiceStatus,
)
from invoice.models import (
    Invoice,
    InvoiceDomestic,
    SymfoniaFConfiguration,
)
from invoice.reporting import (
    REPORTING_HEADER,
    get_reporting_dict,
)
from invoice.symfonia import generate_date_for_symfonia_export
from orders.enums import OrderStatus
from orders.models import Order

logger = get_task_logger(__name__)


class LogisticOrderSentDeliveryDate(typing.NamedTuple):
    sent_to_customer: typing.Union[date, str]
    delivered_date: typing.Union[date, str]


def get_sale_date_from_invoice_or_logistic_order(invoice, logistic_orders):
    logistic_order = logistic_orders[0] if logistic_orders else None
    return logistic_order.delivered_date if logistic_order else invoice.issued_at.date()


def get_order_type_prefix(order_type):
    # j-jetty w-watty s-sample box, empty for old logistic orders
    return order_type[0] if order_type else ''


def get_delivery_date_for_type(delivered_dates_by_type, furniture_type):
    for_type = delivered_dates_by_type.get(furniture_type.value)
    return for_type.delivered_date if for_type else ''


def format_sent_to_customer_dates(logistic_order_dates_by_type):
    return ' '.join(
        [
            f'{get_order_type_prefix(order_type)}:{dates.sent_to_customer}'
            for order_type, dates in logistic_order_dates_by_type.items()
            if dates.sent_to_customer
        ]
    )


def group_logistic_order_dates_by_order_type(logistic_orders, order):
    logistic_order_dates_by_type = {}
    for logistic_order in logistic_orders:
        if order.status == OrderStatus.DELIVERED and not logistic_order.delivered_date:
            continue
        order_type = logistic_order.order_type
        logistic_order_dates_by_type[order_type] = LogisticOrderSentDeliveryDate(
            logistic_order.sent_to_customer or '', logistic_order.delivered_date or ''
        )
    return logistic_order_dates_by_type


def generate_pdf_and_send_invoice(invoice):
    if invoice.order.is_klarna_payment():
        invoice.create_pdf()
        return

    elif invoice.order.is_free() and not invoice.order.is_full_barter_deal():
        return

    if invoice.sent_invoice_at:
        logger.debug('Invoice for order_id=%s already sent!', invoice.order.id)
        return

    invoice.send_invoice(invoice.order)


@shared_task
def generate_sales_report_and_send(invoice_filter_kwargs, email):
    headers = (
        'Nr Fry',
        'Data fry',
        'Powód korekty',
        'Kwota netto PLN',
        'VAT PLN',
        'Brutto PLN',
        'Kwota netto Waluta',
        'VAT Waluta',
        'Brutto Waluta',
        'Eco Fee EUR',
        'Waluta',
        'Kurs',
        'Data exportu do Sage',
        'Konto Sage1 interfacem',
        'Konto Sage2 interfacem',
        'order id',
        'Liczba logistic order',
        'Data sent to customer',
        'Data dostawy Sample Box',
        'Data dostawy Jetty',
        'Data dostawy Watty',
        'Data zmiany statusu na delivered',
        'Data zwrotu',
        'Status orderu',
        'Data księgowej sprzedaży',
    )

    invoices = Invoice.objects.filter(**invoice_filter_kwargs)
    invoices = (
        invoices.select_related(
            'order',
            'order__region',
            'order__region__currency',
        )
        .prefetch_related(
            'invoice_items',
            'order__items',
            'order__transactions',
        )
        .order_by('id')
    )

    wb = Workbook()
    ws = wb.active
    ws.append(headers)
    for invoice in invoices:
        invoice_dict = invoice.to_dict()
        symfonia_order, _ = generate_date_for_symfonia_export(invoice)

        document_type = symfonia_order['typ_dk']
        TWOPLACES = Decimal(10) ** -2  # noqa: N806
        vat_rate = Decimal(invoice_dict['vat_rate']).quantize(TWOPLACES)
        if document_type.lower() in ['wdt', 'dex']:
            dk = document_type
        else:
            dk = document_type.split('_')[0]
            if vat_rate in [
                Decimal('0.23').quantize(TWOPLACES),
                Decimal(0),
            ]:
                dk = 'F_{}'.format(dk)
            else:
                dk = 'F2{}'.format(dk)

        fk = SymfoniaFConfiguration.objects.filter(document_type=dk).last()
        child_orders = invoice.order.get_child_orders_without_complaints()
        if child_orders.exists():
            logistic_orders = []
            for child_order in child_orders:
                logistic_orders.extend(child_order.logistic_info)
        else:
            logistic_orders = invoice.order.logistic_info

        logistic_orders = sorted(
            logistic_orders, key=lambda lo: lo.delivered_date or date.min, reverse=True
        )

        logistic_order_dates_by_type = group_logistic_order_dates_by_order_type(
            logistic_orders, invoice.order
        )
        changed_to_delivered_date = None

        order_items = filter(
            lambda order_item: (
                order_item.free_return
                and order_item.free_return.finished_at
                and not order_item.free_return.aborted_at
            ),
            invoice.order.items.all(),
        )
        order_items = sorted(
            order_items,
            key=lambda order_item: order_item.free_return.finished_at,
        )

        free_return_date = next(iter(order_items), None)
        if free_return_date:
            free_return_date = free_return_date.free_return.finished_at

        net_pln = invoice_dict['net_value_in_pln']
        total_pln = invoice_dict['total_value_in_pln']
        net_value = invoice_dict['net_value']
        total_value = invoice_dict['total_value']

        is_invoice_for_france = invoice.order.country == 'france'
        recycle_tax_value = (
            invoice_dict['recycle_tax_value']
            if is_invoice_for_france and 'recycle_tax_value' in invoice_dict
            else Decimal('0.0')
        )

        if invoice.status == InvoiceStatus.CORRECTING:
            diff = invoice.to_diff_dict()
            net_value = diff['net_value']
            total_value = diff['total_value']
            net_pln = (net_value * invoice_dict['exchange_rate']).quantize(
                Decimal('.01'), rounding=ROUND_HALF_UP
            )
            total_pln = (total_value * invoice_dict['exchange_rate']).quantize(
                Decimal('.01'), rounding=ROUND_HALF_UP
            )
            if is_invoice_for_france:
                recycle_tax_value = diff['recycle_tax_value']

        ws.append(
            [
                invoice.pretty_id,
                invoice.issued_at,
                invoice.corrected_notes,
                net_pln,
                total_pln - net_pln,
                total_pln,
                net_value,
                total_value - net_value,
                total_value,
                recycle_tax_value,
                symfonia_order['waluta'],
                invoice_dict['exchange_rate'],
                invoice.exported_to_symfonia_f,
                getattr(fk, 'account', None),
                getattr(fk, 'vat_account', None),
                invoice.order.pk,
                len(logistic_orders),
                format_sent_to_customer_dates(logistic_order_dates_by_type),
                get_delivery_date_for_type(
                    logistic_order_dates_by_type, Furniture.sample_box
                ),
                get_delivery_date_for_type(
                    logistic_order_dates_by_type, Furniture.jetty
                ),
                get_delivery_date_for_type(
                    logistic_order_dates_by_type, Furniture.watty
                ),
                changed_to_delivered_date,
                free_return_date,
                invoice.order.get_status_display(),
                get_sale_date_from_invoice_or_logistic_order(invoice, logistic_orders),
            ]
        )

    xlsx_report_file = '/tmp/sales_report_{}.xlsx'.format(  # noqa: S108
        datetime.today().strftime('%Y-%m-%d')
    )
    save_workbook(wb, xlsx_report_file)

    send_html_mail(
        'mail_logistics_notification.html',
        'Sales report ready',
        context={
            'lines': [
                'Report ready',
            ]
        },
        to=email,
        attachment=xlsx_report_file,
    )

    os.remove(xlsx_report_file)


@shared_task
def export_whole_invoices(user_email: str, queryset_pks: list[int]) -> None:
    result = []
    reporting_keys = REPORTING_HEADER
    headers = reporting_keys + ['Logistic sent to customer']  # noqa: RUF005
    ignored_keys = [
        'Adres Dostawy',
        'Nazwa odbiorcy',
        'Nazwa firmy',
        'Adres odbiorcy',
        'Produkt',
    ]
    queryset = (
        Invoice.objects.filter(pk__in=queryset_pks)
        .order_by('order_id')
        .select_related('order')
    )

    for page in Paginator(queryset, 100):
        for invoice in page.object_list:
            report_dict = get_reporting_dict(invoice)
            sent_to_customer_date = ''
            if (
                invoice.order.logistic_info
                and invoice.order.logistic_info[-1].sent_to_customer
            ):
                sent_to_customer_date = invoice.order.logistic_info[
                    -1
                ].sent_to_customer.strftime('%d/%m/%Y')
            csv_row = []
            for key in reporting_keys:
                current_dict_str_key = str(report_dict[key])
                if key not in ignored_keys and '.' in current_dict_str_key:
                    csv_row.append(current_dict_str_key.replace('.', ','))
                else:
                    csv_row.append(report_dict[key])
            csv_row.append(sent_to_customer_date)
            result.append(csv_row)

    dump_list_as_csv(
        result,
        output='invoices_by_lines-{}.csv'.format(timezone.now().strftime('%Y_%m_%d')),
        mail=[user_email],
        delimiter=';',
        headers=headers,
        mail_subject='Invoices Report',
    )


@shared_task
def create_proforma_invoice_task(order_id: int) -> None:
    order = Order.objects.get(pk=order_id)
    order.create_proforma_invoice(source=InvoiceSource.RETOOL)


@shared_task
def create_pdf_and_send_to_client():
    last_24_hours = timezone.now() - timedelta(hours=24)
    invoices = Invoice.objects.select_for_update().filter(
        order__paid_at__gte=last_24_hours,
        pdf__in=['', None],
    )
    domestic_invoices = InvoiceDomestic.objects.select_for_update().filter(
        order__paid_at__gte=last_24_hours,
        pdf__in=['', None],
    )
    with transaction.atomic():
        for invoice in invoices.order_by('id'):
            generate_pdf_and_send_invoice(invoice)
        for domestic_invoice in domestic_invoices.order_by('id'):
            generate_pdf_and_send_invoice(domestic_invoice)
