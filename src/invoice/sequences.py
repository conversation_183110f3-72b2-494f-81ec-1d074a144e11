import logging

from datetime import datetime

from django.db.models import Q
from django.db.transaction import atomic
from django.utils import timezone

from custom.constants import (
    VAT_EU,
    VAT_NORMAL,
)
from custom.models import Countries
from regions.constants import OTHER_REGION_NAME

from .choices import (
    InvoiceChangeScope,
    InvoiceStatus,
    NumerationType,
)
from .constants import (
    SEQUENCE_MAJOR_REGION_VAT_PREFIX,
    SEQUENCE_MAJOR_UK_PREFIX,
    SEQUENCE_MINOR_CORRECTING_PREFIX,
    SEQUENCE_MINOR_OTHER_PREFIX,
    SEQUENCE_MINOR_PROFORMA_PREFIX,
)
from .models import (
    Invoice,
    InvoiceCorrectionChange,
    InvoiceSequence,
)

logger = logging.getLogger('invoice')

ADDITIONAL_COUNTRIES_TO_CODE = {
    'usa': 'US',
    'japan': 'JPN',
    'monaco': 'MCO',
    'hong kong': 'HKG',
    'canada': 'CAN',
    'kuwait': 'KWT',
    'turkey': 'TUR',
    'australia': 'AUS',
}


def get_numeration_type_and_prefix(invoice: 'Invoice') -> tuple[NumerationType, str]:
    if invoice.order.is_united_kingdom():
        return NumerationType.INV, SEQUENCE_MAJOR_UK_PREFIX
    # individual client should be issued by a region entity
    elif invoice.order.region_vat and invoice.order.vat_type == VAT_NORMAL:
        return NumerationType.RV, SEQUENCE_MAJOR_REGION_VAT_PREFIX
    return NumerationType.NORMAL, ''


def get_minor_prefix(invoice: 'Invoice') -> str:
    if invoice.status == InvoiceStatus.CORRECTING:
        return SEQUENCE_MINOR_CORRECTING_PREFIX
    elif invoice.status == InvoiceStatus.ENABLED:
        return ''
    elif invoice.status == InvoiceStatus.ENABLED_VAT_REGION_CORRECTION:
        return ''
    elif invoice.status == InvoiceStatus.PROFORMA:
        return SEQUENCE_MINOR_PROFORMA_PREFIX
    return SEQUENCE_MINOR_OTHER_PREFIX


def is_proforma_sequence(invoice: 'Invoice') -> bool:
    return invoice.status == InvoiceStatus.PROFORMA or (
        invoice.pretty_id and 'PROFORMA' in invoice.pretty_id
    )


def get_invoice_number_in_year(invoice: 'Invoice') -> int | None:
    last_correction_change = InvoiceCorrectionChange.objects.filter(
        correction_type=InvoiceChangeScope.INVOICE_NUMBER,
        correcting_invoice__corrected_invoice=invoice,
    ).last()
    if last_correction_change is None and is_proforma_sequence(invoice):
        return 0

    try:
        return int(
            invoice.pretty_id.replace('RV/', '').split('/')[0].replace('FKS', '')
        )
    except IndexError:
        logger.warning(
            'Invoice id=%s has odd pretty id: %s', invoice.id, invoice.pretty_id
        )
        return None
    except ValueError:
        logger.warning(
            'Old invoice in new year, check %s %s', invoice.id, invoice.pretty_id
        )
        return None


def generate_pretty_id_db_sequence(invoice: 'Invoice', issued_at=None) -> str:
    now = timezone.now()
    if issued_at:
        issue_date = issued_at
    elif invoice.issued_at:
        issue_date = invoice.issued_at
    else:
        issue_date = now

    numeration_type, prefix = get_numeration_type_and_prefix(invoice)

    invoice_country = (
        invoice.order.country if numeration_type != NumerationType.NORMAL else 'poland'
    )
    sequence = InvoiceSequence.objects.filter(
        country__name=invoice_country,
        invoice_type=invoice.status,
        numeration_type=numeration_type,
    )

    sequence_count = sequence.count()
    if sequence_count == 0 or sequence_count > 1:
        raise ValueError('Numeration sequence error')

    sequence = sequence.last()

    country = str(invoice.order.country.lower())
    if country in Countries.__members__:
        country_code = Countries.__members__[country].code
    elif (
        invoice.order.region.name == OTHER_REGION_NAME
        and country in ADDITIONAL_COUNTRIES_TO_CODE
    ):
        country_code = ADDITIONAL_COUNTRIES_TO_CODE[country]
    else:
        raise ValueError('Not supported country')

    minor_prefix = get_minor_prefix(invoice)
    if minor_prefix:
        prefix += minor_prefix

    with atomic():
        if now.month == issue_date.month and now.year == issue_date.year:
            invoice_number = sequence.get_next_current_month_sequence()
        else:
            invoice_number = sequence.get_next_chosen_date_sequence(issue_date)

    return sequence.pretty_id_template.format(
        invoice_number=invoice_number,
        order_id=invoice.order.id,
        country_code=country_code,
        issued_date=issue_date.strftime('%m/%Y'),
        prefix=prefix,
    )


def generate_pretty_id_from_previous(invoice: 'Invoice', issue_date: datetime) -> str:
    invoices_from_selected_year_month = (
        Invoice.objects.filter(
            Q(issued_at__year=issue_date.year)
            | Q(corrected_issued_at__year=issue_date.year)
        )
        .filter(
            Q(issued_at__month=issue_date.month)
            | Q(corrected_issued_at__month=issue_date.month)
        )
        .exclude(pk=invoice.pk)
        .order_by('issued_at', 'id')
    )

    if invoice.status == InvoiceStatus.CORRECTING:
        invoices_from_selected_year_month = invoices_from_selected_year_month.filter(
            status=InvoiceStatus.CORRECTING
        )
    elif invoice.status == InvoiceStatus.CORRECTING_DRAFT:
        invoices_from_selected_year_month = invoices_from_selected_year_month.filter(
            status=InvoiceStatus.CORRECTING_DRAFT
        )
    else:
        invoices_from_selected_year_month = invoices_from_selected_year_month.filter(
            status=InvoiceStatus.ENABLED
        )

    prefix = ''
    # checking vat type - only normal can have region numeration
    if invoice.order.region_vat and invoice.order.vat_type == VAT_NORMAL:
        if invoice.order.is_united_kingdom():
            invoices_from_selected_year_month = (
                invoices_from_selected_year_month.filter(order__region_vat=True)
                .filter(order__country=invoice.order.country)
                .filter(pretty_id__startswith='INV')
            )
            prefix = SEQUENCE_MAJOR_UK_PREFIX
        else:
            invoices_from_selected_year_month = (
                invoices_from_selected_year_month.filter(order__region_vat=True)
                .filter(order__country=invoice.order.country)
                .filter(pretty_id__startswith='RV')
            )
            prefix = SEQUENCE_MAJOR_REGION_VAT_PREFIX
    else:
        invoices_from_selected_year_month = invoices_from_selected_year_month.filter(
            Q(order__region_vat=False)
            | Q(Q(order__region_vat=True) & Q(order__vat_type=VAT_EU))
        )
    last_inv = (
        invoices_from_selected_year_month.exclude(pretty_id='')
        .exclude(pretty_id=None)
        .order_by('pk')
        .last()
    )
    if not last_inv or not str(get_invoice_number_in_year(last_inv)).isdigit():
        invoice_no = 1
    else:
        invoice_no = get_invoice_number_in_year(last_inv) + 1

    try:
        country_code = Countries.__members__[str(invoice.order.country.lower())].code
    except Exception:
        country_code = '??'

    if invoice.status == InvoiceStatus.PROFORMA:
        return '{}{}/{}/{:d}/{}'.format(
            prefix,
            'PROFORMA',
            issue_date.strftime('%m/%Y'),
            invoice.order.id,
            country_code,
        )
    elif invoice.status == InvoiceStatus.CORRECTING:
        return '{}FKS{:4d}/{}/{:d}/{}'.format(
            prefix,
            invoice_no,
            issue_date.strftime('%m/%Y'),
            invoice.order.id,
            country_code,
        )
    elif invoice.status == InvoiceStatus.CORRECTING_DRAFT:
        return 'DRAFT'
    return '{}{:5d}/{}/{:d}/{}'.format(
        prefix,
        invoice_no,
        issue_date.strftime('%m/%Y'),
        invoice.order.id,
        country_code,
    )
