import datetime

from decimal import Decimal
from unittest import mock

from django.utils import timezone

import pytest

from custom.models import Countries
from invoice.choices import (
    InvoiceItemType,
    InvoiceStatus,
    VatType,
)
from invoice.constants import HTS_CODE
from invoice.models import (
    Invoice,
    InvoiceDomestic,
)
from orders.enums import VatChoices


@pytest.mark.django_db
class TestInvoice:
    def test_to_dict_should_return_same_value_when_double_called(
        self,
        invoice_factory,
    ):
        invoice = invoice_factory(
            pretty_id='test/1/2',
        )
        first_to_dict = invoice.to_dict()
        invoice.cached_to_dict = {}
        invoice.save(update_fields=['cached_to_dict'])
        invoice.refresh_from_db()

        second_to_dict = invoice.to_dict()

        assert first_to_dict == second_to_dict

    def test_to_dict_should_add_assembly_service_to_correction_when_added(
        self,
        mocker,
        order_item,
        invoice_factory,
        invoice_item_factory,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')

        normal_invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order__items=None,
        )
        invoice_item_type_item = invoice_item_factory(
            invoice=normal_invoice,
            net_value=321,
            item_type=InvoiceItemType.ITEM,
            order_item=order_item,
        )

        normal_invoice_serialized = normal_invoice.to_dict()

        correction_invoice = invoice_factory(
            corrected_invoice=normal_invoice,
            pretty_id='correction/1/2',
            status=InvoiceStatus.CORRECTING,
            order__items=None,
        )
        invoice_item_type_item.pk = None
        invoice_item_type_item.invoice = correction_invoice
        invoice_item_type_item.save()

        assembly_service_invoice_item = invoice_item_factory(
            invoice=correction_invoice,
            net_value=79,
            item_type=InvoiceItemType.ASSEMBLY,
        )
        correction_invoice_serialized = correction_invoice.to_dict()

        normal_invoice_items = normal_invoice_serialized['items']
        correction_invoice_items = correction_invoice_serialized['items']
        assert len(normal_invoice_items) == 1
        assert len(correction_invoice_items) == 2
        assert assembly_service_invoice_item.pk in [
            item['id'] for item in correction_invoice_items
        ]

    def test_get_vat_type_should_return_vat_eu_when_united_kingdom_country_and_order_item_vat_amount_neq_zero(  # noqa: E501
        self, mocker, order_factory, order_item_factory, invoice_factory
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')

        country_name = Countries.united_kingdom.name
        jetty_order = order_factory(
            country=country_name,
            region_total_price=Decimal('500.0'),
            items=[],
        )
        order_item_factory(
            order=jetty_order, vat_amount=Decimal('10.00'), is_jetty=True
        )
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order=jetty_order,
        )

        vat_type = invoice.get_vat_type()

        assert vat_type == VatType.EU

    @pytest.mark.parametrize(
        'country_name',
        [
            Countries.switzerland.name,
            Countries.norway.name,
        ],
    )
    def test_get_vat_type_should_return_vat_switzerland_when_proper_country_and_order_item_vat_amount_neq_zero(  # noqa: E501
        self, mocker, order_factory, order_item_factory, invoice_factory, country_name
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')

        jetty_order = order_factory(country=country_name, items=[])
        order_item_factory(
            order=jetty_order, vat_amount=Decimal('10.00'), is_jetty=True
        )
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order=jetty_order,
        )

        vat_type = invoice.get_vat_type()

        assert vat_type == VatType.SWITZERLAND

    def test_change_delivery_data_and_create_correction_later(
        self, mocker, order_factory, invoice_factory
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')
        mock_pdf_generation = mocker.patch('invoice.models.Invoice.create_pdf')

        original_street_name = 'original'
        updated_street_name = 'new'

        order = order_factory(
            country='poland', street_address_1=original_street_name, items=[]
        )
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order=order,
        )
        invoice.to_dict()  # save cache

        # change delivery data
        order.street_address_1 = updated_street_name
        order.save()

        correction_invoice = invoice_factory(
            corrected_invoice=invoice,
            order=order,
            pretty_id='correction/1/2',
            status=InvoiceStatus.CORRECTING,
        )
        correction_cache = correction_invoice.to_dict()

        # this should not happen, we should fix it
        assert (
            correction_cache['delivery_address']['street_address_1']
            == updated_street_name
        )

        # workaround for now
        correction_invoice.regenerate_pdf_with_original_address()
        refreshed_correction_cache = correction_invoice.to_dict()

        mock_pdf_generation.assert_called()
        assert (
            refreshed_correction_cache['delivery_address']['street_address_1']
            == original_street_name
        )

    def test_get_vat_type_should_return_vat_eu_when_united_kingdom_with_samples_only_and_order_item_vat_amount_neq_zero(  # noqa: E501
        self,
        mocker,
        order_factory,
        order_item_factory,
        invoice_factory,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')

        sample_order = order_factory(
            country=Countries.united_kingdom.name,
            region_total_price=Decimal('10.0'),
            items=[],
        )
        order_item_factory(
            order=sample_order,
            vat_amount=Decimal('0.17'),
            is_sample_box=True,
        )
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order=sample_order,
        )

        vat_type = invoice.get_vat_type()

        assert vat_type == VatType.EU

    def test_get_vat_type_should_return_vat_eu_when_order_item_vat_amount_eq_zero(
        self,
        mocker,
        order_factory,
        order_item_factory,
        invoice_factory,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')

        order = order_factory(items=[])
        order_item_factory(order=order, vat_amount=Decimal('0.0'))
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order=order,
        )

        vat_type = invoice.get_vat_type()

        assert vat_type == VatType.EU

    def test_invoice_sent_at_should_return_uk_when_should_hide_after_release(
        self,
        mocker,
        invoice_factory,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order__country=Countries.united_kingdom.name,
            sent_invoice_at=None,
            issued_at=datetime.datetime(2024, 7, 30, 11, 0, 0, tzinfo=datetime.UTC),
            is_domestic=False,
        )
        sent_at = invoice.invoice_sent_at()
        assert 'UK' == sent_at

    def test_invoice_sent_at_should_return_uk_when_should_hide_before_release(
        self,
        mocker,
        invoice_factory,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order__country=Countries.united_kingdom.name,
            sent_invoice_at=None,
            issued_at=datetime.datetime(2024, 6, 30, 11, 0, 0, tzinfo=datetime.UTC),
            is_domestic=False,
        )
        sent_at = invoice.invoice_sent_at()
        assert 'not yet' == sent_at

    def test_invoice_sent_at_should_return_sent_at_when_already_sent(
        self,
        mocker,
        invoice_factory,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')
        sent_at = datetime.datetime(2024, 10, 10, 12, 0)
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order__country=Countries.united_kingdom.name,
            sent_invoice_at=sent_at,
            is_domestic=False,
        )
        sent_at = invoice.invoice_sent_at()
        assert sent_at == sent_at

    @pytest.mark.nbp
    def test_get_domestic_invoice_should_filter_proforma_by_issued_at(
        self,
        mocker,
        country_factory,
        region_factory,
        invoice_factory,
    ):
        united_kingdom_region = region_factory(united_kingdom=True)
        united_kingdom_country = country_factory(
            united_kingdom=True, vat=Decimal('0.20')
        )
        mocker.patch('invoice.models.Invoice._validate_correction')
        proforma_first = invoice_factory(
            pretty_id='PROFORMA/08/2024/251321603/UK',
            status=InvoiceStatus.PROFORMA,
            order__country=united_kingdom_country.name,
            order__region=united_kingdom_region,
            sent_invoice_at=None,
            issued_at=datetime.datetime(2024, 6, 30, 11, 0, 0, tzinfo=datetime.UTC),
            is_domestic=False,
        )
        proforma_second = invoice_factory(
            pretty_id='PROFORMA/08/2024/251321603/UK',
            status=InvoiceStatus.PROFORMA,
            order__country=united_kingdom_country.name,
            order__region=united_kingdom_region,
            sent_invoice_at=None,
            issued_at=datetime.datetime(2024, 6, 30, 12, 0, 0, tzinfo=datetime.UTC),
            is_domestic=False,
        )

        proforma_first_domestic = (
            InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(
                proforma_first
            )
        )
        proforma_second_domestic = (
            InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(
                proforma_second
            )
        )

        assert proforma_first.get_domestic_invoice() == proforma_first_domestic
        assert proforma_second.get_domestic_invoice() == proforma_second_domestic

    @pytest.mark.parametrize(
        'invoice_status',
        [
            InvoiceStatus.ENABLED,
            InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
            InvoiceStatus.DISABLED,
            InvoiceStatus.PROFORMA,
            InvoiceStatus.CANCELLED,
            InvoiceStatus.ADDITIONAL_ES,
        ],
    )
    def test_to_diff_dict_should_return_none_when_not_correcting(
        self, invoice_factory, invoice_status
    ):
        invoice = invoice_factory(
            pretty_id='test/1/2',
            status=invoice_status,
        )
        diff_dict = invoice.to_diff_dict()
        assert diff_dict is None

    @mock.patch(
        'invoice.models.Invoice.get_exchange_date_and_rate',
        return_value=(None, Decimal(1)),
    )
    @mock.patch('invoice.models.Invoice.should_create_items', return_value=False)
    @mock.patch(
        'orders.services.vat_details.VatDetailsGetter._validate_vat_number',
        return_value=True,
    )
    def test_get_correction_differences_should_calculate_total_diff_when_zero_vat(
        self,
        mocked_validated_vat,
        mocked_should_create_items,
        mocked_get_exchange_date_and_rate,
        order_factory,
        order_item_factory,
        region_factory,
        invoice_item_factory,
        invoice_factory,
    ):
        germany = region_factory(germany=True)
        order = order_factory(
            vat_type=VatChoices.VAT_EU,
            region_vat=True,
            vat='test',
            country=Countries.germany.name,
            region=germany,
            items=None,
            region_total_price=Decimal('100.0'),
            region_total_price_net=Decimal('100.0'),
            region_promo_amount=Decimal('100.0'),
            region_promo_amount_net=Decimal('100.0'),
        )
        order_item = order_item_factory(
            order=order,
            is_jetty=True,
            price=Decimal('100.0'),
            region_price=Decimal('100.0'),
            price_net=Decimal('100.0'),
            region_price_net=Decimal('100.0'),
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
            region_promo_value=Decimal('0.0'),
        )

        invoice = invoice_factory(
            order=order,
            pretty_id='FKS0001/08/2024/162676361/DE',
            status=InvoiceStatus.ENABLED,
        )
        invoice_item = invoice_item_factory(
            invoice=invoice,
            order_item=order_item,
            net_price=Decimal('100.0'),
            discount_value=Decimal('0.0'),
            net_value=Decimal('100.0'),
            vat_amount=Decimal('0.0'),
            gross_price=Decimal('100.0'),
            vat_status=VatType.EU,
        )

        correction = invoice_factory(
            pretty_id='FKS0002/08/2024/162676361/DE',
            order=order,
            status=InvoiceStatus.CORRECTING,
            corrected_invoice=invoice,
        )

        invoice_item_factory(
            invoice=correction,
            order_item=order_item,
            net_price=Decimal('80.0'),
            discount_value=Decimal('0.0'),
            net_value=Decimal('80.0'),
            vat_amount=Decimal('0.0'),
            gross_price=Decimal('80.0'),
            vat_status=VatType.EU,
            corrected_invoice_item=invoice_item,
        )

        diff_dict = correction.get_correction_differences()
        totals = diff_dict['totals']
        assert totals['net_diff'] == Decimal('-20.00')
        assert totals['vat_diff'] == Decimal('-0.00')
        assert totals['gross_diff'] == Decimal('-20.00')

    @mock.patch('invoice.models.VatDetailsGetter')
    def test_get_vat_rate_should_return_from_order_when_not_null(
        self, vat_details_getter_mocked, order_factory, invoice_factory
    ):
        vat_getter = vat_details_getter_mocked.return_value
        vat_getter.get_vat_rate_for_display.return_value = Decimal('0.2')
        order = order_factory(vat_rate=Decimal('0.0'), country='germany')
        invoice = invoice_factory(pretty_id='123', order=order)
        vat_rate = invoice.get_vat_rate()
        assert vat_rate == Decimal('0.0')

    def test__create_items_should_create_when_single_quantity_no_services(
        self,
        order_factory,
        order_item_factory,
        region_factory,
    ):
        germany = region_factory(germany=True)
        order = order_factory(
            vat_type=VatChoices.VAT_EU,
            region_vat=True,
            vat_rate=Decimal('0.2'),
            country=Countries.germany.name,
            region=germany,
            items=None,
            assembly=False,
            region_total_price=Decimal('100.0'),
            region_total_price_net=Decimal('83.33'),
            region_promo_amount=Decimal('0.0'),
            region_promo_amount_net=Decimal('0.0'),
        )

        order_item_factory(
            order=order,
            is_jetty=True,
            quantity=1,
            region_price=Decimal('100.0'),
            region_price_net=Decimal('83.33'),
            region_vat_amount=Decimal('16.67'),
            region_promo_value=Decimal('0.0'),
            assembly_price=Decimal('0.0'),
            region_assembly_price=Decimal('0.0'),
            delivery_price=Decimal('0.0'),
            region_delivery_price=Decimal('0.0'),
        )

        now = timezone.now()
        invoice = Invoice.objects.create(
            order=order,
            pretty_id='INV/1/12/2024/DE',
            exchange_rate=Decimal('4.0'),
            exchange_date=now.date(),
            sell_at=now,
            issued_at=now,
            currency_symbol=order.get_region().currency.symbol,
        )
        item = invoice.invoice_items.filter(item_type=InvoiceItemType.ITEM)[0]

        assert item.quantity == 1
        assert item.net_price == Decimal('83.33')
        assert item.discount_value == Decimal('0.0')
        assert item.net_value == Decimal('83.33')
        assert item.vat_amount == Decimal('16.67')
        assert item.gross_price == Decimal('100.0')

        service_items = invoice.invoice_items.filter(
            item_type__in=[
                InvoiceItemType.DELIVERY,
                InvoiceItemType.ASSEMBLY,
                InvoiceItemType.FAST_TRACK,
                InvoiceItemType.SERVICE,
            ]
        )
        assert service_items.count() == 0

    def test__create_items_should_create_when_multiple_quantity_no_services(
        self,
        order_factory,
        order_item_factory,
        region_factory,
    ):
        germany = region_factory(germany=True)
        order = order_factory(
            vat_type=VatChoices.VAT_EU,
            region_vat=True,
            vat_rate=Decimal('0.2'),
            country=Countries.germany.name,
            region=germany,
            items=None,
            assembly=False,
            region_total_price=Decimal('200.0'),
            region_total_price_net=Decimal('166.66'),
            region_promo_amount=Decimal('0.0'),
            region_promo_amount_net=Decimal('0.0'),
        )

        order_item_factory(
            order=order,
            is_jetty=True,
            quantity=2,
            region_price=Decimal('100.0'),
            region_price_net=Decimal('83.33'),
            region_vat_amount=Decimal('16.67'),
            region_promo_value=Decimal('0.0'),
            assembly_price=Decimal('0.0'),
            region_assembly_price=Decimal('0.0'),
            delivery_price=Decimal('0.0'),
            region_delivery_price=Decimal('0.0'),
        )

        now = timezone.now()
        invoice = Invoice.objects.create(
            order=order,
            pretty_id='INV/1/12/2024/DE',
            exchange_rate=Decimal('4.0'),
            exchange_date=now.date(),
            sell_at=now,
            issued_at=now,
            currency_symbol=order.get_region().currency.symbol,
        )
        item = invoice.invoice_items.filter(item_type=InvoiceItemType.ITEM)[0]

        assert item.quantity == 2
        assert item.net_price == Decimal('83.33')
        assert item.discount_value == Decimal('0.0')
        assert item.net_value == Decimal('166.67')
        assert item.vat_amount == Decimal('33.33')
        assert item.gross_price == Decimal('200.0')

        service_items = invoice.invoice_items.filter(
            item_type__in=[
                InvoiceItemType.DELIVERY,
                InvoiceItemType.ASSEMBLY,
                InvoiceItemType.FAST_TRACK,
                InvoiceItemType.SERVICE,
            ]
        )
        assert service_items.count() == 0

    def test__create_items_should_create_when_multiple_quantity_with_services(
        self,
        order_factory,
        order_item_factory,
        region_factory,
    ):
        germany = region_factory(germany=True)
        order = order_factory(
            vat_type=VatChoices.VAT_EU,
            region_vat=True,
            vat_rate=Decimal('0.2'),
            country=Countries.germany.name,
            region=germany,
            items=None,
            assembly=True,
            region_total_price=Decimal('250.0'),
            region_total_price_net=Decimal('208.33'),
            region_promo_amount=Decimal('0.0'),
            region_promo_amount_net=Decimal('0.0'),
        )

        order_item_factory(
            order=order,
            is_jetty=True,
            quantity=2,
            region_price=Decimal('100.0'),
            region_price_net=Decimal('83.33'),
            region_vat_amount=Decimal('16.67'),
            region_promo_value=Decimal('0.0'),
            assembly_price=Decimal('25.0'),
            region_assembly_price=Decimal('25.0'),
            delivery_price=Decimal('0.0'),
            region_delivery_price=Decimal('0.0'),
        )

        now = timezone.now()
        invoice = Invoice.objects.create(
            order=order,
            pretty_id='INV/1/12/2024/DE',
            exchange_rate=Decimal('4.0'),
            exchange_date=now.date(),
            sell_at=now,
            issued_at=now,
            currency_symbol=order.get_region().currency.symbol,
        )
        item = invoice.invoice_items.filter(item_type=InvoiceItemType.ITEM)[0]

        assert item.quantity == 2
        assert item.net_price == Decimal('83.33')
        assert item.discount_value == Decimal('0.0')
        assert item.net_value == Decimal('166.67')
        assert item.vat_amount == Decimal('33.33')
        assert item.gross_price == Decimal('200.0')

        service_items = invoice.invoice_items.filter(
            item_type__in=[
                InvoiceItemType.DELIVERY,
                InvoiceItemType.ASSEMBLY,
                InvoiceItemType.FAST_TRACK,
                InvoiceItemType.SERVICE,
            ]
        )
        assert service_items.count() == 1

        assembly_item = service_items[0]
        assert assembly_item.quantity == 2
        assert assembly_item.net_price == Decimal('20.83')
        assert assembly_item.discount_value == Decimal('0.0')
        assert assembly_item.net_value == Decimal('41.67')
        assert assembly_item.vat_amount == Decimal('8.33')
        assert assembly_item.gross_price == Decimal('50.0')

    def test__create_items_should_create_when_multiple_items_quantity_with_services(
        self,
        order_factory,
        order_item_factory,
        region_factory,
    ):
        germany = region_factory(germany=True)
        order = order_factory(
            vat_type=VatChoices.VAT_EU,
            region_vat=True,
            vat_rate=Decimal('0.2'),
            country=Countries.germany.name,
            region=germany,
            items=None,
            assembly=True,
            region_total_price=Decimal('250.0'),
            region_total_price_net=Decimal('208.33'),
            region_promo_amount=Decimal('0.0'),
            region_promo_amount_net=Decimal('0.0'),
        )

        jetty_order_item = order_item_factory(
            order=order,
            is_jetty=True,
            quantity=3,
            region_price=Decimal('100.0'),
            region_price_net=Decimal('83.33'),
            region_vat_amount=Decimal('16.67'),
            region_promo_value=Decimal('0.0'),
            assembly_price=Decimal('25.0'),
            region_assembly_price=Decimal('25.0'),
            delivery_price=Decimal('0.0'),
            region_delivery_price=Decimal('0.0'),
        )

        watty_order_item = order_item_factory(
            order=order,
            is_watty=True,
            quantity=1,
            region_price=Decimal('80.0'),
            region_price_net=Decimal('66.67'),
            region_vat_amount=Decimal('13.37'),
            region_promo_value=Decimal('0.0'),
            assembly_price=Decimal('50.0'),
            region_assembly_price=Decimal('50.0'),
            delivery_price=Decimal('0.0'),
            region_delivery_price=Decimal('0.0'),
        )

        now = timezone.now()
        invoice = Invoice.objects.create(
            order=order,
            pretty_id='INV/1/12/2024/DE',
            exchange_rate=Decimal('4.0'),
            exchange_date=now.date(),
            sell_at=now,
            issued_at=now,
            currency_symbol=order.get_region().currency.symbol,
        )
        jetty_invoice_item = invoice.invoice_items.get(
            item_type=InvoiceItemType.ITEM,
            order_item=jetty_order_item,
        )

        assert jetty_invoice_item.quantity == 3
        assert jetty_invoice_item.net_price == Decimal('83.33')
        assert jetty_invoice_item.discount_value == Decimal('0.0')
        assert jetty_invoice_item.net_value == Decimal('250.0')
        assert jetty_invoice_item.vat_amount == Decimal('50.0')
        assert jetty_invoice_item.gross_price == Decimal('300.0')

        watty_invoice_item = invoice.invoice_items.get(
            item_type=InvoiceItemType.ITEM, order_item=watty_order_item
        )

        assert watty_invoice_item.quantity == 1
        assert watty_invoice_item.net_price == Decimal('66.67')
        assert watty_invoice_item.discount_value == Decimal('0.0')
        assert watty_invoice_item.net_value == Decimal('66.67')
        assert watty_invoice_item.vat_amount == Decimal('13.33')
        assert watty_invoice_item.gross_price == Decimal('80.0')

        service_items = invoice.invoice_items.filter(
            item_type__in=[
                InvoiceItemType.DELIVERY,
                InvoiceItemType.ASSEMBLY,
                InvoiceItemType.FAST_TRACK,
                InvoiceItemType.SERVICE,
            ]
        )
        assert service_items.count() == 2

        assembly_item_jetty = service_items.get(order_item=jetty_order_item)
        assert assembly_item_jetty.quantity == 3
        assert assembly_item_jetty.net_price == Decimal('20.83')
        assert assembly_item_jetty.discount_value == Decimal('0.0')
        assert assembly_item_jetty.net_value == Decimal('62.50')
        assert assembly_item_jetty.vat_amount == Decimal('12.50')
        assert assembly_item_jetty.gross_price == Decimal('75.00')

        assembly_item_watty = service_items.get(order_item=watty_order_item)
        assert assembly_item_watty.quantity == 1
        assert assembly_item_watty.net_price == Decimal('41.67')
        assert assembly_item_watty.discount_value == Decimal('0.0')
        assert assembly_item_watty.net_value == Decimal('41.67')
        assert assembly_item_watty.vat_amount == Decimal('8.33')
        assert assembly_item_watty.gross_price == Decimal('50.00')

    def test_should_display_difference_on_correction_should_return_false_when_poland(
        self,
        order_factory,
        invoice_factory,
        region_factory,
    ):
        poland = region_factory(poland=True)
        order = order_factory(
            vat_type=VatChoices.VAT_NORMAL,
            region_vat=False,
            vat_rate=Decimal('0.23'),
            country=Countries.poland.name,
            region=poland,
        )
        invoice = invoice_factory(pretty_id='test/1/2', order=order)
        assert not invoice.should_display_difference_on_correction()

    @pytest.mark.parametrize(
        ('order_item_type', 'expected'),
        [
            ('is_sample_box', False),
            ('is_jetty', True),
            ('is_sotty', True),
            ('is_watty', True),
        ],
    )
    def test_should_create_assembly_service_return_expected_when_assembly_enabled(
        self,
        order_item_type,
        expected,
        order_factory,
        invoice_factory,
        order_item_factory,
    ):
        order = order_factory(assembly=True, items=[])
        invoice = invoice_factory(pretty_id='test/1/2', order=order)
        order_item = order_item_factory(**{order_item_type: True}, order=order)
        assert invoice.should_create_assembly_service(order_item) is expected

    @pytest.mark.parametrize(
        ('order_item_type', 'covers_only', 'expected'),
        [
            ('is_sotty', True, False),
            ('is_sotty', False, True),
        ],
    )
    def test_should_create_assembly_service_return_expected_when_assembly_enabled_sotty(
        self,
        order_item_type,
        covers_only,
        expected,
        order_factory,
        invoice_factory,
        order_item_factory,
        sotty_factory,
    ):
        order = order_factory(assembly=True, items=[])
        invoice = invoice_factory(pretty_id='test/1/2', order=order)
        order_item = order_item_factory(
            **{order_item_type: True},
            order=order,
            order_item=sotty_factory(covers_only=covers_only),
        )
        assert invoice.should_create_assembly_service(order_item) is expected

    @pytest.mark.parametrize(
        'order_item_type',
        ['is_sample_box', 'is_jetty', 'is_sotty', 'is_watty'],
    )
    def test_should_create_assembly_service_return_expected_when_assembly_disabled(
        self, order_item_type, order_factory, invoice_factory, order_item_factory
    ):
        order = order_factory(assembly=False, items=[])
        invoice = invoice_factory(pretty_id='test/1/2', order=order)
        sample_box = order_item_factory(is_sample_box=True, order=order)
        assert invoice.should_create_assembly_service(sample_box) is False

    @mock.patch('invoice.models.Invoice.should_create_items', return_value=False)
    def test_sum_invoice_items_gross_price_should_sum_gross_price_quantity_included(
        self,
        mocked_should_create_items,
        region_factory,
        order_factory,
        order_item_factory,
        invoice_factory,
        invoice_item_factory,
    ):
        germany = region_factory(germany=True)
        order = order_factory(
            country=Countries.germany.name,
            region=germany,
            items=None,
            region_total_price=Decimal('180.0'),
            region_promo_amount=Decimal('20.0'),
        )
        order_item = order_item_factory(
            order=order,
            is_jetty=True,
            region_price=Decimal('100.0'),
            region_promo_value=Decimal('10.0'),
            quantity=2,
        )

        invoice = invoice_factory(
            order=order,
            pretty_id='FKS0001/08/2024/162676361/DE',
            status=InvoiceStatus.ENABLED,
        )
        invoice_item_factory(
            invoice=invoice,
            quantity=2,
            order_item=order_item,
            discount_value=Decimal('10.0'),
            gross_price=Decimal('180.0'),
        )

        assert invoice.sum_invoice_items_gross_price() == Decimal('180.0')

    @mock.patch('invoice.models.Invoice.should_create_items', return_value=False)
    def test_get_sotty_items_should_exclude_covers_when_called(
        self,
        mocked_should_create_items,
        region_factory,
        order_factory,
        order_item_factory,
        sotty_factory,
        invoice_factory,
    ):
        germany = region_factory(germany=True)
        order = order_factory(
            country=Countries.germany.name,
            region=germany,
            items=None,
            region_total_price=Decimal('180.0'),
            region_promo_amount=Decimal('20.0'),
        )
        sotty_item = order_item_factory(
            order=order,
            is_sotty=True,
            region_price=Decimal('100.0'),
            region_promo_value=Decimal('10.0'),
            quantity=1,
        )
        order_item_factory(
            order=order,
            is_jetty=True,
            region_price=Decimal('100.0'),
            region_promo_value=Decimal('10.0'),
            quantity=1,
        )
        cover_furniture_item = sotty_factory(covers_only=True)
        order_item_factory(
            order=order,
            is_sotty=True,
            order_item=cover_furniture_item,
            region_price=Decimal('10.0'),
            region_promo_value=Decimal('1.0'),
            quantity=1,
        )
        order_item_factory(
            order=order,
            is_old_sofa_collection=True,
            region_price=Decimal('50.0'),
            region_promo_value=Decimal('5.0'),
            quantity=1,
        )

        invoice = invoice_factory(
            order=order,
            pretty_id='FKS0001/08/2024/162676361/DE',
            status=InvoiceStatus.ENABLED,
        )

        sotty_items = invoice.get_sotty_items()

        assert set([sotty_item.pk for sotty_item in sotty_items]) == {sotty_item.id}  # noqa: C403

    @pytest.mark.parametrize(
        ('status', 'issued_at', 'expected'),
        [
            (
                InvoiceStatus.ENABLED,
                datetime.datetime(year=2019, month=11, day=1),
                True,
            ),
            (
                InvoiceStatus.ENABLED,
                datetime.datetime(year=2019, month=9, day=1),
                False,
            ),
            (
                InvoiceStatus.CORRECTING,
                datetime.datetime(year=2019, month=11, day=1),
                True,
            ),
            (
                InvoiceStatus.CORRECTING,
                datetime.datetime(year=2019, month=9, day=1),
                False,
            ),
            (
                InvoiceStatus.PROFORMA,
                datetime.datetime(year=2019, month=11, day=1),
                False,
            ),
            (
                InvoiceStatus.CORRECTING_DRAFT,
                datetime.datetime(year=2019, month=11, day=1),
                False,
            ),
        ],
    )
    @mock.patch('invoice.models.Invoice._validate_correction')
    def test_is_db_sequence_should_return_proper_when_status_and_issue_date(
        self,
        mocked__validate_correction,
        status,
        issued_at,
        expected,
        invoice_factory,
    ):
        issued_at = issued_at.replace(tzinfo=timezone.utc)
        invoice = invoice_factory(
            pretty_id='test/1/2',
            status=status,
            issued_at=issued_at,
        )
        assert invoice.is_db_sequence(issued_at) is expected


@pytest.mark.django_db
class TestInvoiceItem:
    def test_get_hts_code_should_return_valid_by_furniture_type(
        self,
        order_item_factory,
        order,
        invoice_factory,
        invoice_item_factory,
    ):
        order_item = order_item_factory(
            order=order,
            is_jetty=True,
            price=Decimal('100.0'),
            region_price=Decimal('100.0'),
            price_net=Decimal('100.0'),
            region_price_net=Decimal('100.0'),
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
            region_promo_value=Decimal('0.0'),
        )
        invoice = invoice_factory(pretty_id='test/1/2', order=order)
        invoice_item = invoice_item_factory(
            invoice=invoice,
            item_type=InvoiceItemType.ITEM,
            order_item=order_item,
            quantity=1,
            net_price=Decimal('100.0'),
            net_value=Decimal('100.0'),
        )

        assert invoice_item.get_hts_code() == HTS_CODE
