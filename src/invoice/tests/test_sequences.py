from unittest.mock import patch

import pytest

from freezegun import freeze_time

from invoice.choices import NumerationType
from invoice.models import InvoiceSequence
from invoice.sequences import (
    generate_pretty_id_db_sequence,
    get_numeration_type_and_prefix,
)
from orders.choices import VatType
from orders.services.vat_details import VatDetailsGetter


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('country_code', 'b2type', 'expected_numeration_type', 'expected_prefix'),
    [
        ('pl', 'B2B', NumerationType.NORMAL, ''),
        ('pl', 'B2C', NumerationType.NORMAL, ''),
        ('de', 'B2B', NumerationType.NORMAL, ''),
        ('de', 'B2Binvalid', NumerationType.NORMAL, ''),
        ('de', 'B2C', NumerationType.RV, 'RV/'),
        ('dk', 'B2B', NumerationType.NORMAL, ''),
        ('dk', 'B2Binvalid', NumerationType.NORMAL, ''),
        ('dk', 'B2C', NumerationType.RV, 'RV/'),
        ('uk', 'B2B', NumerationType.INV, 'INV/'),
        ('uk', 'B2C', NumerationType.INV, 'INV/'),
        ('no', 'B2B', NumerationType.NORMAL, ''),
        ('no', 'B2C', NumerationType.NORMAL, ''),
        ('ch', 'B2B', NumerationType.NORMAL, ''),
        ('ch', 'B2C', NumerationType.NORMAL, ''),
    ],
)
def test_numeration_type_and_prefix(
    country_code,
    b2type,
    expected_numeration_type,
    expected_prefix,
    order_factory,
    invoice_factory,
    request,
):
    is_b2b = b2type.startswith('B2B')
    is_b2b_valid = b2type == 'B2B'
    region = request.getfixturevalue(f'region_{country_code}')

    vat = 'PL1234567890' if country_code == 'pl' else 'VATID123456'
    order = order_factory(
        country=region.country.name,
        region=region,
        vat=vat if is_b2b else None,
    )

    # from PriceCalculatorBase...
    vat_details = VatDetailsGetter(order)
    with patch.object(vat_details, '_validate_vat_number', return_value=is_b2b_valid):
        vat_type = VatDetailsGetter(order).vat_type
        order.region_vat = vat_type == VatType.LOCAL_VAT
        order.vat_type = vat_type.legacy_type

    # set faked pretty_id to prevent real sequence generation
    invoice = invoice_factory(order=order, pretty_id='faked_ID')
    numeration_type, prefix = get_numeration_type_and_prefix(invoice)
    assert numeration_type == expected_numeration_type
    assert prefix == expected_prefix


@pytest.mark.parametrize(
    ('country_code', 'b2type', 'expected_sequence_name', 'expected_pretty_id'),
    [
        (
            'pl',
            'B2B',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/PL',
        ),
        (
            'pl',
            'B2C',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/PL',
        ),
        (
            'de',
            'B2B',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/DE',
        ),
        (
            'de',
            'B2C',
            '_invoice_germany_1_0_2025_10',
            'RV/0017/10/2025/25353/DE',
        ),
        (
            'dk',
            'B2B',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/DK',
        ),
        (
            'dk',
            'B2C',
            '_invoice_denmark_1_0_2025_10',
            'RV/0017/10/2025/25353/DK',
        ),
        (
            'uk',
            'B2B',
            '_invoice_united_kingdom_3_0_2025_10',
            'INV/0017/10/2025/25353/UK',
        ),
        (
            'uk',
            'B2C',
            '_invoice_united_kingdom_3_0_2025_10',
            'INV/0017/10/2025/25353/UK',
        ),
        (
            'no',
            'B2B',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/NO',
        ),
        (
            'no',
            'B2C',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/NO',
        ),
        (
            'ch',
            'B2B',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/CH',
        ),
        (
            'ch',
            'B2C',
            '_invoice_poland_0_0_2025_10',
            '00017/10/2025/25353/CH',
        ),
    ],
)
@pytest.mark.django_db
@freeze_time('2025-10-05')
def test_sequence_and_pretty_id(
    country_code,
    b2type,
    expected_sequence_name,
    expected_pretty_id,
    order_factory,
    invoice_factory,
    invoice_sequence_factory,
    request,
):
    is_b2b = b2type.startswith('B2B')
    is_b2b_valid = b2type == 'B2B'
    region = request.getfixturevalue(f'region_{country_code}')
    poland = request.getfixturevalue('region_pl')
    vat = 'PL1234567890' if country_code == 'pl' else 'VATID12345'

    order = order_factory(
        id=25353,
        country=region.country.name,
        region=region,
        vat=vat if is_b2b else None,
    )
    # from PriceCalculatorBase...
    vat_details = VatDetailsGetter(order)
    with patch.object(vat_details, '_validate_vat_number', return_value=is_b2b_valid):
        vat_type = VatDetailsGetter(order).vat_type
        order.region_vat = vat_type == VatType.LOCAL_VAT
        order.vat_type = vat_type.legacy_type

    # set faked pretty_id to prevent real sequence generation
    invoice = invoice_factory(order=order, pretty_id='faked_ID')

    # prerequisites for sequence calculation
    numeration_type, _ = get_numeration_type_and_prefix(invoice)
    template = (
        '{prefix}{invoice_number:0>5}/{issued_date}/{order_id}/{country_code}'
        if numeration_type == NumerationType.NORMAL
        else '{prefix}{invoice_number:0>4}/{issued_date}/{order_id}/{country_code}'
    )
    original_sequence_filter = InvoiceSequence.objects.filter

    def sequence_filter_effect(*, country__name, invoice_type, numeration_type):
        if country__name == 'poland':
            country = poland.country
        elif country__name == region.country.name:
            country = region.country
        else:
            raise ValueError('Unknown country name')

        sequence = invoice_sequence_factory(
            country=country,
            invoice_type=invoice_type,
            numeration_type=numeration_type,
            pretty_id_template=template,
        )
        return original_sequence_filter(pk=sequence.pk)

    with (
        patch(
            'invoice.sequences.InvoiceSequence.objects.filter',
            side_effect=sequence_filter_effect,
        ),
        patch(
            'invoice.models.InvoiceSequence.get_next_current_month_sequence',
            return_value=17,
        ),
    ):
        pretty_id = generate_pretty_id_db_sequence(invoice)
        sequence = InvoiceSequence.objects.all().first()
        sequence_name = sequence._get_sequence_name()

    assert pretty_id == expected_pretty_id
    assert sequence_name == expected_sequence_name
