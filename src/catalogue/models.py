from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.validators import FileExtensionValidator
from django.db import models
from django.db.models import (
    Max,
    Q,
    QuerySet,
)

from cloudinary.models import CloudinaryField
from django_jsonform.models.fields import JSONField
from taggit.managers import TaggableManager

from catalogue.enums import (
    FurnitureAttributesEnum,
    HeightRange,
    ListingOrder,
    MainMarketsEnum,
    ProductLine,
    WidthRange,
)
from catalogue.schemas import (
    ORDER_SCHEMA,
    empty_order_schema,
)
from catalogue.services.attributes_assigner import AttributesAssigner
from catalogue.validators import (
    validate_image_size,
    validate_thumbnail_size,
)
from custom.enums import (
    Furniture,
    ShelfType,
    Type01Color,
)
from custom.utils.grid import (
    get_label_new,
    get_label_promo,
)
from feeds.models import FeedCategory
from gallery.enums import FurnitureCategory
from gallery.models import SampleBox
from gallery.services.category_configuration_validation import (
    validate_category_configuration,
)
from gallery.types import (
    CatalogItemType,
    FurnitureType,
)
from promotions.utils import get_active_promotion
from regions.services.limitations import LimitationService
from regions.types import RegionLikeObject
from skus.enums import SkuCategoryEnum
from skus.models import SkuCategory
from skus.models.abstracts import SkuCartAbstract
from skus.services import SkuStockService


class CatalogueEntryOrder(models.Model):
    """Model for ordering catalogue entries. Lower order shows first on plp."""

    # Base ordering
    order = models.IntegerField(db_index=True)
    category_order = models.IntegerField(null=True, blank=True)

    # Strategic ordering
    profit_netto_order = models.IntegerField(blank=True, null=True, db_index=True)
    profit_netto_category_order = models.IntegerField(blank=True, null=True)
    profit_netto_alt_order = models.IntegerField(blank=True, null=True)
    profit_netto_alt_category_order = models.IntegerField(blank=True, null=True)

    profit_netto_order_de = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_de = models.IntegerField(blank=True, null=True)
    profit_netto_order_fr = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_fr = models.IntegerField(blank=True, null=True)
    profit_netto_order_nl = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_nl = models.IntegerField(blank=True, null=True)
    profit_netto_order_uk = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_uk = models.IntegerField(blank=True, null=True)
    profit_netto_order_ch = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_ch = models.IntegerField(blank=True, null=True)
    profit_netto_order_se = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_se = models.IntegerField(blank=True, null=True)
    profit_netto_order_no = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_no = models.IntegerField(blank=True, null=True)
    profit_netto_order_dk = models.IntegerField(blank=True, null=True)
    profit_netto_category_order_dk = models.IntegerField(blank=True, null=True)

    # Test ordering
    test_order = models.IntegerField(null=True, blank=True, unique=True)
    test_category_order = models.IntegerField(null=True, blank=True)

    test_profit_netto_order = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order = models.IntegerField(blank=True, null=True)
    test_profit_netto_alt_order = models.IntegerField(
        blank=True,
        null=True,
        unique=True,
    )
    test_profit_netto_alt_category_order = models.IntegerField(blank=True, null=True)

    test_profit_netto_order_de = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_de = models.IntegerField(blank=True, null=True)
    test_profit_netto_order_fr = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_fr = models.IntegerField(blank=True, null=True)
    test_profit_netto_order_nl = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_nl = models.IntegerField(blank=True, null=True)
    test_profit_netto_order_uk = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_uk = models.IntegerField(blank=True, null=True)
    test_profit_netto_order_ch = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_ch = models.IntegerField(blank=True, null=True)
    test_profit_netto_order_se = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_se = models.IntegerField(blank=True, null=True)
    test_profit_netto_order_no = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_no = models.IntegerField(blank=True, null=True)
    test_profit_netto_order_dk = models.IntegerField(blank=True, null=True, unique=True)
    test_profit_netto_category_order_dk = models.IntegerField(blank=True, null=True)

    # non-standard
    profit_netto_dynamic_order = JSONField(
        schema=ORDER_SCHEMA, default=empty_order_schema
    )
    test_profit_netto_dynamic_order = JSONField(
        schema=ORDER_SCHEMA, default=empty_order_schema
    )

    class Meta:
        abstract = True


class CatalogueEntryImageBaseModel(models.Model):
    product_unreal_image_webp = models.ImageField(
        upload_to='catalogue/catalogue_entry/%Y/%m',
        max_length=200,
        blank=True,
        null=True,
        validators=[
            validate_image_size,
            FileExtensionValidator(['webp']),
        ],
    )
    product_unreal_thumbnail_image_webp = models.ImageField(
        upload_to='catalogue/catalogue_entry/%Y/%m',
        max_length=200,
        blank=True,
        null=True,
        validators=[
            validate_thumbnail_size,
            FileExtensionValidator(['webp']),
        ],
    )
    lifestyle_unreal_image_webp = models.ImageField(
        upload_to='catalogue/catalogue_entry/%Y/%m',
        max_length=200,
        blank=True,
        null=True,
        validators=[
            validate_image_size,
            FileExtensionValidator(['webp']),
        ],
    )
    real_lifestyle_image = models.ImageField(
        upload_to='catalogue/catalogue_entry/%Y/%m',
        max_length=200,
        blank=True,
        null=True,
        validators=[validate_image_size],
    )
    real_lifestyle_image_webp = models.ImageField(
        upload_to='catalogue/catalogue_entry/%Y/%m',
        max_length=200,
        blank=True,
        null=True,
    )
    rendered_lifestyle_image = models.ImageField(
        upload_to='catalogue/catalogue_entry/%Y/%m',
        max_length=200,
        blank=True,
        null=True,
    )
    rendered_lifestyle_image_webp = models.ImageField(
        upload_to='catalogue/catalogue_entry/%Y/%m',
        max_length=200,
        blank=True,
        null=True,
    )
    image_cloudinary = CloudinaryField(
        'Furniture image',
        blank=True,
        null=True,
    )

    class Meta:
        abstract = True


class CatalogueEntryQuerySet(models.QuerySet):
    def all_for_region(
        self, region: RegionLikeObject | None = None
    ) -> QuerySet['CatalogueEntry']:
        """
        Some products are not available in all regions.
        Use this method to exclude them for given region
        """
        qs = self
        if region is None:
            return qs

        qs = qs.exclude(sku_variant__excluded_regions=region)

        limitation_service = LimitationService(region=region)

        if not limitation_service.is_s01_available:
            qs = qs.exclude(shelf_type=ShelfType.SOFA_TYPE01)
        elif not limitation_service.is_corduroy_available:
            qs = qs.exclude(attributes__name=FurnitureAttributesEnum.CORDUROY.value)

        if not limitation_service.is_t03_available:
            qs = qs.exclude(
                category=FurnitureCategory.WARDROBE, shelf_type=ShelfType.TYPE03
            )

        return qs

    def get_fallback_recommendations(
        self,
        categories: list[str] | None,
        materials: list[int] | None,
        exclude_ids: list[int] | None,
        limit=8,
    ):
        excluded_types = {SkuCartAbstract.product_type, SampleBox.product_type}
        excluded_categories = {FurnitureCategory.COVER}
        q = Q()
        if categories:
            q &= Q(category__in=categories)
        if materials:
            q &= Q(material__in=materials)
        return (
            self.filter(q, enabled=True)
            .exclude(id__in=exclude_ids)
            .exclude(content_type__model__in=excluded_types)
            .exclude(category__in=excluded_categories)
            .order_by('?')[:limit]
        )

    def filter_by_feed_category(
        self, category: FeedCategory
    ) -> QuerySet['CatalogueEntry']:
        query = {'enabled': True}
        if category.furniture_category:
            query |= {'category': category.furniture_category}
        if furniture_tag := category.furniture_tag.all():
            query |= {
                'attributes__in': furniture_tag,
            }
        if sku_category := category.sku_category:
            query |= {
                'sku_category': sku_category,
            }
        return self.filter(**query).prefetch_related('furniture')

    def with_attribute_names(self) -> QuerySet['CatalogueEntry']:
        return self.annotate(
            attribute_names=ArrayAgg('attributes__name', distinct=True)
        )


class CatalogueEntry(CatalogueEntryOrder, CatalogueEntryImageBaseModel):
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='sotty')
            | models.Q(app_label='gallery', model='watty')
            | models.Q(app_label='skus', model='skuvariant')
        ),
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    furniture: CatalogItemType = GenericForeignKey('content_type', 'object_id')

    enabled = models.BooleanField(default=True, db_index=True)

    # filter fields
    attributes = TaggableManager()
    category = models.CharField(
        choices=FurnitureCategory.choices,
        max_length=31,
        blank=True,
        default='',
    )
    sku_category = models.ForeignKey(
        'skus.SkuCategory',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    shelf_type = models.IntegerField(choices=ShelfType.choices(), blank=True, null=True)
    material = models.IntegerField(blank=True, null=True)
    available_colors = models.JSONField(default=list)
    width = models.IntegerField(null=True)
    height = models.IntegerField(null=True)
    depth = models.IntegerField(null=True)
    price = models.IntegerField(null=True)

    # Business data fields
    profit = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )

    profit_netto = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_de = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_fr = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_nl = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_uk = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_ch = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_se = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_no = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )
    profit_netto_dk = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
    )

    objects = CatalogueEntryQuerySet.as_manager()

    class Meta:
        unique_together = [
            ['object_id', 'content_type'],  # furniture can appear only once
            ['test_category_order', 'category'],
            ['test_profit_netto_category_order', 'category'],
            ['test_profit_netto_alt_category_order', 'category'],
            ['test_profit_netto_category_order_de', 'category'],
            ['test_profit_netto_category_order_fr', 'category'],
            ['test_profit_netto_category_order_nl', 'category'],
            ['test_profit_netto_category_order_uk', 'category'],
            ['test_profit_netto_category_order_ch', 'category'],
            ['test_profit_netto_category_order_se', 'category'],
            ['test_profit_netto_category_order_no', 'category'],
            ['test_profit_netto_category_order_dk', 'category'],
        ]
        constraints = [
            models.CheckConstraint(
                # since Django 5.1 param is named `condition`
                check=(Q(category='') | Q(sku_category__isnull=True)),
                name='only_one_category',
            ),
        ]
        indexes = [
            models.Index(
                models.F(
                    f'profit_netto_dynamic_order__{region_code}__{ListingOrder.COLLECTIVE_CATEGORY}'
                ),
                name=f'{region_code}_{ListingOrder.COLLECTIVE_CATEGORY}_idx',
            )
            for region_code in MainMarketsEnum.get_region_codes()
        ]
        verbose_name = 'Catalogue Entry'
        verbose_name_plural = 'Catalogue Entries'

    def __str__(self) -> str:
        return f'Catalogue {self.id} - {self.content_type}:{self.object_id}'

    def save(
        self,
        *args,
        force_insert: bool = False,
        update_attributes: bool = True,
        **kwargs,
    ) -> None:
        # save first to make sure that the pk exists
        if force_insert or not self.pk:
            super().save(*args, **kwargs, force_insert=force_insert)
        self.update_fields()
        if update_attributes:
            AttributesAssigner(self).assign_attributes()
        super().save(*args, **kwargs, force_insert=False)

    @classmethod
    def create_entry_from_furniture(
        cls,
        furniture: FurnitureType,
        save: bool = True,
        available_colors: list = None,  # noqa: RUF013
        **kwargs,
    ) -> 'CatalogueEntry':
        validate_category_configuration(furniture)
        if not available_colors:
            available_colors = [{'id': furniture.id, 'material': furniture.material}]
        entry = cls(
            furniture=furniture,
            order=cls.get_next_all_shelves_order(),
            available_colors=available_colors,
            **kwargs,
        )
        entry.update_fields()
        if save:
            entry.save()
        return entry

    @property
    def is_in_promo(self):
        if promo := get_active_promotion():
            return bool(get_label_promo(self.furniture, promo.promo_code, promo))

        return False

    @property
    def is_new_arrival(self):
        return bool(get_label_new(self.furniture))

    @property
    def is_special(self) -> bool:
        # this is a purely business issue, will change over time
        return self.shelf_type == ShelfType.TYPE01 and self.material == Type01Color.RED

    @property
    def is_furniture(self) -> bool:
        return self.furniture.furniture_type in Furniture.get_furniture_type_values()

    @property
    def is_sku_variant(self) -> bool:
        return self.furniture.furniture_type == Furniture.skuvariant

    @property
    def product_line(self) -> ProductLine | None:
        if self.is_furniture:
            return ShelfType(self.shelf_type).product_line
        elif self.is_sku_variant:
            id_to_name_map = SkuCategory.objects.get_id_to_slug_mapping()
            try:
                sku_category_name = id_to_name_map.get(self.sku_category_id)
                return SkuCategoryEnum(sku_category_name).product_line
            except ValueError:
                pass
        return None

    def update_fields(self) -> None:
        self.height = self.furniture.height
        self.width = self.furniture.width
        self.depth = self.furniture.depth
        self.price = self.furniture.get_shelf_price_as_number(region=None)
        if self.is_furniture:
            self.shelf_type = self.furniture.shelf_type
            self.material = self.furniture.material
            self.category = self.furniture.furniture_category
        elif self.is_sku_variant:
            self.sku_category = self.furniture.sku_category

    @property
    def related_entries(self) -> CatalogueEntryQuerySet:
        return CatalogueEntry.objects.filter(
            object_id__in={item['id'] for item in self.available_colors},
            content_type=self.content_type,
            enabled=True,
        )

    @property
    def height_range(self) -> HeightRange:
        from catalogue.services.automatic_merchandising.catalogue_stats import (
            category_height_ranges,
        )

        category_height_ranges = category_height_ranges(self.category)
        if self.height < category_height_ranges[HeightRange.LOW].maximal:
            return HeightRange.LOW
        if self.height < category_height_ranges[HeightRange.MEDIUM].maximal:
            return HeightRange.MEDIUM
        return HeightRange.TALL

    @property
    def width_range(self) -> WidthRange:
        from catalogue.services.automatic_merchandising.catalogue_stats import (
            category_width_ranges,
        )

        category_width_ranges = category_width_ranges(self.category)
        if self.width < category_width_ranges[WidthRange.NARROW].maximal:
            return WidthRange.NARROW
        if self.width < category_width_ranges[WidthRange.MEDIUM].maximal:
            return WidthRange.MEDIUM
        return WidthRange.WIDE

    @classmethod
    def get_next_all_shelves_order(cls) -> int:
        return cls.objects.aggregate(Max('order'))['order__max'] + 1

    @classmethod
    def get_next_category_order(cls, category: FurnitureCategory) -> int:
        return (
            cls.objects.filter(category=category).aggregate(Max('category_order'))[
                'category_order__max'
            ]
            + 1
        )


class BoardManualOrderQuerySet(models.QuerySet):
    def exclude_out_of_stock(self):
        sku_stock_service = SkuStockService()
        unavailable_sku_variant_ids = (
            sku_stock_service.get_unavailable_sku_variant_ids()
        )
        return self.exclude(
            entry__content_type__model=Furniture.skuvariant,
            entry__object_id__in=unavailable_sku_variant_ids,
        )


class BoardManualOrder(models.Model):
    """A model that allows us to override the default order from CatalogueEntry.
    Used for main boards and miniboards.

    Allows placing a selected entry as a Xth shelf on a given board,
    without reordering the original catalogue.
    The shelf that was originally there will not show on the board at all.

    Board names are an alphabetically sorted list of filters joined by ``__``.
    """

    entry = models.ForeignKey('catalogue.CatalogueEntry', on_delete=models.CASCADE)
    board_name = models.CharField(max_length=512, db_index=True)
    order = models.IntegerField()
    published = models.BooleanField(default=True)

    objects = BoardManualOrderQuerySet.as_manager()

    class Meta:
        # enforce no order clashes
        unique_together = ('board_name', 'order', 'published')

    def __str__(self) -> str:
        return f'ID:{self.id} - {self.board_name} - Order: {self.order}'
