from django.urls import reverse
from rest_framework import status

import pytest


@pytest.mark.django_db
class TestPLPPerformance:
    url = reverse('catalogue:catalogue-main')

    @pytest.mark.parametrize(
        (
            'url_params',
            'expected_query_count',
            'second_hit_query_count',
        ),
        [
            ('', 33, 23),
            ('category=sideboard&regionName=germany', 30, 20),
            ('category=wardrobe&regionName=germany', 30, 20),
            ('category=wardrobe&colors=white,grey&regionName=germany', 29, 20),
        ],
    )
    def test_plp_performance(
        self,
        url_params,
        expected_query_count,
        second_hit_query_count,
        api_client,
        entries_with_strategic_orders,
        django_assert_max_num_queries,
    ):
        # first hit with no cache
        with django_assert_max_num_queries(expected_query_count):
            response = api_client.get(f'{self.url}?{url_params}')
            assert response.status_code == status.HTTP_200_OK

        # second hit with some cache
        with django_assert_max_num_queries(second_hit_query_count):
            response = api_client.get(f'{self.url}?{url_params}')
            assert response.status_code == status.HTTP_200_OK
