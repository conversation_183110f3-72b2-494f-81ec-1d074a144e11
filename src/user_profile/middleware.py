import logging

from datetime import timed<PERSON><PERSON>
from typing import Optional
from urllib.parse import (
    parse_qs,
    urlparse,
)

from django.contrib.auth import login
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import get_language_from_path

from custom.enums import LanguageEnum
from custom.utils.strings import sanitize_incoming_string
from regions.change_region import change_region
from regions.geo_guessr import get_forced_region
from regions.models import Region
from regions.services.region_selector import RegionSelector
from user_profile.constants import (
    FACEBOOK_COOKIE_NAME,
    FACEBOOK_UTM,
)
from user_profile.models import LoginAccessToken

logger = logging.getLogger('cstm')


class LoginAccessTokenMiddleware(MiddlewareMixin):
    def process_request(self, request):
        lat_token = request.GET.get('lat', None) or self._get_lat_from_referer(request)
        if lat_token:
            try:
                lat = LoginAccessToken.objects.get(token=lat_token)
                if lat.is_token_valid():
                    user = lat.user
                    if user.is_staff or user.is_superuser:
                        logger.warning(
                            'LoginAccessToken %s used to log in user_id=%s as staff',
                            lat.token,
                            user.id,
                        )
                        return
                    user.backend = 'django.contrib.auth.backends.ModelBackend'
                    login(request, user)
                    lat.used_at = timezone.now()
                    lat.save(update_fields=['used_at'])
            except LoginAccessToken.DoesNotExist:
                pass

    def _get_lat_from_referer(self, request) -> str | None:
        referer = request.META.get('HTTP_REFERER')
        parsed_url = urlparse(referer)
        query_params = parse_qs(parsed_url.query)
        return query_params.get('lat', [None])[0]


class ForceRegionMiddleware(MiddlewareMixin):
    _IGNORED_PATHS = (
        '/api',
        '/internal-api',
        '/admin',
        'favicon.ico',
        'review-list-ajax',
        '/uploaded',
        '/pages',
    )
    _IGNORED_USER_AGENTS = (
        'datadog',
        'newrelic',
        'geckoboard',
        'feedly',
        'node-fetch',
        'SkypeUriPreview',
        'WEBDAV',
        'Mediapartners-Google',
        'Chrome/36.0.1985.67',
    )

    def process_request(self, request):
        forced_region = get_forced_region(request)
        if request.user.is_anonymous:
            if forced_region:
                request.session['cached_region'] = forced_region.get_data_as_dict()
                request.session['cached_language'] = self._get_referer_language_prefix(
                    request
                ) or LanguageEnum(forced_region.default_for_language)
                return
            return

        if forced_region:
            change_region(request.user.profile, forced_region, request)
        elif self.should_change_region_to_default(request):
            region = Region.get_region_for_language(request.user.profile.language)
            change_region(request.user.profile, region, request)

    def should_change_region_to_default(self, request):
        user_agent = sanitize_incoming_string(request.META.get('HTTP_USER_AGENT', ''))
        profile = request.user.profile
        for ignored_path in self._IGNORED_PATHS:
            if ignored_path in request.path:
                return False
        for ignored_agent in self._IGNORED_USER_AGENTS:
            if ignored_agent.lower() in user_agent.lower():
                return False
        return not request.user_agent.is_bot and not profile.region

    def _get_referer_language_prefix(self, request) -> Optional[str]:
        referer = request.META.get('HTTP_REFERER')
        if not referer:
            return

        return get_language_from_path(referer)


class ProcessAnonymousMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if request.session.get('cached_region') and request.session.get(
            'cached_language'
        ):
            return

        if request.user.is_anonymous:
            region_selector = RegionSelector(request)
            region = region_selector.get_region()
        else:
            region = request.user.profile.region

        request.session['cached_region'] = region.get_data_as_dict()
        request.session['cached_language'] = self._get_referer_language_prefix(
            request
        ) or LanguageEnum(region.default_for_language)

        if not request.session.session_key:
            request.session.create()

    def process_response(self, request, response):
        if request.COOKIES.get('global_session_id'):
            return response

        # Calculate the expiration date (2 years from now)
        expiration_date = timezone.now() + timedelta(days=365 * 2)

        response.set_cookie(
            'global_session_id',
            request.session.session_key,
            expires=expiration_date,
        )

        if request.COOKIES.get(FACEBOOK_COOKIE_NAME) is None:
            # check if user comes from facebook,
            # can be deleted after meta_traffic_cta_order_reversed AB test is over
            response.set_cookie(
                FACEBOOK_COOKIE_NAME,
                FACEBOOK_UTM in request.META.get('HTTP_REFERER', ''),
                expires=expiration_date,
            )

        return response

    def _get_referer_language_prefix(self, request) -> Optional[str]:
        referer = request.META.get('HTTP_REFERER')
        if not referer:
            return

        return get_language_from_path(referer)
