import logging

from django.conf import settings
from django.db import transaction
from django.http.response import HttpResponse
from django.shortcuts import get_object_or_404
from django.views.generic.base import TemplateView
from rest_framework import (
    mixins,
    status,
)
from rest_framework.decorators import action
from rest_framework.filters import OrderingFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from django_filters.rest_framework import DjangoFilterBackend
from sorl.thumbnail.shortcuts import get_thumbnail

from custom.utils.response_cache import cache_response
from ecommerce_api.settings import api_settings
from gallery.constants import SOFA_FURNITURE_CATEGORY
from gallery.enums import FurnitureCategory
from reviews.cache_helpers import should_skip_review_pdp_cache
from reviews.constants import TOTAL_REVIEWS_FOR_PDP
from reviews.enums import (
    ReviewAttribute,
    ReviewCreateErrorEnum,
)
from reviews.exceptions import (
    ReviewOrderItemAlreadyReviewedError,
    ReviewOrderItemNotFoundError,
)
from reviews.filters import (
    ReviewExtendedFilter,
    ReviewFilter,
    ReviewPdpFilter,
)
from reviews.models import (
    Review,
    ReviewQuerySet,
    ReviewScore,
    ReviewTag,
)
from reviews.serializers import (
    CreateReviewSerializer,
    ReviewPdpParamSerializer,
    SingleReviewSerializer,
    ToolReviewListSerializer,
    ToolReviewSerializer,
    ToolReviewTagSerilizer,
    ToolReviewUpdateSerializer,
    UnreviewedProductsInputSeralizer,
    UnreviewedProductsOutputSerializer,
)
from reviews.services.order_items import ReviewOrderItemService
from reviews.tasks import (
    match_review_with_order_task,
    send_bad_review_to_slack,
)

logger = logging.getLogger('cstm')


class ReviewsPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 1_000


class ReviewPhotoUrlView(APIView):
    swagger_tags = ['Reviews']
    permission_classes = ()

    def get(self, request, pk):
        review = get_object_or_404(Review, pk=pk, photos__isnull=False)
        last_image_review = review.photos.last()
        if last_image_review is None:
            return HttpResponse(
                '', content_type='text/plain', status=status.HTTP_200_OK
            )
        else:
            thumbnail = get_thumbnail(review.photos.last().image, '1200x800')
            return HttpResponse(
                str(thumbnail.url), content_type='text/plain', status=status.HTTP_200_OK
            )


class ReviewToolView(TemplateView):
    template_name = 'admin_custom/review_tool.html'


class ReviewToolViewSet(
    mixins.RetrieveModelMixin,
    mixins.DestroyModelMixin,
    mixins.UpdateModelMixin,
    mixins.ListModelMixin,
    GenericViewSet,
):
    queryset = Review.latest_objects.order_by('-created_at')
    pagination_class = ReviewsPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = ReviewExtendedFilter

    def get_serializer_class(self):
        if self.action == 'list':
            return ToolReviewListSerializer
        elif self.action == 'update':
            return ToolReviewUpdateSerializer

        return ToolReviewSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        pk = kwargs.get('pk')
        next_review_id = (
            Review.latest_objects.filter(id__gt=pk).values_list('id', flat=True).first()
        )
        previous_review_id = (
            Review.latest_objects.filter(id__lt=pk).values_list('id', flat=True).first()
        )
        return Response(
            {
                **serializer.data,
                'next_review_id': next_review_id,
                'previous_review_id': previous_review_id,
            }
        )


class ReviewViewSet(
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    GenericViewSet,
):
    swagger_tags = ['Reviews']
    queryset = (
        Review.latest_objects.filter(enabled=True)
        .prefetch_related('tags')
        .select_related('order_item')
    )
    pagination_class = ReviewsPagination
    permission_classes = (AllowAny,)
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['score', 'created_at']
    filterset_class = ReviewFilter
    renderer_classes = api_settings.ECOMMERCE_RENDERER_CLASSES

    # typing
    request: Request

    @property
    def language(self) -> str:
        return self.request.query_params.get('language', self.request.LANGUAGE_CODE)

    def get_queryset(self) -> ReviewQuerySet:
        return super().get_queryset().fetch_translations(language=self.language)

    def get_serializer_class(self):
        if self.action == 'create':
            return CreateReviewSerializer
        return SingleReviewSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['language'] = self.language
        return context

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        context = self.get_serializer_context()
        serializer = self.get_serializer(data=request.data, context=context)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        transaction.on_commit(
            lambda: match_review_with_order_task.delay(serializer.data['pk'])
        )
        if settings.IS_PRODUCTION and serializer.data['score'] < 5:
            send_bad_review_to_slack.delay(serializer.data['pk'])
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    @action(
        detail=False,
        methods=['get'],
        url_path='order-items/unreviewed',
        url_name='order-items-unreviewed',
    )
    def unreviewed_furniture_from_order(self, request, *args, **kwargs):
        serializer = UnreviewedProductsInputSeralizer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        user_email = serializer.validated_data['email']
        order_number = serializer.validated_data['order_number']

        try:
            order_items = ReviewOrderItemService.get_unreviewed_items_for_order(
                user_email, order_number
            )
        except ReviewOrderItemNotFoundError:
            return Response(
                {
                    'error_code': ReviewCreateErrorEnum.ORDER_NOT_FOUND,
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        except ReviewOrderItemAlreadyReviewedError:
            return Response(
                {
                    'error_code': ReviewCreateErrorEnum.ORDER_ALREADY_REVIEWED,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serialized_response = UnreviewedProductsOutputSerializer(
            order_items,
            many=True,
        )

        return Response(serialized_response.data, status=status.HTTP_200_OK)

    @cache_response()
    @action(
        methods=['get'],
        detail=False,
        url_path='review-score',
        url_name='review-score',
    )
    def review_score(self, request):
        queryset = self.filter_queryset(self.get_queryset())
        data = {
            'average_score': queryset.average_score(),
            'count': queryset.count(),
            'active_tags': Review.objects.filter(
                tags__name__in=ReviewAttribute, enabled=True
            )
            .values_list('tags__name', flat=True)
            .distinct()
            .order_by('tags__name'),
        }
        return Response(data)

    @cache_response(skip_if=should_skip_review_pdp_cache)
    @action(
        methods=['get'],
        detail=False,
        url_path='pdp',
        url_name='pdp',
        filterset_class=ReviewPdpFilter,
    )
    def get_reviews_for_pdp(self, request):
        serializer = ReviewPdpParamSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        params = serializer.validated_data

        filter_queryset = self.filter_queryset(self.get_queryset().filter_for_pdp())
        ordered_queryset = filter_queryset.sort_by(
            category=params['category'],
            shelf_type=params['shelf_type'],
            material=params.get('material'),
        )[:TOTAL_REVIEWS_FOR_PDP]

        serializer = self.get_serializer(ordered_queryset, many=True)

        return Response(serializer.data)


class ReviewTagsApiView(APIView):
    authentication_classes = []
    permission_classes = ()

    def get(self, request):
        review = ToolReviewTagSerilizer(ReviewTag.objects.all(), many=True).data
        return Response(review)


class GlobalReviewScoreApiView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]
    swagger_tags = ['Reviews']

    def get(self, request):
        avg_score, reviews_count = ReviewScore.get_general_review_score()
        data = {
            'avg_score': avg_score,
            'categories': self._get_categories(),
            'reviews_count': reviews_count,
            'tags': [
                {
                    'value': review_attribute.value,
                    'translated_name': review_attribute.label,
                }
                for review_attribute in ReviewAttribute
            ],
        }
        return Response(
            data,
            status=status.HTTP_200_OK,
        )

    def _get_categories(self) -> set[FurnitureCategory | str]:
        return {
            category
            if category not in FurnitureCategory.get_sofas()
            else SOFA_FURNITURE_CATEGORY
            for category in Review.objects.get_categories()
        }
