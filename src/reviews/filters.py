from django.db.models import Count

from django_filters import rest_framework as django_filters

from gallery.constants import SOFA_FURNITURE_CATEGORY
from gallery.enums import FurnitureCategory
from reviews.models import (
    Review,
    ReviewTranslation,
)
from skus.models import SkuCategory


class ReviewAttributeInFilter(django_filters.BaseInFilter, django_filters.CharFilter):
    pass


class ReviewFilter(django_filters.FilterSet):
    category = django_filters.CharFilter(method='filter_category')
    has_images = django_filters.BooleanFilter(
        lookup_expr='isnull',
        field_name='photos',
        exclude=True,
    )
    tags = ReviewAttributeInFilter(
        field_name='tags__name',
        lookup_expr='in',
    )

    class Meta:
        model = Review
        fields = [
            'category',
            'space',
            'tags',
        ]

    @staticmethod
    def filter_category(queryset, name, value):
        if value == SOFA_FURNITURE_CATEGORY:
            return queryset.filter(category__in=FurnitureCategory.get_sofas())
        if value.lower() in map(str.lower, SkuCategory.objects.get_names()):
            return queryset.filter(
                order_item__sku_variant__sku__sku_category__name__iexact=value
            )
        category = FurnitureCategory(value)
        return queryset.filter(category=category)


class ReviewPdpFilter(ReviewFilter):
    class Meta:
        model = Review
        fields = [
            'space',
            'tags',
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.filters.pop('category', None)


class ReviewExtendedFilter(ReviewFilter):
    year = django_filters.NumberFilter(field_name='created_at', lookup_expr='year')
    month = django_filters.NumberFilter(field_name='created_at', lookup_expr='month')
    has_email_duplicates = django_filters.BooleanFilter(
        field_name='email',
        method='get_email_duplicates',
    )
    has_description_duplicates = django_filters.BooleanFilter(
        field_name='description',
        method='get_description_duplicates',
    )

    class Meta(ReviewFilter.Meta):
        fields = ReviewFilter.Meta.fields + [  # noqa: RUF005
            'score',
            'enabled',
            'featured',
            'shelf_type',
            'furniture_type',
            'order',
            'review_sentiment',
            'created_at',
            'photos',
            'email',
        ]

    @staticmethod
    def get_description_duplicates(queryset, field_name, value):
        duplicates = (
            ReviewTranslation.objects.filter(review__in=queryset)
            .values(field_name)
            .annotate(field_count=Count(field_name))
            .filter(field_count__gt=1)
            .values_list(field_name, flat=True)
        )
        if value:
            return queryset.filter(
                **{f'translations__{field_name}__in': duplicates}
            ).order_by(f'translations__{field_name}')
        return queryset.exclude(
            **{f'translations__{field_name}__in': duplicates}
        ).order_by(f'translations__{field_name}')

    @staticmethod
    def get_email_duplicates(queryset, field_name, value):
        duplicates = (
            queryset.values(field_name)
            .annotate(field_count=Count(field_name))
            .filter(field_count__gt=1)
            .values_list(field_name, flat=True)
        )
        if value:
            return queryset.filter(**{f'{field_name}__in': duplicates}).order_by(
                field_name
            )
        return queryset.exclude(**{f'{field_name}__in': duplicates}).order_by(
            field_name
        )
