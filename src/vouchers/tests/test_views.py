import base64

from decimal import Decimal
from typing import Dict
from unittest.mock import (
    PropertyMock,
    patch,
)
from urllib.parse import urlencode

from django.test import Client
from django.urls import reverse
from rest_framework import status

import pytest

from carts.services.cart_service import CartService
from custom.enums import (
    Furniture,
    ShelfType,
    Type01Color,
    Type02Color,
    Type03Color,
)
from custom.enums.colors import Sofa01Color
from custom.utils.adyen import get_current_payment_settings
from gallery.enums import FurnitureCategory
from invoice.choices import (
    InvoiceStatus,
    NumerationType,
)
from orders.enums import OrderStatus
from payments.tasks import accept_notification
from pricing_v3.services.price_calculators import OrderPriceCalculator
from skus.enums import HtsCodesChoices
from skus.models import SkuCategory
from vouchers.enums import (
    ServiceType,
    VoucherOrigin,
    VoucherStatusMessages,
    VoucherType,
)
from vouchers.models import Voucher
from vouchers.serializers import MailingAbsoluteVoucherSerializer


@pytest.fixture
def user_client(api_client, user):
    api_client.force_authenticate(user=user)
    return api_client


@pytest.fixture
def cart_with_item(cart_factory, user, mocker):
    mocker.patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=125)
    cart = cart_factory(owner=user)
    return cart


@pytest.mark.django_db
class TestCheckVoucher:
    code = 'MAGICSTRING'
    url = reverse('rest_check_voucher', args=['MAGICSTRING'])

    def test_removes_voucher_from_cart_if_no_code_provided(
        self,
        user,
        user_client,
        cart_factory,
        voucher_factory,
    ):
        voucher = voucher_factory(is_percentage=True)
        order = cart_factory(owner=user, vouchers=[voucher])
        url = reverse('rest_check_voucher')
        response = user_client.post(url, format='json')

        order.refresh_from_db()
        assert order.vouchers.exists() is False
        assert response.json()['message'] == 'Removed voucher from cart'

    def test_applies_voucher_when_voucher_group_with_proper_voucher_exists(
        self,
        voucher_factory,
        voucher_group_factory,
        user_client,
        cart_with_item,
    ):
        group = voucher_group_factory(code='MAGICSTRING')
        voucher = voucher_factory(
            is_percentage=True,
            code='notMAGICSTRING',
            quantity=1,
            quantity_left=1,
            amount_starts=1,
            group=group,
        )

        user_client.post(self.url, format='json')
        cart_with_item.refresh_from_db()
        assert voucher in cart_with_item.vouchers.all()

    def test_doesnt_apply_voucher_and_returns_400_when_no_matching_voucher_in_group(
        self,
        voucher_factory,
        voucher_group_factory,
        user_client,
        cart_with_item,
    ):
        group = voucher_group_factory(code='MAGICSTRING')
        voucher_factory(
            is_percentage=True,
            code='notMAGICSTRING',
            quantity=1,
            quantity_left=1,
            amount_starts=1010,
            group=group,
        )

        response = user_client.post(self.url, format='json')
        cart_with_item.refresh_from_db()

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert cart_with_item.vouchers.exists() is False

    def test_doesnt_apply_voucher_and_returns_400_when_its_a_promo_voucher_and_promo_is_not_active(  # noqa: E501
        self,
        voucher_factory,
        promotion_factory,
        promotion_config_factory,
        user_client,
        cart_with_item,
    ):
        voucher = voucher_factory(
            is_percentage=True,
            code='MAGICSTRING',
            quantity=1,
            quantity_left=1,
            amount_starts=1,
        )

        promotion = promotion_factory(promo_code=voucher, active=False)
        config = promotion_config_factory(promotion=promotion)
        config.enabled_regions.set([cart_with_item.region])

        response = user_client.post(self.url, format='json')
        cart_with_item.refresh_from_db()

        assert cart_with_item.vouchers.exists() is False
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()['status']
            == VoucherStatusMessages.VOUCHER_CONDITIONS_ERROR.value
        )

    def test_doesnt_apply_voucher_and_returns_400_when_the_voucher_is_not_active(
        self,
        voucher_factory,
        user_client,
        cart_with_item,
    ):
        voucher_factory(
            is_percentage=True,
            code='MAGICSTRING',
            quantity=1,
            quantity_left=0,
            amount_starts=1,
        )

        response = user_client.post(self.url, format='json')
        cart_with_item.refresh_from_db()

        assert cart_with_item.vouchers.exists() is False
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()['status']
            == VoucherStatusMessages.VOUCHER_NOT_APPLICABLE.value
        )

    @pytest.mark.parametrize(('amount_starts', 'amount_limit'), [(1, 3), (1010, 1011)])
    def test_doesnt_apply_voucher_and_returns_400_when_order_is_not_within_vouchers_limits(  # noqa: E501
        self,
        voucher_factory,
        user_client,
        cart_with_item,
        amount_starts,
        amount_limit,
    ):
        voucher_factory(
            is_percentage=True,
            code='MAGICSTRING',
            quantity=1,
            quantity_left=1,
            amount_starts=amount_starts,
            amount_limit=amount_limit,
        )

        response = user_client.post(self.url, format='json')
        cart_with_item.refresh_from_db()

        assert cart_with_item.vouchers.exists() is False
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()['status']
            == VoucherStatusMessages.VOUCHER_NOT_APPLICABLE.value
        )
        assert response.json()['message'] == 'This voucher does not apply to your order'

    def test_rest_check_voucher_returns_400_when_apply_wrong_promocode(
        self,
        api_client,
        cart,
    ):
        api_client.force_authenticate(user=cart.owner)
        url = reverse('rest_check_voucher', args=['Nebuchadnezzar'])

        response = api_client.post(url, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()['message'] == 'There is no voucher with Nebuchadnezzar code'
        )
        assert response.json()['is_active'] is False
        assert (
            response.json()['status'] == VoucherStatusMessages.VOUCHER_NOT_FOUND.value
        )

    def test_rest_check_voucher_returns_400_when_not_valid_code(
        self,
        voucher_factory,
        api_client,
        cart,
    ):
        api_client.force_authenticate(user=cart.owner)
        voucher = voucher_factory(code=self.code, quantity=0, quantity_left=0)
        url = reverse('rest_check_voucher', args=[voucher.code])

        response = api_client.post(url, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()['status']
            == VoucherStatusMessages.VOUCHER_NOT_APPLICABLE.value
        )

    def test_hit_without_code_cancels_voucher(
        self,
        api_client,
        cart,
    ):
        api_client.force_authenticate(user=cart.owner)
        url = reverse('rest_check_voucher')
        response = api_client.post(url, format='json')

        assert response.status_code == status.HTTP_200_OK

    def test_query_count_at_adding_dead_promo(
        self,
        api_client,
        user,
        cart,
        voucher_factory,
        django_assert_max_num_queries,
    ):
        api_client.force_authenticate(user=cart.owner)
        voucher = voucher_factory(code=self.code, quantity=0, quantity_left=0)
        url = reverse('rest_check_voucher', args=[voucher.code])
        with django_assert_max_num_queries(69):
            api_client.post(url, format='json')

    def test_query_count_at_adding_alive_promo(
        self,
        api_client,
        user,
        cart,
        voucher_factory,
        django_assert_max_num_queries,
    ):
        api_client.force_authenticate(user=cart.owner)
        voucher = voucher_factory(code=self.code, quantity=1, quantity_left=1)
        url = reverse('rest_check_voucher', args=[voucher.code])
        with django_assert_max_num_queries(119):
            api_client.post(url, format='json')

    def test_query_count_at_resetting_voucher(
        self,
        api_client,
        user,
        cart,
        django_assert_max_num_queries,
    ):
        api_client.force_authenticate(user=cart.owner)
        url = reverse('rest_check_voucher')
        with django_assert_max_num_queries(57):
            api_client.post(url, format='json')

    def test_cart_not_found(
        self,
        api_client,
        user,
    ):
        url = reverse('rest_check_voucher')
        api_client.force_authenticate(user=user)
        cart = CartService.get_cart(user)
        assert cart is None
        response = api_client.post(url, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data.get('status') == VoucherStatusMessages.CART_NOT_FOUND


@pytest.mark.django_db
class TestGlobalPromoViewSet:
    @pytest.mark.parametrize(
        'filters',
        [
            f'?furniture_type={Furniture.jetty.value}&furniture_category={FurnitureCategory.CHEST}&material={Type01Color.WHITE}&shelf_type={ShelfType.TYPE01}',
            f'?furniture_type={Furniture.sotty.value}&furniture_category=sofa&material={Sofa01Color.REWOOL2_BROWN}',
            f'?furniture_type={Furniture.watty.value}&furniture_category={FurnitureCategory.WARDROBE}&material={Type03Color.WHITE}&shelf_type={ShelfType.TYPE03}',
        ],
    )
    @patch('vouchers.views.EcommerceAPIMixin.region', new_callable=PropertyMock)
    def test_get_global_promo_discount(
        self, mock, api_client, active_promotion, region_de, filters
    ):
        mock.return_value = region_de.cached_region_data
        url = reverse('global_promo-discount') + filters

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
class TestVouchers:
    @pytest.fixture
    def user_client(self, user, client):
        client.force_login(user=user)
        return client

    @pytest.fixture
    def request_data(self) -> Dict:
        return {
            'kind_of': VoucherType.ABSOLUTE,
            'code': '',
            'value': 50,
            'barter_value': 0,
            'uses': 1,
            'limit_lower': 500,
            'limit_upper': 100000,
            'how_many': 1,
            'origin': VoucherOrigin.MANUAL,
            'characters': 8,
            'code_charactes': 'all',
            '_save': 'Save',
        }

    def test_should_allow_creating_promocode(
        self, admin_client: Client, request_data: Dict
    ):
        response = admin_client.post(
            reverse('cs_create_promocode'),
            urlencode(request_data),
            content_type='application/x-www-form-urlencoded',
        )

        assert response.status_code == status.HTTP_200_OK

    def test_should_create_voucher_object_when_creating_promocode(
        self, admin_client: Client, request_data: Dict
    ):
        admin_client.post(
            reverse('cs_create_promocode'),
            urlencode(request_data),
            content_type='application/x-www-form-urlencoded',
        )

        assert Voucher.objects.count() == 1
        voucher = Voucher.objects.first()

        assert voucher.value == request_data['value']
        assert voucher.amount_starts == request_data['limit_lower']
        assert voucher.amount_limit == request_data['limit_upper']

    @pytest.mark.parametrize(
        ('form_data', 'item_conditionals'),
        [
            (
                {'excluded_furniture': ShelfType.TYPE01.value},
                {'exclude': [{'shelf_types': ['0']}]},
            ),
            (
                {'exclude_sample_box': 'on'},
                {'exclude': [{'furniture_types': ['sample_box']}]},
            ),
        ],
    )
    def test_create_voucher_for_excluded_furniture_and_samplebox(
        self,
        admin_client: Client,
        request_data: Dict,
        form_data,
        item_conditionals,
    ):
        data = {**request_data, **form_data}

        admin_client.post(
            reverse('cs_create_promocode'),
            urlencode(data),
            content_type='application/x-www-form-urlencoded',
        )

        assert Voucher.objects.count() == 1
        voucher = Voucher.objects.first()
        assert voucher.item_conditionals == item_conditionals


@pytest.mark.django_db
class TestInvoiceIgnoredVouchers:
    @pytest.fixture
    def invoice_ignored_full_promo(self, voucher_factory):
        return voucher_factory(
            value=100,
            kind_of=VoucherType.PERCENTAGE,
            amount_starts=0,
            quantity_left=10,
            ignore_on_invoice=True,
        )

    @pytest.fixture
    def full_promo(self, voucher_factory):
        return voucher_factory(
            value=100,
            kind_of=VoucherType.PERCENTAGE,
            amount_starts=0,
            quantity_left=10,
            ignore_on_invoice=False,
        )

    @pytest.fixture
    def invoice_ignored_regular_promo(self, voucher_factory):
        return voucher_factory(
            value=50,
            kind_of=VoucherType.PERCENTAGE,
            amount_starts=0,
            quantity_left=10,
            ignore_on_invoice=True,
        )

    @pytest.fixture
    def regular_promo(self, voucher_factory):
        return voucher_factory(
            value=50,
            kind_of=VoucherType.PERCENTAGE,
            amount_starts=0,
            quantity_left=10,
            ignore_on_invoice=False,
        )

    @pytest.fixture
    def order_owner(self, user_factory, order_region):
        user = user_factory()
        user.profile.change_region(order_region)

        return user

    @pytest.fixture
    def full_promo_order(self, order_factory, order_owner, order_region):
        order = order_factory(
            total_price_net=0,
            status=OrderStatus.CART,
            owner=order_owner,
            country=order_region.name,
            region=order_region,
        )

        OrderPriceCalculator(order).calculate(check_vat=True)
        return order

    @pytest.fixture
    def user_client(self, order_owner, client):
        client.force_login(user=order_owner)
        return client

    @pytest.fixture
    def order_region(self, region_factory, currency_factory, country_factory):
        currency = currency_factory(name='Euro', code='EUR', symbol='€')
        region = region_factory(currency=currency, name='poland')
        country_factory(region=region, name=region.name, locale='pl_PL')

        return region

    @pytest.fixture
    def request_data(self) -> Dict:
        return {
            'kind_of': VoucherType.ABSOLUTE,
            'code': '',
            'value': 50,
            'barter_value': 0,
            'uses': 1,
            'limit_lower': 500,
            'limit_upper': 100000,
            'how_many': 1,
            'origin': VoucherOrigin.MANUAL,
            'characters': 8,
            'code_charactes': 'all',
            'ignore_discount_on_invoice': True,
            '_save': 'Save',
        }

    @pytest.fixture
    def payment_notification(self, payment_pending_order):
        return {
            'operations': 'CANCEL,CAPTURE,REFUND',
            'merchantAccountCode': 'CSTMCO',
            'merchantReference': f'order-{payment_pending_order.id}',
            'eventCode': 'AUTHORISATION',
            'paymentMethod': 'mc',
            'additionalData.cardSummary': '9028',
            'value': '1000',
            'currency': 'EUR',
            'live': 'true',
            'additionalData.expiryDate': '11/2018',
            'originalReference': '',
            'success': 'true',
            'reason': '240650:9028:11/2018',
            'additionalData.authCode': '240650',
            'pspReference': '3333',
            'eventDate': '2015-06-18T06:28:58.43Z',
        }

    @pytest.fixture
    def invoice_sequences(
        self,
        payment_pending_order,
        invoice_sequence_factory,
        country_factory,
    ):
        for numeration_type in [
            NumerationType.NORMAL,
            NumerationType.RV,
            NumerationType.VC,
        ]:
            invoice_sequence_factory(
                country=country_factory(name=payment_pending_order.country),
                invoice_type=InvoiceStatus.ENABLED,
                numeration_type=numeration_type,
            )

    @pytest.fixture
    def payment_pending_order(
        self, order_owner, order_factory, region_factory, order_region
    ):
        order = order_factory(
            owner=order_owner,
            total_price_net=0,
            status=OrderStatus.PAYMENT_PENDING,
            status_previous=OrderStatus.CART,
            country=order_region.name,
            region=order_region,
        )

        OrderPriceCalculator(order).calculate(check_vat=True)
        return order

    @pytest.fixture(autouse=True)
    def mock_sleep(self, mocker):
        mocker.patch('payments.tasks.sleep')

    def test_should_allow_creating_invoice_ignored_promocode(
        self, admin_client: Client, request_data: Dict
    ):
        response = admin_client.post(
            reverse('cs_create_promocode'),
            urlencode(request_data),
            content_type='application/x-www-form-urlencoded',
        )

        assert response.status_code == status.HTTP_200_OK
        assert Voucher.objects.count() == 1

        voucher = Voucher.objects.first()
        assert voucher.ignore_on_invoice is True

    @pytest.mark.usefixtures('invoice_sequences')
    def test_payment_notification_updates_order_value_for_invoice_ignored_voucher(
        self,
        payment_notification,
        client,
        payment_pending_order,
        invoice_ignored_regular_promo,
    ):
        self._setup_order_with_promo(
            payment_pending_order, invoice_ignored_regular_promo
        )

        adyen_settings = get_current_payment_settings()

        credentials = base64.b64encode(
            f'{adyen_settings["WEBHOOK_USERNAME"]}:{adyen_settings["WEBHOOK_PASSWORD"]}'.encode()
        ).decode()
        with patch('payments.views.accept_notification.delay') as notification_mock:
            notification_mock.side_effect = lambda args: accept_notification(args)
            response = client.post(
                '/api/v1/payment/notifications/',
                payment_notification,
                HTTP_AUTHORIZATION=f'Basic {credentials}',
            )

        assert response.status_code == status.HTTP_200_OK

        payment_pending_order.refresh_from_db()
        expected_region_price = (
            sum(
                [  # noqa: C419
                    item.region_price + item.region_delivery_price
                    for item in payment_pending_order.items.all()
                ]
            )
            / 2
        )
        expected_total_price = (
            sum([item.price for item in payment_pending_order.items.all()]) / 2  # noqa: C419
        )
        assert payment_pending_order.region_total_price == expected_region_price
        assert payment_pending_order.total_price == expected_total_price
        assert payment_pending_order.promo_amount == expected_total_price
        assert payment_pending_order.region_promo_amount == expected_region_price

    @pytest.mark.usefixtures('invoice_sequences')
    def test_payment_notification_does_not_update_order_value_for_regular_voucher(
        self, payment_notification, client, payment_pending_order, regular_promo
    ):
        self._setup_order_with_promo(payment_pending_order, regular_promo)
        expected_region_price = payment_pending_order.region_total_price
        expected_total_price = payment_pending_order.total_price
        expected_promo = payment_pending_order.promo_amount
        expected_region_promo = payment_pending_order.region_promo_amount

        adyen_settings = get_current_payment_settings()

        credentials = base64.b64encode(
            f'{adyen_settings["WEBHOOK_USERNAME"]}:{adyen_settings["WEBHOOK_PASSWORD"]}'.encode()
        ).decode()
        with patch('payments.views.accept_notification.delay') as notification_mock:
            notification_mock.side_effect = lambda args: accept_notification(args)
            response = client.post(
                '/api/v1/payment/notifications/',
                payment_notification,
                HTTP_AUTHORIZATION=f'Basic {credentials}',
            )

        assert response.status_code == status.HTTP_200_OK

        payment_pending_order.refresh_from_db()
        assert payment_pending_order.region_total_price == expected_region_price
        assert payment_pending_order.total_price == expected_total_price
        assert payment_pending_order.promo_amount == expected_promo
        assert payment_pending_order.region_promo_amount == expected_region_promo

    def _setup_order_with_promo(self, order, promo):
        order.vouchers.add(promo)
        OrderPriceCalculator(order).calculate()

        promo.amount_limit = (
            order.get_total_price_number_before_discount()
            * 2
            / order.get_region().get_currency().current_rate.rate
        )
        promo.save()


@pytest.mark.django_db
class TestMailingBaseVoucherCreateView:
    def test_create_percentage_voucher(
        self,
        api_client,
        user,
    ):
        api_client.force_authenticate(user)
        vouchers_count_before = Voucher.objects.count()
        data = {
            'prefix': 'asdf',
            'amount': 10,
            'duration': 28,
            'for_email': '<EMAIL>',
            'exclude_types': [0, 1],
        }
        url = reverse('mailing-percentage-voucher')
        response = api_client.post(data=data, path=url, format='json')
        vouchers_count_after = Voucher.objects.count()

        assert response.status_code == status.HTTP_201_CREATED
        assert vouchers_count_before + 1 == vouchers_count_after

    def test_create_promo_code_with_discount(self, api_client, user):
        delivery_value = Decimal('15.0')
        data = {
            'kind_of': VoucherType.PERCENTAGE,
            'email': '<EMAIL>',
            'delivery_discount': delivery_value,
            'duration': 1,
            'for_email': '<EMAIL>',
            'prefix': 'nwl',
            'exclude_sku_variants': False,
        }
        url = reverse('mailing-absolute-voucher')
        api_client.force_authenticate(user)
        response = api_client.post(url, data=data, format='json')
        assert response.status_code == 201
        voucher = Voucher.objects.last()
        discount = voucher.discounts.filter(service_type=ServiceType.DELIVERY).last()
        assert discount.value == delivery_value

    @pytest.mark.parametrize(
        ('exclude_sofas', 'item_conditionals'),
        [
            (True, {'exclude': [{'shelf_types': [ShelfType.SOFA_TYPE01]}]}),
            (False, None),
        ],
    )
    def test_create_promo_code_check_no_promo_for_sotty(
        self, exclude_sofas, item_conditionals, api_client, user
    ):
        data = {
            'kind_of': VoucherType.ABSOLUTE,
            'email': '<EMAIL>',
            'duration': 1,
            'for_email': '<EMAIL>',
            'prefix': 'nwl',
            'exclude_sofas': exclude_sofas,
            'exclude_sku_variants': False,
        }
        url = reverse('mailing-absolute-voucher')
        api_client.force_authenticate(user)
        response = api_client.post(url, data=data, format='json')
        assert response.status_code == 201
        voucher = Voucher.objects.last()
        assert voucher.item_conditionals == item_conditionals

    def test_create_promo_code_exclude_sku_variants(
        self,
        sku_category_factory,
        api_client,
        user,
    ):
        # Arrange
        sku_category_factory()
        data = {
            'kind_of': VoucherType.ABSOLUTE,
            'email': '<EMAIL>',
            'duration': 1,
            'for_email': '<EMAIL>',
            'prefix': 'nwl',
            'exclude_sku_variants': True,
        }
        url = reverse('mailing-absolute-voucher')
        api_client.force_authenticate(user)

        # Act
        with patch.object(MailingAbsoluteVoucherSerializer, '_apply_sofa_discount'):
            response = api_client.post(url, data=data, format='json')

        # Assert
        assert response.status_code == 201
        voucher: Voucher = Voucher.objects.last()
        sku_categories = SkuCategory.objects.get_names()
        assert voucher.item_conditionals == {
            'exclude': [{'sku_category_names': sku_categories}],
        }

    def test_different_discount_value_for_sotty(self, api_client, user):
        data = {
            'email': '<EMAIL>',
            'duration': 1,
            'for_email': '<EMAIL>',
            'prefix': 'cwks',
            'amount': 25,
            'sofa_discount_value': 1,
            'exclude_sku_variants': False,
        }
        url = reverse('mailing-percentage-voucher')
        api_client.force_authenticate(user)

        response = api_client.post(url, data=data, format='json')

        assert response.status_code == 201
        voucher = Voucher.objects.last()
        assert voucher.value == data['amount']
        assert voucher.discounts.filter(
            value=data['sofa_discount_value'], furniture_type=Furniture.sotty.value
        ).exists()


@pytest.mark.django_db
class TestMailingSampleVoucherCreateView:
    def test_create_sample_voucher(
        self,
        api_client,
        user,
        sku_category_factory,
    ):
        # assign
        api_client.force_authenticate(user)
        email = '<EMAIL>'
        data = {
            'prefix': 'wfs',
            'duration': 28,
            'for_email': email,
        }
        sku_category_sample = sku_category_factory(hts_code=HtsCodesChoices.SAMPLES)
        sku_category_factory(hts_code='000001')
        # act
        url = reverse('mailing-sample-voucher')
        response = api_client.post(data=data, path=url, format='json')
        # assert
        voucher = Voucher.objects.get(code=response.json().get('Promocode'))
        expected_discount = {
            'value': Decimal('50.0'),
            'sku_category': sku_category_sample.id,
        }
        discount = voucher.discounts.all().first()
        assert response.status_code == status.HTTP_201_CREATED
        assert voucher.kind_of == VoucherType.PERCENTAGE
        assert voucher.origin == VoucherOrigin.MAILING
        assert voucher.value == Decimal('0.0')
        assert voucher.for_email == email
        assert len(voucher.discounts.all()) == 1
        assert expected_discount == {
            'value': discount.value,
            'sku_category': discount.sku_category.id,
        }


@pytest.mark.django_db
class TestCheckVoucherWithExcludes:
    @pytest.mark.parametrize('kind_of', [VoucherType.ABSOLUTE, VoucherType.PERCENTAGE])
    def test_exclude_jetty_type01_white(
        self,
        kind_of,
        api_client,
        voucher_factory,
        cart_item_factory,
        cart_factory,
        jetty_factory,
    ):
        voucher = voucher_factory(
            active=True,
            kind_of=kind_of,
            item_conditionals={
                'exclude': [
                    {
                        'materials': [Type01Color.WHITE],
                        'shelf_types': [ShelfType.TYPE01],
                    },
                ],
            },
        )

        cart = cart_factory(items=[])
        jetty_excluded = jetty_factory(
            material=Type01Color.WHITE,
            shelf_type=ShelfType.TYPE01,
        )
        cart_item_factory(cart_item=jetty_excluded, cart=cart)

        jetty_included = jetty_factory(material=Type01Color.WHITE.DUSTY_PINK)
        cart_item_included = cart_item_factory(cart_item=jetty_included, cart=cart)
        CartService.recalculate_cart(cart)

        response = self._send_request(api_client, cart.owner, voucher.code)
        cart_item_included.refresh_from_db()

        self._make_price_assertion(
            cart=cart,
            included_item_price=cart_item_included.region_price,
            kind_of=kind_of,
            voucher_value=Decimal(voucher.value),
            expected_discount=response.data['order_pricing']['discount_value'],
        )

    @pytest.mark.parametrize('kind_of', [VoucherType.ABSOLUTE, VoucherType.PERCENTAGE])
    def test_exclude_type01_white_and_type02_black(
        self,
        kind_of,
        api_client,
        voucher_factory,
        cart_item_factory,
        cart_factory,
        jetty_factory,
    ):
        voucher = voucher_factory(
            kind_of=kind_of,
            item_conditionals={
                'exclude': [
                    {
                        'materials': [Type01Color.WHITE],
                        'shelf_types': [ShelfType.TYPE01],
                    },
                    {
                        'materials': [Type02Color.MATTE_BLACK],
                        'shelf_types': [ShelfType.TYPE02],
                    },
                ],
            },
        )

        cart = cart_factory(items=[])
        cart_item_factory(
            cart_item=jetty_factory(
                material=Type03Color.WHITE,
                shelf_type=ShelfType.TYPE01,
            ),
            cart=cart,
        )
        cart_item_excluded = cart_item_factory(
            cart_item=jetty_factory(
                material=Type02Color.MATTE_BLACK,
                shelf_type=ShelfType.TYPE02,
            ),
            cart=cart,
        )
        CartService.recalculate_cart(cart)

        response = self._send_request(api_client, cart.owner, voucher.code)
        cart_item_excluded.refresh_from_db()

        assert response.data['order_pricing']['discount_value'] == 0

    @pytest.mark.parametrize('kind_of', [VoucherType.ABSOLUTE, VoucherType.PERCENTAGE])
    def test_exclude_type03_white(
        self,
        kind_of,
        api_client,
        voucher_factory,
        cart_item_factory,
        cart_factory,
        watty_factory,
        jetty,
    ):
        voucher = voucher_factory(
            kind_of=kind_of,
            item_conditionals={
                'exclude': [
                    {
                        'materials': [Type03Color.WHITE],
                        'shelf_types': [ShelfType.TYPE03],
                    },
                ],
            },
        )

        cart = cart_factory(items=[])
        watty_excluded = watty_factory(
            material=Type03Color.WHITE,
            shelf_type=ShelfType.TYPE03,
        )
        cart_item_factory(cart_item=watty_excluded, cart=cart)
        cart_item_included = cart_item_factory(cart_item=jetty, cart=cart)
        CartService.recalculate_cart(cart)

        response = self._send_request(api_client, cart.owner, voucher.code)
        cart_item_included.refresh_from_db()

        self._make_price_assertion(
            cart=cart,
            included_item_price=cart_item_included.region_price,
            kind_of=kind_of,
            voucher_value=Decimal(voucher.value),
            expected_discount=response.data['order_pricing']['discount_value'],
        )

    @pytest.mark.parametrize('kind_of', [VoucherType.ABSOLUTE, VoucherType.PERCENTAGE])
    def test_exclude_sample_box(
        self,
        kind_of,
        api_client,
        voucher_factory,
        cart_item_factory,
        cart_factory,
        sample_box,
        jetty,
    ):
        voucher = voucher_factory(
            kind_of=kind_of,
            item_conditionals={
                'exclude': [{'furniture_types': [Furniture.sample_box.value]}],
            },
        )

        cart = cart_factory(items=[])
        cart_item_factory(cart_item=sample_box, cart=cart)
        cart_item_included = cart_item_factory(cart_item=jetty, cart=cart)
        CartService.recalculate_cart(cart)

        response = self._send_request(api_client, cart.owner, voucher.code)
        cart_item_included.refresh_from_db()

        self._make_price_assertion(
            cart=cart,
            included_item_price=cart_item_included.region_price,
            kind_of=kind_of,
            voucher_value=Decimal(voucher.value),
            expected_discount=response.data['order_pricing']['discount_value'],
        )

    @pytest.mark.parametrize('kind_of', [VoucherType.ABSOLUTE, VoucherType.PERCENTAGE])
    def test_exclude_jetty(
        self,
        kind_of,
        api_client,
        voucher_factory,
        cart_item_factory,
        cart_factory,
        watty,
        jetty,
    ):
        voucher = voucher_factory(
            kind_of=kind_of,
            item_conditionals={
                'exclude': [{'furniture_types': [Furniture.jetty.value]}],
            },
        )

        cart = cart_factory(items=[])
        cart_item_factory(cart_item=jetty, cart=cart)
        cart_item_included = cart_item_factory(cart_item=watty, cart=cart)
        CartService.recalculate_cart(cart)

        response = self._send_request(api_client, cart.owner, voucher.code)
        cart_item_included.refresh_from_db()

        self._make_price_assertion(
            cart=cart,
            included_item_price=cart_item_included.region_price,
            kind_of=kind_of,
            voucher_value=Decimal(voucher.value),
            expected_discount=response.data['order_pricing']['discount_value'],
        )

    @pytest.mark.parametrize('kind_of', [VoucherType.ABSOLUTE, VoucherType.PERCENTAGE])
    def test_exclude_sample_box_and_jetty_type02_pink(
        self,
        kind_of,
        api_client,
        voucher_factory,
        cart_item_factory,
        cart_factory,
        sample_box,
        jetty_factory,
    ):
        voucher = voucher_factory(
            kind_of=kind_of,
            item_conditionals={
                'exclude': [
                    {'furniture_types': [Furniture.sample_box.value]},
                    {
                        'materials': [Type02Color.REISINGERS_PINK],
                        'shelf_types': [ShelfType.TYPE02],
                    },
                ],
            },
        )

        cart = cart_factory(items=[])
        excluded_jetty = jetty_factory(
            material=Type02Color.REISINGERS_PINK,
            shelf_type=ShelfType.TYPE02,
        )
        cart_item_factory(cart_item=excluded_jetty, cart=cart)
        cart_item_factory(cart_item=sample_box, cart=cart)
        included_jetty = jetty_factory(
            material=Type02Color.WHITE,
            shelf_type=ShelfType.TYPE02,
        )
        cart_item_included = cart_item_factory(cart_item=included_jetty, cart=cart)
        CartService.recalculate_cart(cart)

        response = self._send_request(api_client, cart.owner, voucher.code)
        cart_item_included.refresh_from_db()

        self._make_price_assertion(
            cart=cart,
            included_item_price=cart_item_included.region_price,
            kind_of=kind_of,
            voucher_value=Decimal(voucher.value),
            expected_discount=response.data['order_pricing']['discount_value'],
        )

    @pytest.mark.parametrize('kind_of', [VoucherType.ABSOLUTE, VoucherType.PERCENTAGE])
    def test_exclude_all_shelf_types(
        self,
        kind_of,
        api_client,
        voucher_factory,
        cart_item_factory,
        cart_factory,
        sample_box,
        jetty_factory,
        watty_factory,
    ):
        voucher = voucher_factory(
            kind_of=kind_of,
            item_conditionals={
                'exclude': [
                    {
                        'shelf_types': [
                            ShelfType.TYPE01,
                            ShelfType.TYPE02,
                            ShelfType.VENEER_TYPE01,
                            ShelfType.TYPE03,
                            ShelfType.TYPE13,
                        ],
                    },
                ],
            },
        )

        cart = cart_factory(items=[])
        cart_item_factory(
            cart_item=jetty_factory(shelf_type=ShelfType.TYPE01),
            cart=cart,
        )
        cart_item_factory(
            cart_item=jetty_factory(shelf_type=ShelfType.VENEER_TYPE01),
            cart=cart,
        )
        cart_item_factory(
            cart_item=jetty_factory(shelf_type=ShelfType.TYPE02),
            cart=cart,
        )
        cart_item_factory(
            cart_item=watty_factory(shelf_type=ShelfType.TYPE03),
            cart=cart,
        )
        cart_item_factory(
            cart_item=watty_factory(shelf_type=ShelfType.TYPE13),
            cart=cart,
        )
        cart_item_included = cart_item_factory(cart_item=sample_box, cart=cart)
        CartService.recalculate_cart(cart)

        response = self._send_request(api_client, cart.owner, voucher.code)
        cart_item_included.refresh_from_db()

        self._make_price_assertion(
            cart=cart,
            included_item_price=cart_item_included.region_price,
            kind_of=kind_of,
            voucher_value=Decimal(voucher.value),
            expected_discount=response.data['order_pricing']['discount_value'],
        )

    @staticmethod
    def _send_request(api_client, user, voucher_code):
        url = reverse('rest_check_voucher', args=[voucher_code])
        api_client.force_authenticate(user=user)
        response = api_client.post(url, data={})
        return response

    @staticmethod
    def _make_price_assertion(
        cart,
        included_item_price,
        kind_of,
        voucher_value,
        expected_discount,
    ):
        currency_rate = Decimal(cart.region.currency.current_rate.rate)
        discounted_amount = (
            included_item_price * voucher_value / 100
            if kind_of == VoucherType.PERCENTAGE
            else voucher_value * currency_rate
        )

        assert expected_discount == pytest.approx(discounted_amount, abs=1)


class TestRemoveVoucherView:
    @pytest.mark.django_db
    def test_remove_voucher(
        self,
        user,
        client,
        voucher_factory,
        cart_factory,
    ):
        client.force_login(user=user)
        percentage_voucher = voucher_factory(is_percentage=True)
        absolute_voucher = voucher_factory(is_absolute=True)
        cart = cart_factory(owner=user, vouchers=[percentage_voucher, absolute_voucher])

        url = reverse('remove-voucher', kwargs={'code': percentage_voucher.code})
        response = client.post(url)
        voucher = cart.vouchers.first()
        assert response.status_code == 200
        assert response.json() == {
            'is_active': False,
            'status': VoucherStatusMessages.OK,
            'message': 'Removed voucher from cart',
        }
        assert cart.vouchers.all().count() == 1
        assert voucher.id == absolute_voucher.id
