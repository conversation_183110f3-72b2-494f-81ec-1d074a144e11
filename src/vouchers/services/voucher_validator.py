from typing import (
    TYPE_CHECKING,
    Union,
)

from django.db.models import QuerySet

from vouchers.models import Voucher
from vouchers.services.item_discount_condition_checker import validate_absolute_voucher

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order


class VouchersValidator:
    """validate if vouchers can be used"""

    def __init__(
        self, instance: Union['Cart', 'Order'], vouchers: QuerySet[Voucher]
    ) -> None:
        self.instance = instance
        self.vouchers = vouchers

    def __call__(self, *args, **kwargs):
        return [
            voucher
            for voucher in self.vouchers
            if self.check_if_voucher_applies(self.instance, voucher)
        ]

    @staticmethod
    def check_if_voucher_applies(
        instance: Union['Cart', 'Order'], voucher: Voucher
    ) -> bool:
        region = instance.get_region()

        if not voucher.is_active():
            return False

        if instance.is_order and instance.is_split():
            return instance.check_if_voucher_applies_for_split()

        if not (items := voucher.get_promotion_affected_items(instance)):
            return False

        try:
            total_price_for_items = instance.calculate_total_price_for_items(
                items=items
            )
        except AttributeError:
            return False

        if voucher.is_absolute() and not validate_absolute_voucher(
            instance=instance, voucher=voucher
        ):
            return False

        region_voucher = voucher.get_region_entry(region)
        if (
            region_voucher.amount_starts
            <= total_price_for_items
            <= region_voucher.amount_limit
        ):
            return True

        return False
