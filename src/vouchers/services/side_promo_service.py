from typing import (
    TYPE_CHECKING,
    Union,
)

from vouchers.exceptions import (
    MaxNumberVouchersReached,
    MaxStackableVouchersReached,
)
from vouchers.services.voucher_validator import VouchersValidator

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order
    from vouchers.models import Voucher


class SidePromotionService:
    def __init__(self, instance: Union['Cart', 'Order'], call_back_function) -> None:
        self.instance = instance
        self.call_back_function = call_back_function

    def handle_side_promotion(self):
        side_promotion = self._get_side_promotion()
        if not side_promotion:
            return

        if self._should_add_side_promotion(side_promotion):
            self._add_side_promotion(side_promotion)
        elif self._should_remove_side_promotion(side_promotion):
            self._remove_side_promotion(side_promotion)

    def _get_side_promotion(self):
        if not self.instance.main_voucher:
            return None
        return self.instance.main_voucher.side_promotion

    def _should_add_side_promotion(self, side_promotion: 'Voucher') -> bool:
        return (
            VouchersValidator.check_if_voucher_applies(
                instance=self.instance, voucher=side_promotion
            )
            and not self.instance.side_promotion
        )

    def _add_side_promotion(self, side_promotion: 'Voucher') -> None:
        from carts.services.cart_service import CartService

        try:
            CartService.can_voucher_be_applicable(side_promotion, self.instance)
        except (MaxStackableVouchersReached, MaxNumberVouchersReached):
            return  # Silently fail if voucher limits reached
        else:
            self.instance.vouchers.add(side_promotion)
            self.call_back_function()

    def _should_remove_side_promotion(self, side_promotion: 'Voucher') -> bool:
        return (
            not VouchersValidator.check_if_voucher_applies(
                instance=self.instance, voucher=side_promotion
            )
            and self.instance.side_promotion
        )

    def _remove_side_promotion(self, side_promotion: 'Voucher') -> None:
        self.instance.vouchers.remove(side_promotion)
        self.call_back_function()
