from decimal import Decimal
from functools import lru_cache
from typing import (
    TYPE_CHECKING,
    Union,
)

from django.core.exceptions import ObjectDoesNotExist

from gallery.enums import CollectiveFurnitureCategory

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from gallery.types import MaterialItemType
    from orders.models import (
        Order,
        OrderItem,
    )
    from vouchers.models import (
        ItemDiscount,
        Voucher,
    )


def get_bundle_discount_price(
    voucher: 'Voucher',
    sellable_item: 'MaterialItemType',
    regular_price: Decimal,
    instance: 'Order | Cart' = None,
) -> Decimal | None:
    if not (voucher.has_bundle() and instance):
        return

    bundle_furniture_discounts = voucher.bundle.item_discounts.filter(
        service_type__isnull=True,
    )
    items = tuple(item for item in instance.items.all())
    if not does_bundle_apply_to_items(voucher, items, bundle_furniture_discounts):
        return

    for discount in bundle_furniture_discounts:
        if does_discount_apply(discount, sellable_item):
            return discount.calculate_price(regular_price)


@lru_cache(maxsize=1000)
def does_bundle_apply_to_items(
    voucher: 'Voucher',
    items: 'tuple[CartItem | OrderItem]',
    bundle_discounts: tuple['ItemDiscount'],  # Changed from QuerySet for caching
) -> bool:
    if not voucher.has_bundle():
        return False

    bundle = voucher.bundle
    matched_items_count = 0

    if bundle.amount_starts:
        total_price = sum(Decimal(item.sellable_item.price) for item in items)
        if total_price < bundle.amount_starts:
            return False

    for discount in bundle_discounts:
        discount_matched = False

        for item in items:
            if does_discount_apply(discount, item.sellable_item):
                matched_items_count += item.quantity
                discount_matched = True

        if not discount_matched:
            return False

    return matched_items_count >= bundle.minimum_items_required


def validate_absolute_voucher(
    instance: Union['Cart', 'Order'], voucher: 'Voucher'
) -> bool:
    try:
        bundle_furniture_discounts = voucher.bundle.item_discounts.filter(
            service_type__isnull=True,
        )
    except ObjectDoesNotExist:
        return True

    # Remove cache from items
    fresh_instance = instance.__class__.objects.get(pk=instance.pk)
    items = tuple(item for item in fresh_instance.items.all())

    if bundle_furniture_discounts and not does_bundle_apply_to_items(
        voucher=voucher, items=items, bundle_discounts=bundle_furniture_discounts
    ):
        return False
    return True


def does_discount_apply(
    discount: 'ItemDiscount',
    item: 'MaterialItemType',
) -> bool:
    """If all filter fields are the same at self and item - then the discount is
    valid. In other words - filters are validated on AND logic.
    If ItemDiscount is about a service - it's not counted for one item, but for
    whole order.
    Made a separate function to make caching easier.
    """
    if discount.service_type:
        return False
    for filter_field in discount.FILTER_FIELDS:
        try:
            if getattr(discount, filter_field) in {None, ''}:
                # if instance does not store value in given field - skip
                continue
        except ObjectDoesNotExist:
            continue
        try:
            if filter_field == 'collective_category':
                collective_category = getattr(discount, filter_field)
                categories = CollectiveFurnitureCategory(
                    collective_category
                ).get_furniture_categories()
                return any(
                    item.furniture_category == category for category in categories
                )
            elif getattr(item, filter_field) != getattr(discount, filter_field):
                # if discount value is different from item quality -
                # discount is not valid
                return False
        except AttributeError:
            # item might be a jetty, watty or sample box, and they vary in fields
            # we're filtering on. So if we want to discount box_variant this should
            # not apply to jetty, etc.
            return False
    return True
