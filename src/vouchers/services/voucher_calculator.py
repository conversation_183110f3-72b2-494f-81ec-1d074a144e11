from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    TYPE_CHECKING,
    Iterable,
    Optional,
    Union,
)

from django.db.models import QuerySet

from regions.types import RegionLikeObject
from vouchers.models import Voucher
from vouchers.utils import convert_absolute_voucher_to_percentage

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from orders.models import (
        Order,
        OrderItem,
    )


class VoucherCalculator:
    """
    Calculates the total promo amount for a given cart/order
    after applying all applicable vouchers.
    """

    def __init__(
        self,
        instance: Union['Cart', 'Order'],
        vouchers: Iterable[Voucher],
        region: Optional[RegionLikeObject] = None,
    ) -> None:
        self.instance = instance
        self.region = region
        self.vouchers = vouchers
        self.billable_vouchers = [
            voucher for voucher in vouchers if not voucher.ignore_on_invoice
        ]
        self.region_total_price = self.instance.get_total_value()

    def __call__(self) -> tuple[Decimal, Decimal]:
        return (
            self._calculate_region_promo_value(self.vouchers),
            self._calculate_region_promo_value(self.billable_vouchers),
        )

    def _calculate_region_promo_value(self, vouchers: Iterable[Voucher]) -> Decimal:
        """
        Calculates the total price by stacking all applicable vouchers.

        The discount calculator works as follows:
        total_price = initial_price * (1 - (percentage_voucher_1+percentage_voucher_2))

        - For example, two 10% discounts result in a 20% total discount.
        """
        return min(
            sum(
                (
                    voucher.calculate_promo_amount(
                        instance=self.instance,
                        region_total_price=self.region_total_price,
                        region=self.region,
                    )
                    for voucher in vouchers
                ),
                Decimal('0.00'),
            ),
            self.region_total_price,
        )


class VoucherItemCalculator:
    """
    Calculates the total promo amount for a given cartItem/orderItem
    after applying all vouchers that would be shown on an invoice.

    `delivery_promo_value` and `region_promo_value` are only used for invoicing,
    so we can do it here.
    """

    def __init__(
        self,
        item: Union['CartItem', 'OrderItem'],
        vouchers: QuerySet['Voucher'] = None,
    ) -> None:
        self.item = item
        self.instance = getattr(self.item, 'cart', None) or self.item.order
        if vouchers is not None:
            self.vouchers = vouchers
        else:
            self.vouchers = self.instance.vouchers.filter(ignore_on_invoice=False)

    def calculate_delivery_promo_value(self):
        return sum(
            (
                self._calculate_delivery_promo_value(voucher)
                for voucher in self.vouchers
            ),
            Decimal('0.00'),
        )

    def calculate_region_promo_value(self) -> Decimal:
        return sum(
            (
                self._calculate_region_promo_value(voucher)
                for voucher in self.vouchers
                if self._check_if_voucher_apply(voucher)
            ),
            Decimal('0.00'),
        )

    @staticmethod
    def _round_up_to_hundredth(value: Decimal) -> Decimal:
        return Decimal(value).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def _round_up_to_integer(value: Decimal) -> Decimal:
        return Decimal(value).quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    def _check_if_voucher_apply(self, voucher: Voucher) -> bool:
        return voucher.check_item_applicability(item=self.item)

    def _calculate_delivery_promo_value(self, voucher: Voucher):
        return self.item.region_delivery_price - self._round_up_to_integer(
            voucher.get_delivery_price_with_discount(self.item.region_delivery_price)
        )

    def _calculate_region_promo_value(self, voucher) -> Decimal:
        if voucher.is_percentage():
            return self._round_up_to_hundredth(
                self.item.region_price
                - voucher.calculate_price_for_furniture(
                    furniture=self.item.sellable_item,
                    price=self.item.region_price,
                    instance=self.instance,
                )
            )
        else:
            promo_value_rate = convert_absolute_voucher_to_percentage(
                instance=self.instance, voucher=voucher
            )
            return self._round_up_to_hundredth(
                self.item.region_price * promo_value_rate
            )
