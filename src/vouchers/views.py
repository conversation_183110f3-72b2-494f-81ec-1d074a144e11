from django.db.models import Prefetch
from django.http import Http404
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import (
    AllowAny,
    IsAuthenticated,
)
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from carts.models import CartItem
from carts.services.cart_service import CartService
from custom.enums import Furniture
from ecommerce_api.mixins import EcommerceAPIMixin
from promotions.utils import get_active_promotion
from vouchers.enums import VoucherStatusMessages
from vouchers.exceptions import CartNotFoundException
from vouchers.models import Voucher
from vouchers.serializers import (
    CheckVoucherResponseSerializer,
    MailingAbsoluteVoucherSerializer,
    MailingPercentageVoucherSerializer,
    MailingSampleSerializer,
    PromoMockJettySerializer,
    PromoMockSottySerializer,
    PromoMockWattySerializer,
    VoucherSerializer,
)
from vouchers.services.voucher_service import VoucherService


class VoucherAPIMixin:
    def initial(self, request: Request, *args, **kwargs) -> None:
        super().initial(request, *args, **kwargs)  # runs authentication + permissions
        # NOTE: the use of status code 400 instead of 404 is intended.
        # 404 in nuxt redirects to 404 page which is unwanted behaviour in this case.
        select_related_fields = [
            'region',
        ]
        prefetch_related_fields = [
            'vouchers',
            'vouchers__discounts',
            'vouchers__bundle',
            Prefetch(
                'items',
                queryset=CartItem.objects.select_related('content_type').prefetch_related('cart_item')
            ),
            'region__countries',
            'region__countries__countryregionvat_set',
        ]
        user = request.user
        self.cart = CartService.get_cart(
            user,
            prefetch_related_fields=prefetch_related_fields,
            select_related_fields=select_related_fields,
            update_strike_through_promo=False,
        )
        if self.cart is None:
            raise CartNotFoundException()


class CheckVoucherAPIView(VoucherAPIMixin, APIView):
    serializer_class = VoucherSerializer
    permission_classes = (IsAuthenticated,)
    throttle_classes = ()

    def post(self, request: Request, code: str | None = None) -> Response:
        if code is None:
            CartService(self.cart).reset_promo()
            data = {
                'is_active': False,
                'status': VoucherStatusMessages.OK,
                'message': _('Removed voucher from cart'),
            }
            return Response(data, status=status.HTTP_200_OK)

        voucher_service = VoucherService(instance=self.cart, code=code.rstrip())
        return_data = voucher_service.process_voucher()
        serializer = CheckVoucherResponseSerializer(instance=self.cart)
        return_data.update(serializer.data)

        if return_data.get('status') == VoucherStatusMessages.OK:
            return_status = status.HTTP_200_OK
        else:
            return_status = status.HTTP_400_BAD_REQUEST

        return Response(
            return_data,
            status=return_status,
        )


class RemoveVoucherAPIView(VoucherAPIMixin, APIView):
    serializer_class = VoucherSerializer
    permission_classes = (IsAuthenticated,)
    throttle_classes = ()

    def post(self, request: Request, code: str) -> Response:
        voucher_service = VoucherService(instance=self.cart, code=code.rstrip())
        voucher_service.remove_voucher()
        data = {
            'is_active': False,
            'status': VoucherStatusMessages.OK,
            'message': _('Removed voucher from cart'),
        }
        return Response(data, status=status.HTTP_200_OK)


class GlobalPromoViewSet(EcommerceAPIMixin, GenericViewSet):
    queryset = Voucher.objects.all()
    permission_classes = (AllowAny,)

    def get_serializer_class(self, *args, **kwargs):
        try:
            furniture_type = Furniture(self.request.query_params.get('furniture_type'))
        except ValueError:
            raise ValidationError('Incorrect furniture_type.')
        match furniture_type:
            case Furniture.jetty:
                return PromoMockJettySerializer
            case Furniture.sotty:
                return PromoMockSottySerializer
            case Furniture.watty:
                return PromoMockWattySerializer
            case _:
                raise ValidationError('Incorrect furniture_type.')

    def get_object(self):
        promotion = get_active_promotion(self.region)
        if not promotion:
            raise Http404()
        return promotion.promo_code

    @action(methods=['get'], detail=False)
    def discount(self, request, *args, **kwarg):
        serializer = self.get_serializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        voucher = self.get_object()
        return Response(
            {
                'value': voucher.get_discount_value_for_item(
                    serializer.create(serializer.validated_data)
                )
            },
            status=status.HTTP_200_OK,
        )


class MailingAbsoluteVoucherCreateView(CreateAPIView):
    serializer_class = MailingAbsoluteVoucherSerializer
    authentication_classes = (TokenAuthentication,)


class MailingPercentageVoucherCreateView(CreateAPIView):
    serializer_class = MailingPercentageVoucherSerializer
    authentication_classes = (TokenAuthentication,)


class MailingSampleVoucherCreateView(CreateAPIView):
    serializer_class = MailingSampleSerializer
    authentication_classes = (TokenAuthentication,)
