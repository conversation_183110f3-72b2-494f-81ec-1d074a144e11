from django.contrib import admin
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.forms import BaseInlineFormSet
from django.http import HttpResponseRedirect
from django.shortcuts import render
from django.urls import reverse

from custom.admin import SingletonAdmin
from custom.enums import ShelfType
from custom.enums.enums import AdminGroup
from custom.utils.exports import (
    dump_list_as_txt,
    export_modeladmin_as_csv,
)
from promotions.models import Promotion
from regions.models import (
    Currency,
    Region,
)
from vouchers.enums import ServiceType
from vouchers.forms import (
    ItemDiscountForm,
    VoucherEmailAndQuantityForm,
    VoucherEmailForm,
    VoucherModelForm,
    VoucherRegionEntryModelForm,
    VoucherSettingsForm,
    VoucherValuesMassChange,
)
from vouchers.models import (
    ItemDiscount,
    Voucher,
    VoucherBarterDeal,
    VoucherBundle,
    VoucherGroup,
    VoucherRegionEntry,
    VoucherSettings,
)
from vouchers.tasks import update_b2b_vouchers_discounts
from vouchers.utils import create_b2b_vouchers


class VoucherSettingsAdmin(SingletonAdmin, admin.ModelAdmin):
    model = VoucherSettings
    form = VoucherSettingsForm

    def _update_b2b_vouchers_on_settings_change(self, request, obj, form, change):
        if not change:
            return

        old_sotty_discount = VoucherSettings.get_b2b_sotty_discount_value()
        old_delivery_discount = VoucherSettings.get_b2b_delivery_discount_value()
        new_sotty_discount = form.cleaned_data['_b2b_sotty_discount_value']
        new_delivery_discount = form.cleaned_data['_b2b_delivery_discount_value']

        if old_sotty_discount != new_sotty_discount:
            update_b2b_vouchers_discounts.delay(
                str(old_sotty_discount) if old_sotty_discount else None,
                str(new_sotty_discount) if new_sotty_discount else None,
                {'shelf_type': ShelfType.SOFA_TYPE01},
            )
            VoucherSettings.get_b2b_sotty_discount_value.cache_clear()

        if old_delivery_discount != new_delivery_discount:
            update_b2b_vouchers_discounts.delay(
                str(old_delivery_discount) if old_delivery_discount else None,
                str(new_delivery_discount) if new_delivery_discount else None,
                {'service_type': ServiceType.DELIVERY},
            )
            VoucherSettings.get_b2b_delivery_discount_value.cache_clear()

    def save_model(self, request, obj, form, change):
        self._update_b2b_vouchers_on_settings_change(request, obj, form, change)
        super().save_model(request, obj, form, change)


class VoucherRegionEntryFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        regions = Region.objects.all()
        for form, region in zip(self.forms, regions):
            form.fields['region'].initial = region


class VoucherRegionEntryInline(admin.TabularInline):
    form = VoucherRegionEntryModelForm
    formset = VoucherRegionEntryFormSet
    model = VoucherRegionEntry
    verbose_name_plural = 'Voucher Region Entries'

    def get_extra(self, request, obj=None, **kwargs):
        return Region.objects.count()

    def get_max_num(self, request, obj=None, **kwargs):
        return Region.objects.count()


class ItemDiscountTabularInline(admin.TabularInline):
    model = Voucher.discounts.through
    verbose_name_plural = 'Item Discounts'


class VoucherBundleInline(admin.TabularInline):
    model = VoucherBundle


class VoucherBarterDealInline(admin.TabularInline):
    model = VoucherBarterDeal


class ItemDiscountAdmin(admin.ModelAdmin):
    model = ItemDiscount
    form = ItemDiscountForm
    list_display = (
        'value',
        'shelf_type',
        'furniture_type',
        'material',
        'category_display',
        'service_type',
    )
    fieldsets = (
        ('Value', {'fields': ('value',)}),
        (
            'Conditionals',
            {
                'fields': (
                    'shelf_type',
                    'furniture_type',
                    'material',
                    'collective_category',
                    'category',
                    'service_type',
                ),
                'description': 'Remember that some fields might be mutually exclusive',
            },
        ),
    )

    def category_display(self, obj: ItemDiscount) -> str:
        if obj.furniture_category:
            return obj.furniture_category
        if obj.sku_category_id:
            return obj.sku_category.name
        return '-'

    category_display.short_description = 'Category'


class VoucherFromPromotionListFilter(admin.SimpleListFilter):
    title = 'promotions'
    parameter_name = 'vouchers_from_promotions'

    def lookups(self, request, model_admin):
        return (
            ('from_promotions', 'Vouchers from promotions'),
            ('other', 'Other vouchers'),
        )

    def queryset(self, request, queryset):
        vouchers_from_promotions = Promotion.objects.filter(
            promo_code__isnull=False,
        ).values_list('promo_code__id', flat=True)
        if self.value() == 'from_promotions':
            return queryset.filter(id__in=vouchers_from_promotions)
        elif self.value() == 'other':
            return queryset.exclude(id__in=vouchers_from_promotions)


class VoucherGroupListFilter(admin.SimpleListFilter):
    """
    This filter is added to exclude VoucherGroups that are threshold vouchers
    from being listed, since they relate to individual vouchers not to promotions.
    """

    title = 'group'
    parameter_name = 'group__id'

    def lookups(self, request, model_admin):
        groups = VoucherGroup.objects.exclude(promotions__isnull=True)
        return ((group.pk, group) for group in groups)

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(group__pk=self.value())
        return queryset


def group_code(obj):
    group = obj.group
    return getattr(group, 'code', '-')


def voucher_quantity(obj):
    if obj.quantity == -1:
        return 'unlimited'
    return obj.quantity


class VoucherAdmin(admin.ModelAdmin):
    form = VoucherModelForm
    actions = [
        export_modeladmin_as_csv,
        'create_b2b_voucher',
        'create_and_email_bulk_b2b_vouchers',
        'change_voucher_values',
        'change_end_date',
        'populate_region_entries',
    ]
    fieldsets = (
        (
            'General information',
            {
                'fields': (
                    'active',
                    'is_stackable',
                    'code',
                    'kind_of',
                    'value',
                    'origin',
                    'group',
                    'start_date',
                    'end_date',
                    'quantity',
                    'quantity_left',
                    'amount_starts',
                    'amount_limit',
                    'for_email',
                    'item_conditionals',
                    'notes',
                    'ignore_on_invoice',
                ),
            },
        ),
        (
            'Cheapest item promotion',
            {
                'fields': (
                    'cheaper_items_number',
                    'cheapest_discount_value',
                )
            },
        ),
        (
            'Mystery box promotion',
            {'fields': ('mystery_box_id',)},
        ),
        (
            'Side promotion',
            {'fields': ('side_promotion',)},
        ),
    )
    date_hierarchy = 'created_at'
    inlines = [
        ItemDiscountTabularInline,
        VoucherRegionEntryInline,
        VoucherBarterDealInline,
        VoucherBundleInline,
    ]
    list_display = (
        'code',
        'kind_of',
        'value',
        voucher_quantity,
        'quantity_left',
        'amount_starts',
        'creator',
        'created_at',
        'start_date',
        'end_date',
        'get_times_paid',
        group_code,
        'barter_deal',
    )
    list_filter = (
        'active',
        'kind_of',
        VoucherGroupListFilter,
        'origin',
        VoucherFromPromotionListFilter,
    )
    raw_id_fields = ('side_promotion',)
    list_select_related = ('barter_deal', 'creator')
    search_fields = ('code', 'group__code')

    @admin.action(description='Create B2B voucher')
    def create_b2b_voucher(self, request, queryset):
        _selected_action = request.POST.getlist(ACTION_CHECKBOX_NAME)
        form = None
        if 'apply' in request.POST:
            form = VoucherEmailForm(request.POST)
            if form.is_valid():
                email = form.cleaned_data['email']
                b2b_code = create_b2b_vouchers(quantity=1, for_email=email)[0]
                self.message_user(
                    request,
                    f'Voucher {b2b_code} for B2B client with email {email} has '
                    f'been created!',
                )
                return HttpResponseRedirect(
                    reverse('admin:vouchers_voucher_changelist')
                )
        if not form:
            form = VoucherEmailForm(initial={'_selected_action': _selected_action})
        opts = self.model._meta
        app_label = opts.app_label
        voucher_description = 'Create B2B voucher'
        return render(
            request,
            'admin/vouchers/voucher_email.html',
            {
                'stacks': queryset,
                'form': form,
                'opts': opts,
                'app_label': app_label,
                'select_across': request.POST['select_across'],
                'name': 'B2B voucher',
                'voucher_description': voucher_description,
                'button_text': 'Generate voucher for email',
                'admin_action': 'create_b2b_voucher',
            },
        )

    @admin.action(description='Create and send by email B2B vouchers')
    def create_and_email_bulk_b2b_vouchers(self, request, queryset):
        _selected_action = request.POST.getlist(ACTION_CHECKBOX_NAME)
        form = None
        if 'apply' in request.POST:
            form = VoucherEmailAndQuantityForm(request.POST)
            if form.is_valid():
                email = form.cleaned_data['email']
                quantity = form.cleaned_data['quantity']
                codes = create_b2b_vouchers(quantity=quantity)
                dump_list_as_txt(
                    codes,
                    output='codes_for_b2b_vouchers.txt',
                    mail=(email,),
                    mail_body='Codes for B2B vouchers.',
                    mail_subject='Codes for B2B vouchers',
                )
                self.message_user(
                    request,
                    f'{quantity} vouchers have been created and sent to {email}.',
                )
                return HttpResponseRedirect(
                    reverse('admin:vouchers_voucher_changelist')
                )
        if not form:
            form = VoucherEmailAndQuantityForm(
                initial={'_selected_action': _selected_action},
            )
        opts = self.model._meta
        app_label = opts.app_label
        voucher_description = 'Create B2B vouchers'
        return render(
            request,
            'admin/vouchers/voucher_email.html',
            {
                'stacks': queryset,
                'form': form,
                'opts': opts,
                'app_label': app_label,
                'select_across': request.POST['select_across'],
                'name': 'B2B voucher',
                'voucher_description': voucher_description,
                'button_text': 'Create vouchers and send by email',
                'admin_action': 'create_and_email_bulk_b2b_vouchers',
            },
        )

    def get_readonly_fields(self, request, obj=None):
        """Make "value" read-only on edit if user is not from accounting."""
        read_only_fields = super().get_readonly_fields(request, obj)
        if (
            request.user.groups.filter(name=AdminGroup.ACCOUNTING).exists()
            or obj is None
        ):
            return read_only_fields
        return read_only_fields + ('value',)  # noqa: RUF005

    def get_fields(self, request, obj=None):
        """Overrides the default implementation to show readonly fields first"""
        if self.fields:
            return self.fields
        form = self._get_form_for_get_fields(request, obj)
        return [*self.get_readonly_fields(request, obj), *form.base_fields]

    def change_voucher_values(self, request, queryset):
        vouchers = queryset
        currencies = Currency.objects.exclude(code='USD').all().order_by('pk')
        forms = []
        if 'apply' in request.POST:
            valid = True
            for currency in currencies:
                form = VoucherValuesMassChange(request.POST, prefix=currency.pk)
                if not form.is_valid():
                    valid = False
                forms.append(form)
            if valid:
                for voucher in vouchers:
                    for form in forms:
                        voucher_region_entries = VoucherRegionEntry.objects.filter(
                            voucher=voucher,
                            region__currency__code=form.cleaned_data['currency'],
                        )
                        update = {'value': form.cleaned_data['in_currency']}
                        voucher_region_entries.update(**update)
                self.message_user(request, 'Vouchers were updated')
                return HttpResponseRedirect(
                    reverse('admin:vouchers_voucher_changelist')
                )
        if not forms:
            for currency in currencies:
                voucher_region_entry = VoucherRegionEntry.objects.filter(
                    voucher=vouchers.first(), region__currency=currency
                ).first()
                init = {'currency': currency.code}
                if voucher_region_entry:
                    init.update(
                        {
                            '_selected_action': request.POST.getlist(
                                ACTION_CHECKBOX_NAME
                            ),
                            'in_currency': voucher_region_entry.value,
                            'amount_starts': voucher_region_entry.amount_starts,
                            'amount_limit': voucher_region_entry.amount_limit,
                        }
                    )
                forms.append(VoucherValuesMassChange(prefix=currency.pk, initial=init))
        opts = self.model._meta
        app_label = opts.app_label
        template = 'admin/vouchers/vouchers_value_change.html'
        return render(
            request,
            template,
            {
                'queryset': queryset,
                'form': forms,
                'opts': opts,
                'app_label': app_label,
                'action_name': 'change_voucher_value',
                'select_across': request.POST['select_across'],
            },
        )

    change_voucher_values.short_description = 'Change vouchers values'

    def populate_region_entries(self, request, queryset):
        for voucher in queryset:
            voucher.create_region_entries(force_create=True)
            self.message_user(
                request,
                f'Voucher "{voucher.code}" has been populated with region entries!',
            )

    def save_model(self, request, obj, form, change):
        if not change:
            obj.creator = request.user
        obj.save()


class VoucherRegionEntryAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'region',
        'value',
        'get_currency_code',
        'amount_starts',
        'amount_limit',
    )
    model = VoucherRegionEntry
    raw_id_fields = ('voucher',)
    readonly_fields = ('get_currency_code',)


class GlobalPromoVoucherGroupListFilter(admin.SimpleListFilter):
    title = 'global promo voucher group'
    parameter_name = 'is_from_promo'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Yes'),
            ('no', 'No'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.exclude(promotions__isnull=True)
        elif self.value() == 'no':
            return queryset.filter(promotions__isnull=True)
        return queryset


class VoucherGroupAdmin(admin.ModelAdmin):
    filter_horizontal = ('region',)
    list_display = ('id', 'code')
    list_filter = (GlobalPromoVoucherGroupListFilter,)


class ItemDiscountBundleTabularInline(admin.TabularInline):
    model = VoucherBundle.item_discounts.through
    verbose_name_plural = 'Item Discounts'


class AdminVoucherBundle(admin.ModelAdmin):
    list_display = (
        'id',
        'minimum_items_required',
        'amount_starts',
        'voucher',
    )
    inlines = [ItemDiscountBundleTabularInline]
    fieldsets = (
        (None, {'fields': ('voucher', 'minimum_items_required', 'amount_starts')}),
    )


admin.site.register(VoucherSettings, VoucherSettingsAdmin)
admin.site.register(Voucher, VoucherAdmin)
admin.site.register(VoucherRegionEntry, VoucherRegionEntryAdmin)
admin.site.register(VoucherGroup, VoucherGroupAdmin)
admin.site.register(ItemDiscount, ItemDiscountAdmin)
admin.site.register(VoucherBundle, AdminVoucherBundle)
