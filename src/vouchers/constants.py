from dataclasses import dataclass
from decimal import Decimal
from typing import Final

from django.core.cache import cache

from immutabledict import immutabledict

from custom.models import GlobalSettings
from services.enums import AdditionalServiceKind


@dataclass
class Promo:
    discount_value: Decimal  # percentage
    amount_starts: int


ASSEMBLY_PROMO_CACHE_KEY: Final[str] = 'assembly_promo'


def is_assembly_promo_active() -> bool:
    cached_value = cache.get(ASSEMBLY_PROMO_CACHE_KEY)
    if cached_value is not None:
        return cached_value
    value = GlobalSettings.is_assembly_promo_active()
    cache.set(ASSEMBLY_PROMO_CACHE_KEY, value, 60 * 60 * 24)
    return value


def clean_assembly_promo_cache() -> None:
    cache.delete(ASSEMBLY_PROMO_CACHE_KEY)


# Assembly promo
ASSEMBLY_PROMO_DISCOUNT_VALUE: Final[Decimal] = Decimal('0.3')  # percentage
ASSEMBLY_PROMO_AMOUNT_STARTS: Final[int] = 2_000  # in euro
ASSEMBLY_VOUCHER_CODE: Final[str] = 'DEAL1'

# Additional service promo
ADDITIONAL_SERVICE_PROMO: Final[immutabledict[AdditionalServiceKind, Promo]] = (
    immutabledict(
        {
            AdditionalServiceKind.WHITE_GLOVES_DELIVERY: Promo(
                discount_value=ASSEMBLY_PROMO_DISCOUNT_VALUE,
                amount_starts=ASSEMBLY_PROMO_AMOUNT_STARTS,
            ),
        }
    )
)

SOFA_DEFAULT_DISCOUNT_VALUE: Final[int] = 0
VOUCHER_ABSOLUTE_LIMIT: Final[int] = 1_000
VOUCHER_PERCENTAGE_LIMIT: Final[int] = 50
VOUCHER_B2B_LIMIT: Final[int] = 10

VOUCHER_B2B_PREFIX = 'b2b'
VOUCHER_KSB2B_PREFIX = 'ksb2b'
