from unittest.mock import patch

from django.test import override_settings
from django.urls import reverse
from rest_framework import status

import pytest

from events.models import Event
from events.utils import hash_normalized_string
from mailing.constants import NEWSLETTER_VOUCHER_VALUES
from regions.services.regionalized_price import RegionCalculationsObject


@patch('orders.models.Order.get_total_price', return_value=100)
@patch('orders.models.Order.display_regionalized', return_value=100)
@patch(
    'pricing_v3.services.price_calculators.OrderPriceCalculator.calculate',
    return_value=100,
)
@patch(
    'gallery.models.furniture_abstract.FurnitureAbstract.get_recycle_tax_value',
    return_value=0,
)
def test_remove_unavailable_items_from_cart_on_region_change(
    mocked_get_recycle_tax_value,
    mock_price_calculator,
    mocked_display_regionalized,
    mocked_get_total_price,
    db,
    settings,
    user,
    watty,
    region_factory,
    api_client,
    cart_factory,
    cart_item_factory,
):
    cart = cart_factory(owner=user)
    cart_item_factory(cart=cart, cart_item=watty)
    settings.T03_REGION_KEYS = {'poland', 'germany', 'france'}
    data = {'region_name': 'belgium'}
    region_factory(name=data['region_name'])
    api_client.force_authenticate(user)

    assert cart.items.count() == 3
    assert watty in [item.sellable_item for item in cart.items.all()]

    # Change region where T03 is not available
    response = api_client.post(reverse('rest_change_region'), data)

    assert response.status_code == status.HTTP_200_OK
    assert cart.items.count() == 2
    assert watty not in [item.sellable_item for item in cart.items.all()]


@pytest.mark.django_db
class TestChangeRegion:
    url = reverse('rest_change_region')

    def test_returns_error_on_empty_request(self, api_client, user):
        expected_response = {
            'status': 'error',
            'message': 'Invalid data',
            'details': {'region_name': ['This field is required.']},
        }

        response = api_client.post(self.url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == expected_response

    def test_returns_error_on_non_existent_region_name(self, api_client, user):
        expected_response = {
            'status': 'error',
            'message': 'Given region does not exist.',
        }

        response = api_client.post(self.url, data={'region_name': 'INVALID'})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == expected_response

    def test_change_region_for_authenticated_user(
        self,
        api_client,
        user,
        country_factory,
        cart_factory,
    ):
        cart_factory(owner=user)
        country = country_factory(germany=True)
        request_data = {'region_name': country.region.name}
        api_client.force_authenticate(user)

        response = api_client.post(self.url, data=request_data)
        user.profile.refresh_from_db()

        assert response.status_code == status.HTTP_200_OK
        assert user.profile.region == country.region

    def test_change_region_for_unauthenticated_user(
        self,
        api_client,
        country_factory,
    ):
        country = country_factory(germany=True)
        request_data = {'region_name': country.region.name}

        response = api_client.post(self.url, data=request_data)

        assert response.status_code == status.HTTP_200_OK

    def test_change_region_emits_region_update_event_with_expected_properties(
        self,
        api_client,
        user,
        country_factory,
        cart_factory,
    ):
        cart_factory(owner=user)
        country = country_factory(germany=True)
        region = country.region
        request_data = {'region_name': region.name}
        api_client.force_login(user)

        api_client.post(self.url, data=request_data)

        user.profile.refresh_from_db()

        expected_properties = {
            'external_id': hash_normalized_string(user.profile.email),
            'country': country.code.upper(),
            'language': user.profile.language,
            'currency_code': region.currency.code,
        }

        events = Event.objects.all()
        assert events.count() == 1
        assert events.last().event_name == 'RegionUpdateEvent'
        assert events.last().properties == expected_properties


@pytest.mark.django_db
class TestRegionGlobalAPIView:
    url = reverse('region-global')

    @pytest.mark.parametrize('is_authenticated', (True, False))  # noqa: PT007
    @patch('regions.serializers.ABTest.objects.get_active_tests_cached_list')
    def test_user_response(
        self,
        ab_tests_mock,
        is_authenticated,
        ab_test_factory,
        user_factory,
        sample_price_settings_factory,
        region_de,
        api_client,
        mocker,
    ):
        ab_test = ab_test_factory(active=True, feature_flag=True, regions=[region_de])
        ab_tests_mock.return_value = [
            ab_test,
            ab_test_factory(
                active=True,
                feature_flag=False,
                regions=[region_de],  # not a ff
            ),
        ]
        user = user_factory(profile__region=region_de)
        region_data = region_de.cached_region_data
        sample_prices = sample_price_settings_factory(
            storage_sample_price=2,
            storage_sample_sale_price=1,
            storage_sample_promo_active=True,
            sofa_sample_price=1,
            sofa_sample_sale_price=0,
            sofa_sample_promo_active=True,
        )

        if is_authenticated:
            api_client.force_authenticate(user)
        else:
            mocker.patch(
                'user_profile.middleware.RegionSelector.get_region',
                return_value=region_de,
            )

        with override_settings(
            ASSEMBLY_REGION_KEYS={region_data.name},
            CORDUROY_RESTRICTED_REGIONS={region_data.name},
            DOORSTEP_SOTTY_DELIVERY_REGIONS={region_data.name},
            OLD_SOFA_COLLECTION_REGIONS={region_data.name},
            S01_REGION_KEYS={region_data.name},
            T03_REGION_KEYS={region_data.name},
            WHITE_GLOVES_DELIVERY_REGIONS={region_data.name},
        ):
            response = api_client.get(self.url)
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data['regionCode'] == region_data.country_code
        assert response_data['regionName'] == region_data.name
        assert response_data['currencyCode'] == region_data.currency_code
        assert response_data['countryLocale'] == region_data.country_locale
        assert response_data['assemblyAvailable'] is True
        assert response_data['corduroyAvailable'] is False
        assert response_data['doorstepSottyDelivery'] is True
        assert response_data['s01Available'] is True
        assert response_data['oldSofaCollectionAvailable'] is True
        assert response_data['t03Available'] is True
        assert response_data['whiteGlovesDeliveryAvailable'] is True
        assert response_data['newsletterVoucherValue'] == (
            region_data.get_format_price(
                NEWSLETTER_VOUCHER_VALUES.get(region_data.currency_code).value
            )
        )
        rco = RegionCalculationsObject(region_data)
        assert response_data['storageSamplePrice'] == rco.calculate_regionalized(
            sample_prices.storage_sample_price
        )
        assert response_data['storageSamplePromoPrice'] == rco.calculate_regionalized(
            sample_prices.storage_sample_sale_price
        )
        assert response_data['isStorageSamplePromoActive'] is True
        assert response_data['sofaSamplePrice'] == rco.calculate_regionalized(
            sample_prices.sofa_sample_price
        )
        assert response_data['sofaSamplePromoPrice'] == rco.calculate_regionalized(
            sample_prices.sofa_sample_sale_price
        )
        assert response_data['isSofaSamplePromoActive'] is True

        assert response_data['availableLanguages'] == ['de', 'en']
        assert response_data['featureFlags'] == [ab_test.codename]

    def test_authenticated_user_db_performance(
        self,
        user_factory,
        region_de,
        api_client,
        django_assert_max_num_queries,
    ):
        user = user_factory(profile__region=region_de)
        api_client.force_authenticate(user)

        with django_assert_max_num_queries(22):
            api_client.get(self.url)

        with django_assert_max_num_queries(7):
            api_client.get(self.url)

    def test_anonymous_user_db_performance(
        self,
        region_de,
        api_client,
        django_assert_max_num_queries,
        mocker,
    ):
        mocker.patch(
            'user_profile.middleware.RegionSelector.get_region',
            return_value=region_de,
        )

        with django_assert_max_num_queries(18):
            api_client.get(self.url)

        with django_assert_max_num_queries(6):
            api_client.get(self.url)
