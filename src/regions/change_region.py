from django.db import transaction
from django.http import HttpRequest
from rest_framework.request import Request

from carts.models import Cart
from carts.services.cart_service import CartService
from orders.models import Order
from payments.models import Transaction
from regions.models import Region
from user_profile.models import UserProfile


@transaction.atomic
def change_region(
    profile: UserProfile,
    region: Region,
    request: Request | HttpRequest | None = None,
) -> None:
    """Change region of the profile and related cart and order. Update fields also at
    transaction, if exists.

    Recalculates cart after region change.
    """
    if request:
        request.session['cached_region'] = region.get_data_as_dict()

    profile = UserProfile.objects.select_for_update().get(id=profile.id)
    profile.change_region(region)
    cart = CartService.get_cart(profile.user)
    if not cart:
        return

    cart = Cart.objects.select_for_update().get(id=cart.id)
    cart_service = CartService(cart)
    cart_service.change_region(region)

    order = cart.order
    if not order:
        return

    order = Order.objects.select_for_update().get(id=order.id)
    cart_service.sync_with_order(request)
    Transaction.sync_with_order(order)
