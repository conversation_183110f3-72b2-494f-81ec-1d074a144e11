from datetime import timedelta

from django.utils import timezone

from celery import shared_task
from celery.utils.log import get_task_logger

from custom.metrics import task_metrics
from customer_service.models import (
    CSOrder,
    CSUnsuccessfulPayments,
    CSUserProfile,
    OrderItemAbortRequest,
)
from orders.models import Order
from orders.services.order_abort import abort_order
from producers.choices import ProductStatus
from user_profile.models import UserProfile

task_logger = get_task_logger(__name__)


@shared_task
@task_metrics
def cs_pending_failure_report():
    CSUnsuccessfulPayments.find_orders()


@shared_task
@task_metrics
def cs_pending_failure_clean_report():
    CSUnsuccessfulPayments.clean_already_payed_orders()


@shared_task
def update_or_create_cs_user_profile(user_profile_id):
    user_profile = UserProfile.objects.get(pk=user_profile_id)
    CSUserProfile.objects.update_or_create_from_user_profile(user_profile)


@shared_task
def update_or_create_cs_order(order_id):
    order = Order.objects.get(pk=order_id)
    CSOrder.objects.update_or_create_from_order(order)


@shared_task
def process_pending_order_abort_requests() -> None:
    fifteen_minutes_ago = timezone.now() - timedelta(minutes=15)

    pending_requests = OrderItemAbortRequest.objects.pending().filter(
        requested_at__lte=fifteen_minutes_ago,
    )
    for pending_request in pending_requests:
        order = pending_request.order
        order_items_with_quantity = pending_request.order_items_with_quantity
        exception_messages = []
        for order_item_with_quantity in order_items_with_quantity:
            order_item = order.items.get(id=order_item_with_quantity['order_item_id'])
            product = order_item.product_set.first()
            if product.status == ProductStatus.ABORTED:
                exception_messages.append(f'Product {product.id} is already aborted')
            else:
                exception_msg = abort_order(
                    pending_request.order,
                    product,
                    pending_request.requested_by,
                )
                if exception_msg:
                    exception_messages.append(exception_msg)
        if exception_messages:
            pending_request.exception_messages = ','.join(exception_messages)
            pending_request.save(update_fields=['exception_messages'])

        pending_request.update_status_to_done()
