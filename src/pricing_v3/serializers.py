from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from rest_framework import serializers

from carts.models import Cart
from custom.enums import ShelfType
from custom.fields import Currency<PERSON><PERSON>berField
from gallery.types import FurnitureType
from orders.models import Order
from orders.services.vat_details import (
    STANDARD_VAT,
    VatDetailsGetter,
)
from regions.services.limitations import LimitationService
from regions.services.regionalized_price import RegionCalculationsObject
from regions.types import RegionLikeObject
from vouchers.constants import (
    ASSEMBLY_PROMO_AMOUNT_STARTS,
    ASSEMBLY_PROMO_DISCOUNT_VALUE,
    ASSEMBLY_VOUCHER_CODE,
    is_assembly_promo_active,
)
from vouchers.enums import ServiceType


class OmnibusPriceSerializer(serializers.Serializer):
    shelf_type = serializers.ChoiceField(choices=ShelfType.choices())
    geometry = serializers.JSONField()


class PricingSerializer(serializers.Serializer):
    total_price = CurrencyNumberField(
        source='get_total_price_number',
        initial=0,
        default=0,
    )
    total_price_netto = CurrencyNumberField(
        source='get_total_value_net',
        initial=0,
        default=0,
    )
    total_price_before_discount = CurrencyNumberField(
        source='get_total_price_number_before_discount',
        initial=0,
        default=0,
    )
    assembly_price = CurrencyNumberField(
        source='get_assembly_price',
        initial=0,
        default=0,
    )
    assembly_price_in_euro = CurrencyNumberField(
        source='get_assembly_price_in_euro',
        initial=0,
        default=0,
    )
    delivery_price = CurrencyNumberField(
        source='get_delivery_price',
        initial=0,
        default=0,
    )
    delivery_price_in_euro = CurrencyNumberField(
        source='get_delivery_price_in_euro',
        initial=0,
        default=0,
    )
    discount_value = CurrencyNumberField(
        source='region_promo_amount',
        initial=0,
        default=0,
    )
    recycle_tax_value = CurrencyNumberField(
        source='get_recycle_tax_value',
        initial=0,
        default=0,
    )
    order_revenue_brutto = CurrencyNumberField(
        source='get_base_total_value',
        initial=0,
        default=0,
    )
    tax = CurrencyNumberField(
        source='get_base_vat_value',
        initial=0,
        default=0,
    )
    order_total_price_netto = CurrencyNumberField(
        source='get_base_total_value_net',
        initial=0,
        default=0,
    )
    order_promo_amount_netto = CurrencyNumberField(
        source='get_base_promo_amount_net',
        initial=0,
        default=0,
    )
    vat_percentage_value = serializers.SerializerMethodField()

    @staticmethod
    def get_vat_percentage_value(instance: Cart | Order) -> Decimal:
        if not instance:
            return STANDARD_VAT * Decimal('100')
        vat_details = VatDetailsGetter(instance)
        as_percent = vat_details.get_vat_rate_for_display() * Decimal('100')
        if as_percent == int(as_percent):
            return Decimal(as_percent).quantize(Decimal('1'))
        return Decimal(as_percent).quantize(Decimal('.1'))


class CartPricingSerializer(PricingSerializer):
    region_aggregated_furniture_price = serializers.SerializerMethodField()

    delivery_price = serializers.SerializerMethodField()
    delivery_promo_price = serializers.SerializerMethodField()
    delivery_promo = serializers.SerializerMethodField()

    # extra delivery services + simple delivery
    delivery_services_price = serializers.SerializerMethodField()
    delivery_services_promo_price = serializers.SerializerMethodField()
    delivery_services_promo = serializers.SerializerMethodField()
    delivery_services_price_without_delivery = serializers.SerializerMethodField()

    old_sofa_collection_price = serializers.SerializerMethodField()

    # tape promo
    assembly_promo = serializers.SerializerMethodField()
    assembly_promo_value = serializers.SerializerMethodField()

    @property
    def rco(self) -> RegionCalculationsObject:
        return self.context['region_calculations_object']

    @property
    def vat_details(self) -> VatDetailsGetter:
        return VatDetailsGetter(self.parent.instance)

    @property
    def region(self) -> RegionLikeObject:
        return self.context['region']

    def get_old_sofa_collection_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        return self.rco.calculate_regionalized(obj.get_old_sofa_collection_price())

    def get_white_gloves_delivery_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        if not LimitationService(region=obj.region).is_white_gloves_delivery_available:
            return Decimal(0)
        return self.rco.calculate_regionalized(obj.get_white_gloves_delivery_price())

    def _get_delivery_price_without_promo(self, obj: Cart) -> Decimal:
        # in local currency
        delivery_price = obj.get_delivery_price()

        if obj.is_vat_exempt and not obj.is_from_non_eu_country:
            delivery_price = self._get_net_price(
                delivery_price, self.vat_details.vat_rate
            )
        return delivery_price

    def _get_delivery_price_with_promo(self, obj: Cart) -> int:
        delivery_price = obj.get_delivery_price()
        delivery_region_promo_value = obj.get_delivery_region_price_promo_value()
        return delivery_price - delivery_region_promo_value

    def _get_assembly_price(self, obj: Cart) -> int:
        # in region currency, full price
        if not LimitationService(region=obj.region).is_assembly_available:
            return Decimal(0)
        if not is_assembly_promo_active():
            return obj.get_assembly_price()

        if (
            # if vouchers are applied, assembly price is calculated correctly
            not obj.assembly_promo_applies()
            or obj.get_items_price()
            < self.rco.calculate_regionalized(ASSEMBLY_PROMO_AMOUNT_STARTS)
        ):
            return obj.get_assembly_price()

        assembly_price = Decimal(0)
        for cart_item in obj.sellable_items:
            sellable_item: FurnitureType = cart_item.sellable_item
            as_price = sellable_item.get_assembly_price(
                region=self.region, discount_value=ASSEMBLY_PROMO_DISCOUNT_VALUE
            )
            assembly_price += (
                self.rco.calculate_regionalized(as_price) * cart_item.quantity
            )

        return assembly_price

    def _has_delivery_discount(self, obj: Cart) -> bool:
        if not obj.vouchers.exists() or not obj.has_s01:
            return False
        return obj.vouchers.filter(
            discounts__service_type=ServiceType.DELIVERY
        ).exists()

    def get_region_aggregated_furniture_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        return obj.get_region_aggregated_furniture_price()

    def get_delivery_price(self, obj: Cart) -> Decimal:
        # full delivery price for all items in regional currency
        if not obj:
            return Decimal(0)
        return self._get_delivery_price_without_promo(obj)

    def get_delivery_promo(self, obj: Cart) -> bool:
        if not obj:
            return False
        return self._has_delivery_discount(obj)

    def get_delivery_promo_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        return Decimal(self._get_delivery_price_with_promo(obj))

    def get_delivery_services_price(self, obj: Cart) -> Decimal:
        # price for all delivery services in regional currency
        if not obj:
            return Decimal(0)
        assembly_price = self._get_assembly_price(obj)
        white_gloves_delivery_price = self.get_white_gloves_delivery_price(obj)
        delivery_price = self._get_delivery_price_without_promo(obj)
        return Decimal(assembly_price + white_gloves_delivery_price + delivery_price)

    def get_delivery_services_promo(self, obj: Cart) -> bool:
        if not obj:
            return False
        return self._has_delivery_discount(obj)

    def get_assembly_promo(self, obj: Cart) -> bool:
        if not is_assembly_promo_active():
            return False

        if not obj:
            return False

        if not obj.assembly_promo_applies():
            return False

        region_amount_starts = self.rco.calculate_regionalized(
            ASSEMBLY_PROMO_AMOUNT_STARTS
        )
        return obj.get_items_price() > region_amount_starts

    def get_assembly_promo_value(self, obj: Cart) -> Decimal:
        """Return the discount value no matter if the promo applies or not to minimise
        queries."""
        return 1 - ASSEMBLY_PROMO_DISCOUNT_VALUE

    def get_delivery_services_promo_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        assembly_price = self._get_assembly_price(obj)
        white_gloves_delivery_price = self.get_white_gloves_delivery_price(obj)
        delivery_price = self._get_delivery_price_with_promo(obj)
        return Decimal(assembly_price + white_gloves_delivery_price + delivery_price)

    def get_delivery_services_price_without_delivery(self, obj: Cart) -> Decimal:
        # price for delivery services in regional currency
        if not obj:
            return Decimal(0)
        assembly_price = self._get_assembly_price(obj)
        white_gloves_delivery_price = self.get_white_gloves_delivery_price(obj)
        return Decimal(assembly_price + white_gloves_delivery_price)

    def _get_net_price(self, gross_price: Decimal, vat_rate: Decimal) -> Decimal:
        # Copied and pasted from ItemPriceCalculatorBase
        net_price = gross_price / (1 + vat_rate)
        return Decimal(net_price).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
