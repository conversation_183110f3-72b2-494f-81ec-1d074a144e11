from abc import abstractmethod
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    Generic,
    Iterable,
    Optional,
    Type,
    TypeVar,
    Union,
)

from django.db.models import QuerySet
from django.utils import timezone
from django.utils.translation import gettext as _

from custom.metrics import metrics_client
from orders.choices import VatType
from orders.enums import OrderStatus
from orders.services.vat_details import VatDetailsGetter
from pricing_v3.services.item_price_calculators import (
    CartItemPriceCalculator,
    ItemPriceCalculatorBase,
    OrderItemPriceCalculator,
)
from regions.services.regionalized_price import RegionCalculationsObject
from vouchers.constants import (
    ASSEMBLY_PROMO_AMOUNT_STARTS,
    ASSEMBLY_PROMO_DISCOUNT_VALUE,
    ASSEMBLY_VOUCHER_CODE,
    is_assembly_promo_active,
)
from vouchers.services.side_promo_service import SidePromotionService
from vouchers.services.voucher_calculator import (
    VoucherCalculator,
    VoucherItemCalculator,
)
from vouchers.services.voucher_validator import VouchersValidator

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from orders.models import (
        Order,
        OrderItem,
    )
    from vouchers.models import Voucher


TInstance = TypeVar('TInstance', bound=Union['Cart', 'Order'])
TItem = TypeVar('TItem', bound=Union['CartItem', 'OrderItem'])


class PriceCalculatorBase(Generic[TInstance, TItem]):
    """Base service responsible for calculating all price related fields.

    Works with Order and Cart models.
    """

    WITH_PRICE_UPDATED_AT_CHANGE: bool = True

    def __init__(self, instance: TInstance) -> None:
        self.instance = instance
        self.total = Decimal(0)
        self.base_total = Decimal(0)
        self.message_status: Optional[str] = None
        self.message_text: Optional[str] = None

        self.recalculate_items: bool = True

        self.vat_details = self.get_vat_details()
        self.region = instance.get_region(dont_use_cache=True)
        self.rco = RegionCalculationsObject(region=self.region)

    @abstractmethod
    def create_item_calculator(
        self, instance: TItem, rco: 'RegionCalculationsObject'
    ) -> 'ItemPriceCalculatorBase':
        """Creates a price calculator for the given item."""

    @property
    @abstractmethod
    def item_class(self) -> Type[TItem]:
        """Returns the class of child item."""

    def get_vat_details(self):
        return VatDetailsGetter(self.instance)

    def calculate(
        self,
        change_promo_quantity: bool = False,
        check_vat: bool = False,
        ignore_vouchers_check: bool = False,
        recalculate_items: bool = True,
        **kwargs,
    ) -> None:
        self.recalculate_items = recalculate_items
        if check_vat:
            vat_type = self.vat_details.vat_type
            self.instance.vat_type = vat_type.legacy_type
            self.instance.region_vat = vat_type == VatType.LOCAL_VAT
            self.instance.vat_rate = self.vat_details.get_vat_rate_for_display()

        self._handle_items(**kwargs)
        self._set_primary_pricing_fields()
        self._handle_promo(
            ignore_vouchers_check=ignore_vouchers_check,
            change_promo_quantity=change_promo_quantity,
        )
        self._set_secondary_pricing_fields()
        self._handle_promo_for_items()
        self._handle_side_promo()
        self.instance.save()

    def _handle_side_promo(self):
        side_promo_service = SidePromotionService(
            instance=self.instance, call_back_function=self.calculate
        )
        side_promo_service.handle_side_promotion()

    def _handle_promo(
        self, ignore_vouchers_check: bool, change_promo_quantity: bool
    ) -> None:
        if ignore_vouchers_check:
            return

        vouchers = VouchersValidator(
            instance=self.instance, vouchers=self.instance.vouchers.all()
        )()
        if not vouchers:
            return self._handle_missing_voucher_candidate()

        self._update_promo_amounts(vouchers)
        self.instance.vouchers.set(vouchers)
        for voucher in vouchers:
            if self._should_update_voucher_usage(voucher, change_promo_quantity):
                self._update_voucher_usage(voucher)

        self.message_status = 'ok'
        self.message_text = _('Promo accepted.')

    def _handle_assembly_promo(self, region_promo_amount: Decimal) -> None:
        """
        Work-around for the tape promo
        from https://cstm-tasks.atlassian.net/browse/ECO-8000 task
        """
        # Check promo condition for assembly
        region_amount_starts = self.rco.calculate_regionalized(
            ASSEMBLY_PROMO_AMOUNT_STARTS
        )
        region_items_price = self.instance.region_total_price - region_promo_amount
        if self.instance.assembly:
            region_items_price -= self.instance.get_assembly_price()
        if region_items_price <= region_amount_starts:
            return

        # repeat the calculations with promotion coefficient for the assembly service
        self._handle_items(assembly_promo_discount_value=ASSEMBLY_PROMO_DISCOUNT_VALUE)
        self._set_primary_pricing_fields()

    def _handle_missing_voucher_candidate(self) -> None:
        self._clear_promo()
        self.message_status = 'error'
        self.message_text = _('invalid_voucher_fallback')

    @staticmethod
    def _update_voucher_usage(voucher: 'Voucher') -> None:
        voucher.quantity_left -= 1
        voucher.save(update_fields=['quantity_left'])

        metrics_client().increment(
            'backend.voucher.use', 1, tags=[f'origin:{voucher.origin}']
        )

    def _update_promo_amounts(self, vouchers: Iterable['Voucher']) -> None:
        vat_rate = (
            Decimal('0') if self.instance.is_vat_exempt else self.vat_details.vat_rate
        )

        voucher_calculator = VoucherCalculator(
            instance=self.instance, vouchers=vouchers, region=self.region
        )
        region_promo_amount, billable_promo_amount = voucher_calculator()

        billable_promo_amount = self._transfer_billable_promo_amount(
            billable_promo_amount,
        )

        self.instance.billable_region_promo_amount = self._crop_decimal(
            billable_promo_amount
        )

        if (
            is_assembly_promo_active()
            and ASSEMBLY_VOUCHER_CODE
            in self.instance.vouchers.values_list('code', flat=True)
        ):
            self._handle_assembly_promo(region_promo_amount=region_promo_amount)

        self.instance.region_promo_amount = self._crop_decimal(region_promo_amount)
        self.instance.region_promo_amount_net = self._crop_decimal(
            self.instance.region_promo_amount / (1 + vat_rate)
        )
        self.instance.promo_amount = self._get_income_promo_amount(
            self.instance.region_promo_amount
        )
        self.instance.promo_amount_net = self._crop_decimal(
            self.instance.promo_amount / (1 + vat_rate)
        )
        self.instance.region_total_price -= self.instance.region_promo_amount
        self.instance.total_price -= self.instance.promo_amount

    def _transfer_billable_promo_amount(self, billable_promo_amount):
        if not hasattr(self.instance, 'is_split') or not self.instance.is_split():
            return billable_promo_amount

        is_suborder = self.instance.is_suborder_for_split()
        is_parent = self.instance.is_parent_for_split()
        has_only_sku = self.instance.contains_only_skus

        if is_suborder and has_only_sku:
            return Decimal('0')

        if is_parent:
            for suborder in self.instance.suborders.all():
                if not suborder.vouchers.exists():
                    continue

                difference = (
                    suborder.region_promo_amount - suborder.billable_region_promo_amount
                )
                billable_promo_amount += difference

        return billable_promo_amount

    def _handle_items(self, **kwargs) -> None:
        total = base_total = Decimal('0')
        for item in self._prefetch_furniture(self.instance.items.all()):
            item_total, item_base_total = self._handle_item(item=item, **kwargs)
            total += item_total
            base_total += item_base_total

        self.total = total
        self.base_total = base_total

    def _handle_item(self, item: TItem, **kwargs) -> tuple[Decimal, Decimal]:
        total = base_total = Decimal('0')

        if self.recalculate_items:
            item = self._calculate_item(item, **kwargs)

        total += item.aggregate_region_price + self._crop_decimal(
            item.aggregate_region_delivery_price
        )
        base_total += item.aggregate_price + self._crop_decimal(
            item.aggregate_delivery_price
        )
        if self.instance.assembly:
            total += self._crop_decimal(item.aggregate_region_assembly_price)
            base_total += self._crop_decimal(item.aggregate_assembly_price)

        return total, base_total

    def _set_primary_pricing_fields(self) -> None:
        self.instance.region_total_price = self._crop_decimal(self.total)
        self.instance.total_price = self._crop_decimal(self.base_total)

    def _set_secondary_pricing_fields(self) -> None:
        vat_rate = 0 if self.instance.is_vat_exempt else self.vat_details.vat_rate

        self.instance.region_total_price_net = self._crop_decimal(
            self.instance.region_total_price / (1 + vat_rate)
        )
        self.instance.region_vat_amount = (
            self.instance.region_total_price - self.instance.region_total_price_net
        )
        self.instance.total_price_net = self._crop_decimal(
            self.instance.total_price / (1 + vat_rate)
        )
        self.instance.vat_amount = (
            self.instance.total_price - self.instance.total_price_net
        )
        if self.WITH_PRICE_UPDATED_AT_CHANGE:
            self.instance.price_updated_at = timezone.now()

    @staticmethod
    def _crop_decimal(value: Union[Decimal, float, str, int]) -> Decimal:
        return Decimal(value).quantize(Decimal('.01'))

    def _get_income_promo_amount(self, region_price: Decimal) -> Decimal:
        base_promo_amount = self.rco.calculate_base(region_price)
        return (base_promo_amount * self.rco.region_rate).quantize(Decimal('.01'))

    @abstractmethod
    def _calculate_item(self, item: TItem, **kwargs) -> TItem:
        calculator = self.create_item_calculator(instance=item, rco=self.rco)
        return calculator.calculate(vat_rate=self.vat_details.vat_rate, **kwargs)

    @abstractmethod
    def _should_update_voucher_usage(
        self,
        voucher: 'Voucher',
        change_promo_quantity: bool,
    ) -> bool:
        pass

    @abstractmethod
    def _clear_promo(self) -> None:
        pass

    @abstractmethod
    def _prefetch_furniture(
        self,
        queryset: QuerySet[TItem],
    ) -> QuerySet[TItem]:
        pass

    def _handle_promo_for_items(self):
        if not self.instance.vouchers.exists():
            return self.instance.items.update(
                region_promo_value=0,
                region_delivery_promo_value=0,
            )

        if self.instance.billable_region_promo_amount <= 0:
            return self.instance.items.update(
                region_promo_value=0,
                region_delivery_promo_value=0,
            )

        voucher_affected_items = self.instance.material_items.all()
        for item in voucher_affected_items:
            calculator = VoucherItemCalculator(item=item)
            item.region_promo_value = calculator.calculate_region_promo_value()
            item.region_delivery_promo_value = (
                calculator.calculate_delivery_promo_value()
            )
        self._handle_missing_cents_in_promo(voucher_affected_items)
        self.item_class.objects.bulk_update(
            voucher_affected_items,
            [
                'region_promo_value',
                'region_delivery_promo_value',
            ],
        )

    def _handle_missing_cents_in_promo(self, items: list[TItem]) -> None:
        sum_promo_on_items = sum(item.aggregate_region_promo_value for item in items)
        sum_delivery_promo_value = sum(
            item.aggregate_region_delivery_promo_value for item in items
        )
        # diff between the value shown on the invoice and the sum of promo on items
        # Since delivery_promo_value is applied to region_promo_amount
        # on the order level and not on individual items, we need to subtract it here.
        missing_diff = (
            self.instance.billable_region_promo_amount
            - sum_promo_on_items
            - sum_delivery_promo_value
        )
        if missing_diff:
            # Calculate total value of all items for proportional distribution
            total_item_value = sum(item.aggregate_region_price for item in items)

            # Distribute proportionally and track remainder
            remaining_diff = missing_diff
            items = sorted(
                items, key=lambda item: item.aggregate_region_price, reverse=True
            )
            for i, item in enumerate(items):
                if i == len(items) - 1:  # Last item gets any remainder
                    item_diff = remaining_diff
                else:
                    proportion = item.aggregate_region_price / total_item_value
                    item_diff = self._crop_decimal(missing_diff * proportion)
                    remaining_diff -= item_diff

                item.region_promo_value += self._crop_decimal(item_diff / item.quantity)


class CartPriceCalculator(PriceCalculatorBase['Cart', 'CartItem']):
    def create_item_calculator(
        self, instance: 'CartItem', rco: 'RegionCalculationsObject'
    ) -> 'CartItemPriceCalculator':
        return CartItemPriceCalculator(instance=instance, rco=rco)

    @property
    def item_class(self) -> Type['CartItem']:
        from carts.models import CartItem

        return CartItem

    def _should_update_voucher_usage(
        self,
        voucher: 'Voucher',
        change_promo_quantity: bool,
    ) -> bool:
        return voucher.quantity != -1 and change_promo_quantity

    def _clear_promo(self) -> None:
        self.instance.clear_promo()

    def _prefetch_furniture(
        self,
        queryset: QuerySet['CartItem'],
    ) -> QuerySet['CartItem']:
        lookup = 'cart_item'
        if lookup in queryset._prefetch_related_lookups:
            return queryset

        return queryset.prefetch_related(lookup)


class OrderPriceCalculator(PriceCalculatorBase['Order', 'OrderItem']):
    def __init__(self, order: 'Order') -> None:
        super().__init__(instance=order)

        # used when switching statuses, as defined in OrderSwitchStatusTransitionsMixin
        self.completed_target_items = order.completed_target_order_items.all()

    def create_item_calculator(
        self, instance: 'OrderItem', rco: 'RegionCalculationsObject'
    ) -> 'OrderItemPriceCalculator':
        return OrderItemPriceCalculator(instance=instance, rco=rco)

    @property
    def item_class(self) -> Type['OrderItem']:
        from orders.models import OrderItem

        return OrderItem

    def _calculate_item(self, item: 'OrderItem', **kwargs) -> 'OrderItem':
        if item not in self.completed_target_items:
            return super()._calculate_item(item, **kwargs)

        return item

    def _should_update_voucher_usage(
        self,
        voucher: 'Voucher',
        change_promo_quantity: bool,
    ) -> bool:
        return (
            voucher.quantity != -1
            and change_promo_quantity
            and not self.instance.is_suborder_for_split()
        )

    def _clear_promo(self) -> None:
        editable_statuses = [
            OrderStatus.CANCELLED,
            OrderStatus.DRAFT,
            OrderStatus.PAYMENT_FAILED,
            OrderStatus.CART,
        ]
        if self.instance.status in editable_statuses:
            self.instance.clear_promo()

    def _prefetch_furniture(
        self,
        queryset: QuerySet['OrderItem'],
    ) -> QuerySet['OrderItem']:
        lookup = 'order_item'
        if lookup in queryset._prefetch_related_lookups:
            return queryset

        return queryset.prefetch_related(lookup)


class OrderPriceCalculatorForPriceUpdatedAtPricing(OrderPriceCalculator):
    """
    It overrides _handle_promo so it doesn't call _get_voucher_candidate which
    triggers calculate_total_price_for_items from _get_voucher, so recalculates
    items even if recalculates_items=False
    """

    WITH_PRICE_UPDATED_AT_CHANGE: bool = False

    def __init__(self, order: 'Order', get_vat_type_from_order=True) -> None:
        self.get_vat_type_from_order = get_vat_type_from_order
        super().__init__(order)

    def get_vat_details(self):
        return VatDetailsGetter(
            self.instance,
            get_vat_type_from_order=self.get_vat_type_from_order,
        )

    def _handle_promo(
        self,
        ignore_vouchers_check: bool,
        change_promo_quantity: bool,
    ) -> None:
        if not self.instance.promo_text:
            return

        vouchers = self.instance.vouchers.all()
        self._update_promo_amounts(vouchers=vouchers)
        for voucher in vouchers:
            if self._should_update_voucher_usage(voucher, change_promo_quantity):
                self._update_voucher_usage(voucher)

        self.message_status = 'ok'
        self.message_text = _('Promo Code accepted.')
        return None

    def _calculate_item(self, item: 'OrderItem', **kwargs) -> 'OrderItem':
        if item not in self.completed_target_items:
            calculator = self.create_item_calculator(instance=item, rco=self.rco)
            item = calculator.calculate(
                vat_rate=self.vat_details.vat_rate,
                for_datetime=self.instance.price_updated_at,
                **kwargs,
            )
        return item
