from datetime import timedelta
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from unittest.mock import patch

from django.test import override_settings
from django.urls import reverse
from django.utils import timezone
from rest_framework import status

import pytest

from freezegun import freeze_time
from pytest_cases import (
    case,
    parametrize_with_cases,
)

from pricing_v3.omnibus import OmnibusCalculator
from regions.change_region import change_region


@pytest.fixture
def euro_currency(currency_factory):
    return currency_factory(code='EUR', name='Euro', symbol='€', rates=[])


@pytest.fixture
def zloty_currency(currency_factory, currency_rate_factory):
    currency = currency_factory(code='PLN', name='<PERSON><PERSON><PERSON><PERSON><PERSON>', symbol='ZŁ', rates=[])
    with freeze_time(timezone.now() - timedelta(days=10000)):
        currency_rate_factory(
            currency=currency,
            rate=0.01,
        )
    with freeze_time(timezone.now() - timedelta(days=100)):
        currency_rate_factory(
            currency=currency,
            rate=1,
        )
    return currency


@pytest.fixture
def poland_region(region_factory, zloty_currency, region_rate_factory):
    poland = region_factory(
        name='Poland',
        currency=zloty_currency,
    )
    region_rate_factory(region=poland, rate=1.0)
    return poland


@pytest.fixture
def germany_region(
    region_factory,
    euro_currency,
    region_rate_factory,
    currency_rate_factory,
):
    germany = region_factory(
        name='Deutschland',
        currency=euro_currency,
    )
    region_rate_factory(region=germany, rate=1.0)
    currency_rate_factory(currency=germany.currency, rate=1.0)
    return germany


@pytest.fixture
def global_strikethrough_promo_30_active_from_yesterday(
    voucher_factory,
    promotion_factory,
    promotion_config_factory,
    germany_region,
    poland_region,
):
    promotion = promotion_factory(
        strikethrough_pricing=True,
        active=True,
        promo_code=voucher_factory(value=30, is_percentage=True, amount_starts=0),
        start_date=timezone.now() - timedelta(days=1),
    )
    promotion_config_factory(promotion=promotion)


@pytest.fixture
def create_promo(
    promotion_factory,
    voucher_factory,
    promotion_config_factory,
    germany_region,
):
    def _create(value, started_x_days_ago, ended_x_days_ago, region=germany_region):
        promotion = promotion_factory(
            strikethrough_pricing=True,
            active=False,
            promo_code=voucher_factory(
                value=value,
                is_percentage=True,
                amount_starts=0,
            ),
            start_date=timezone.now() - timedelta(days=started_x_days_ago),
            end_date=timezone.now() - timedelta(days=ended_x_days_ago),
        )
        conf = promotion_config_factory(promotion=promotion)
        conf.enabled_regions.add(region)
        return promotion

    return _create


class TestOmnibusCalculatorCases:
    @case(tags=['no_previous_promotion'])
    def case_jetty_no_previous_promotion(self, jetty, germany_region):
        return jetty, jetty.get_shelf_price_as_number(region=germany_region)

    @case(tags=['no_previous_promotion'])
    def case_sku_no_previous_promotion(self, sku_variant_factory):
        price = Decimal('100.00')
        sku_variant = sku_variant_factory(price=price)
        return sku_variant, price

    @case(tags=['previous_promotions'])
    def case_jetty_previous_promotions(
        self,
        germany_region,
        jetty,
    ):
        base_price = jetty.get_shelf_price_as_number(region=germany_region)
        return jetty, base_price

    @override_settings(USE_TZ=False)
    @case(tags=['previous_promotions'])
    def case_sku_previous_promotions(self, sku_variant_factory):
        base_price = Decimal('100.00')
        sku_variant = sku_variant_factory(price=base_price)
        return sku_variant, base_price


@pytest.mark.django_db
class TestOmnibusCalculator:
    @parametrize_with_cases(
        'item, expected_price',
        cases=TestOmnibusCalculatorCases,
        has_tag='no_previous_promotion',
    )
    def test_no_previous_promotion(
        self,
        item,
        expected_price,
        germany_region,
        global_strikethrough_promo_30_active_from_yesterday,
    ):
        price_calculator = OmnibusCalculator.get_instance(
            germany_region.cached_region_data,
        )
        omnibus_price = price_calculator.calculate_lowest_price(item=item)
        assert omnibus_price == expected_price

    @parametrize_with_cases(
        'item, base_price',
        cases=TestOmnibusCalculatorCases,
        has_tag='previous_promotions',
    )
    def test_one_previous_promotion(
        self,
        item,
        base_price,
        germany_region,
        create_promo,
        global_strikethrough_promo_30_active_from_yesterday,
    ):
        promo = create_promo(value=50, started_x_days_ago=10, ended_x_days_ago=7)
        price_calculator = OmnibusCalculator.get_instance(
            germany_region.cached_region_data,
        )
        omnibus_price = price_calculator.calculate_lowest_price(item=item)
        expected_price = Decimal(
            base_price - Decimal(promo.promo_code.value / 100) * base_price
        ).quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        assert expected_price == omnibus_price

    @parametrize_with_cases(
        'item, expected_price',
        cases=TestOmnibusCalculatorCases,
        has_tag='no_previous_promotion',
    )
    def test_one_previous_promotion_but_to_old(
        self,
        item,
        expected_price,
        germany_region,
        global_strikethrough_promo_30_active_from_yesterday,
        create_promo,
    ):
        create_promo(value=50, started_x_days_ago=100, ended_x_days_ago=32)
        price_calculator = OmnibusCalculator.get_instance(
            germany_region.cached_region_data,
        )
        omnibus_price = price_calculator.calculate_lowest_price(item=item)
        assert expected_price == omnibus_price

    @parametrize_with_cases(
        'item, base_price',
        cases=TestOmnibusCalculatorCases,
        has_tag='previous_promotions',
    )
    def test_two_previous_promotions(
        self,
        item,
        base_price,
        germany_region,
        global_strikethrough_promo_30_active_from_yesterday,
        create_promo,
    ):
        create_promo(value=10, started_x_days_ago=10, ended_x_days_ago=7)
        promo = create_promo(value=50, started_x_days_ago=20, ended_x_days_ago=15)
        expected_price = Decimal(
            base_price - Decimal(promo.promo_code.value / 100) * base_price
        ).quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        price_calculator = OmnibusCalculator.get_instance(
            germany_region.cached_region_data,
        )

        omnibus_price = price_calculator.calculate_lowest_price(item=item)

        assert expected_price == omnibus_price

    @parametrize_with_cases(
        'item, base_price',
        cases=TestOmnibusCalculatorCases,
        has_tag='previous_promotions',
    )
    def test_two_previous_promotion_but_one_too_late(
        self,
        item,
        base_price,
        germany_region,
        global_strikethrough_promo_30_active_from_yesterday,
        create_promo,
    ):
        voucher = create_promo(value=10, started_x_days_ago=10, ended_x_days_ago=7)
        create_promo(value=50, started_x_days_ago=50, ended_x_days_ago=32)

        price_calculator = OmnibusCalculator.get_instance(
            germany_region.cached_region_data,
        )
        omnibus_price = price_calculator.calculate_lowest_price(item=item)
        expected_price = Decimal(
            base_price - Decimal(voucher.promo_code.value / 100) * base_price
        ).quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        assert expected_price == omnibus_price

    def test_region_with_different_currency_rates(
        self,
        poland_region,
        jetty,
        global_strikethrough_promo_30_active_from_yesterday,
    ):
        """
        In order to check if we consider the right currency_rate here we check the
        largest price in given time range. We expect it to be the same as
        get_shelf_price_as_number() with currency rate and region rate being equal to 1.
        """
        price_calculator = OmnibusCalculator.get_instance(
            poland_region.cached_region_data,
        )
        prices = price_calculator._get_prices_in_daterange(item=jetty)
        max_price = sorted(prices, key=lambda p: p['price'], reverse=True)[0]['price']
        jetty_base_price = jetty.get_shelf_price_as_number(region=poland_region)
        assert max_price == jetty_base_price

    @override_settings(OMNIBUS_SIMPLIFIED=True)
    def test_simplified_omnibus_price_same_as_sale_price_on_pdp(
        self,
        api_client,
        jetty,
        global_strikethrough_promo_30_active_from_yesterday,
        germany_region,
    ):
        price_calculator = OmnibusCalculator.get_instance(
            germany_region.cached_region_data,
        )
        omnibus_price = price_calculator.calculate_lowest_price(item=jetty)
        change_region(jetty.owner.profile, germany_region)

        url = reverse('jetty-pdp', args=(jetty.id,))
        api_client.force_login(jetty.owner)
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['price'] == omnibus_price

    def test_sku_with_different_historical_prices(
        self,
        global_strikethrough_promo_30_active_from_yesterday,
        create_promo,
        sku_variant_factory,
        germany_region,
    ):
        now = timezone.now()
        one_week_ago = now - timedelta(days=7)
        one_month_ago = now - timedelta(days=30)
        current_price = Decimal('100.00')
        old_price = Decimal('49.00')

        # A month to a week ago, the price was 49
        # Since last week, it has been 100
        sku_variant = sku_variant_factory(price=old_price)
        history = sku_variant.sku.trackable_fields.history.first()
        history.history_date = one_month_ago
        history.save()
        sku_variant.sku.trackable_fields.price = current_price
        sku_variant.sku.trackable_fields.save()
        history = sku_variant.sku.trackable_fields.history.last()
        history.history_date = one_week_ago
        history.save()

        # during promo price was 100, so promo price was 50
        create_promo(value=50, started_x_days_ago=4, ended_x_days_ago=2)

        price_calculator = OmnibusCalculator.get_instance(
            germany_region.cached_region_data,
        )
        # if `created_at` is not patched, then the sku_variant returns current price
        with patch.object(sku_variant, 'created_at', one_month_ago):
            omnibus_price = price_calculator.calculate_lowest_price(item=sku_variant)
        assert omnibus_price == old_price
