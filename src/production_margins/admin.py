import datetime
import logging
import re
import textwrap

from io import BytesIO
from urllib.parse import quote_plus
from zipfile import ZipFile

from django.conf import settings
from django.contrib import (
    admin,
    messages,
)
from django.core.paginator import Paginator
from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON>,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
)
from django.db.models.expressions import (
    Case,
    When,
)
from django.http import JsonResponse
from django.http.response import (
    FileResponse,
    HttpResponseRedirect,
)
from django.shortcuts import render
from django.urls import reverse
from django.utils.html import (
    format_html,
    format_html_join,
)
from rest_framework import status

from rangefilter.filters import DateRangeFilter

from admin_customization.admin import ButtonActionsBaseAdmin
from custom.admin_action import admin_action_with_form
from kpi.tasks import update_cash_flow_data
from producers.choices import BatchType
from producers.enums import CustomPricingFactorChangeRequestStatusEnum
from producers.production_system_utils.client import ProductionSystemClient
from producers.tasks import (
    generate_elements_order_to_invoice_meblepl_report,
    generate_financial_report_task,
    generate_order_summary_xls,
    generate_order_summary_xls_manufactor_fault,
    generate_report_and_save_element_order_karton_s93_task,
)
from production_margins import admin_filters
from production_margins.admin_filters import (
    PricingFactorItemCodenameTypeFilter,
    PricingFactorItemCodenameVersionFilter,
)
from production_margins.admin_inlines import (
    CustomPricingFactorChangeRequestCommentInline,
    CustomPricingFactorInline,
    MaterialInfoAttachmentInline,
)
from production_margins.admin_mixins import (
    BulkUpdateDatesMixin,
    CSVExportActionMixin,
    CSVImportActionMixin,
)
from production_margins.choices import (
    MaterialInfoAttachmentType,
    MeasurementUnit,
)
from production_margins.data_management.exporters import (
    AveragePricingFactorsExporter,
    CustomPricingFactorsExporter,
    ElementManagedInfoExporter,
    ManufacturerCodeExporter,
    MaterialManagementCostExporter,
    PricingFactorItemCPFColumnsExporter,
    PricingFactorItemExporter,
    PricingFactorItemPFDataExporter,
)
from production_margins.data_management.exporters.manufacturer_code import (
    ManufacturerCodeAttachmentsExporter,
)
from production_margins.data_management.importers import (
    CustomPricingFactorsImporter,
    ElementManagedInfoImporter,
    LegacyPricingFactorItemImporter,
    ManufacturerCodeImporter,
    MaterialManagementCostImporter,
    PricingFactorItemsImporter,
)
from production_margins.enums import MaterialInfoAttachmentState
from production_margins.forms import (
    ElementsOrderForm,
    ManufacturerCodeForm,
    MaterialInfoAttachmentForm,
    NoteForm,
    PricingFactorItemForm,
    ProductsListForElementsOrderToInvoiceForm,
    SearchByCodenameForm,
)
from production_margins.helpers import CustomPricingFactorXLSXReport
from production_margins.models import (
    CustomPricingFactor,
    CustomPricingFactorChangeRequest,
    CustomPricingFactorChangeRequestComment,
    ElementManagedInfo,
    ElementsOrder,
    ElementsOrderHistory,
    GalaStockLevelMaterial,
    ManufacturerCode,
    MaterialInfoAttachment,
    MaterialManagementCost,
    PricingFactorItem,
    PricingFactorItemImport,
    PricingFactors,
    TelmexMagazineCode,
)
from production_margins.utils import (
    elements_order_file_as_json,
    search_custom_pricing_factor_by_ids,
)

logger = logging.getLogger('cstm')


class PricingFactorsAdmin(admin.ModelAdmin):
    raw_id_fields = ('created_by',)
    list_display = (
        'id',
        'norm',
        'name',
        'description',
        'status',
        'active',
        'active_from_to',
        'compare',
        'updated_at',
        'created',
    )
    actions = ('set_active',)
    list_filter = ('status',)

    # --- EXTRA -----------------------------------------------------------------------
    @property
    def base_class(self):
        return PricingFactors

    # --- COLUMNS ---------------------------------------------------------------------
    def compare(self, obj):
        df = obj._get_pricing_factors_as_df()
        return format_html(
            '{} codenames<br>{} PLN',
            len(df.index),
            df.price_per_unit.sum() if 'price_per_unit' in df.columns else 0,
        )

    def active_from_to(self, obj):
        activated = (
            obj.activated_at.strftime('%Y-%m-%d %H:%M:%S') if obj.activated_at else '-'
        )
        deactivated = (
            obj.deactivated_at.strftime('%Y-%m-%d %H:%M:%S')
            if obj.deactivated_at
            else '-'
        )
        return format_html('{}<br>{}', activated, deactivated)

    def created(self, obj):
        return format_html(
            '{}<br>by {}', obj.created_at.strftime('%Y-%m-%d %H:%M:%S'), obj.created_by
        )

    def set_active(self, request, queryset):
        if len(queryset) != 1:
            messages.error(request, 'Zaznacz dokładnie jeden obiekt!')
            return

        if (
            len(PricingFactors.objects.filter(active=True)) == 1
            and queryset[0].active is True
        ):
            messages.error(request, 'Zaznaczony obiekt jest już aktywny')
            return

        if queryset[0].activated_at is not None:
            messages.error(
                request, 'Nie można aktyuwować wcześniej aktywowanego obiektu!'
            )
            return

        queryset.first().set_as_active()
        messages.info(request, 'Zaznaczony obiekt został oznaczony jako aktywny.')

    set_active.short_description = '-- SET AS ACTIVE --'


class ManufacturerCodeAdmin(
    CSVExportActionMixin,
    CSVImportActionMixin,
    ButtonActionsBaseAdmin,
):
    model = ManufacturerCode
    form = ManufacturerCodeForm

    list_display = (
        'id',
        'code',
        'cpf',
        'codenames',
        'name',
        'has_att_certification',
        'has_att_technical_sketch',
        'has_att_technical_card',
        'has_att_reach',
        'has_att_rohs',
        'has_att_wood_source',
        'has_att_formaldehyde',
        'has_att_golden_sample',
        'preview_att_picture',
        'carbon_footprint',
    )

    inlines = [MaterialInfoAttachmentInline]
    search_fields = ('pricing_factor_item__codename', 'code')
    actions = (
        'bulk_add_attachments',
        'export_manufacturer_code_to_csv',
        'export_attachment_info_to_csv',
        'export_attachment_files',
    )
    button_actions = ('import_manufacturer_code_from_csv',)
    autocomplete_fields = ('pricing_factor_item',)
    list_filter = (
        admin_filters.MissingMaterialInfoAttachmentFilter,
        admin_filters.CustomPricingFactorCategoryFilter,
        admin_filters.ManufacturerCodePrefixFilter,
    )

    @admin.display(description='CPF')
    def cpf(self, obj):
        return format_html(
            '<a href="{}?q={}"><i class="fa fa-search"></i></a>',
            reverse('admin:production_margins_custompricingfactor_changelist'),
            quote_plus(obj.code),
        )

    @admin.display(description='Codename')
    def codenames(self, obj):
        pfi_items = getattr(obj, 'prefetched_pfi', obj.pricing_factor_item.all())
        return format_html_join(
            '',
            '<a href="{}">{}</a><br>',
            (
                (
                    reverse(
                        'admin:production_margins_pricingfactoritem_change',
                        args=[pfi.id],
                    ),
                    pfi.codename,
                )
                for pfi in pfi_items
            ),
        )

    @staticmethod
    def _get_attachment_status_display(obj, attachment_type):
        att_items = getattr(obj, 'prefetched_att', [])

        states = {MaterialInfoAttachmentState.MISSING}  # default attachment state
        for att in att_items:
            if att.document_type != attachment_type.value:
                continue

            if att.optional and att.is_valid_today:
                states.add(MaterialInfoAttachmentState.OPTIONAL)
                continue

            if att.is_soon_to_expire:
                states.add(MaterialInfoAttachmentState.SOON_TO_EXPIRE)
            elif att.is_valid_today:
                states.add(MaterialInfoAttachmentState.VALID)
            else:
                states.add(MaterialInfoAttachmentState.EXPIRED)

        # pick based on predefined state precedence
        color, title = max(states).details()

        return format_html(
            '<i class="circle-with-background {}" title="{}"> </i>',
            color,
            title,
        )

    @admin.display(description='Cert')
    def has_att_certification(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.CERTIFICATION,
        )

    @admin.display(description='Skch')
    def has_att_technical_sketch(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.TECHNICAL_SKETCH,
        )

    @admin.display(description='Card')
    def has_att_technical_card(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.TECHNICAL_CARD,
        )

    @admin.display(description='REACH')
    def has_att_reach(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.REACH,
        )

    @admin.display(description='ROHS')
    def has_att_rohs(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.ROHS,
        )

    @admin.display(description='Wood')
    def has_att_wood_source(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.WOOD_SOURCE,
        )

    @admin.display(description='Fdh')
    def has_att_formaldehyde(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.FORMALDEHYDE,
        )

    @admin.display(description='GS')
    def has_att_golden_sample(self, obj):
        return self._get_attachment_status_display(
            obj,
            MaterialInfoAttachmentType.GOLDEN_SAMPLE,
        )

    @admin.display(description='Picture')
    def preview_att_picture(self, obj):
        att_items = getattr(obj, 'prefetched_att', [])
        return format_html_join(
            '',
            '<img src="{}" style="max-height: 160px; max-width: 160px;"><br>',
            (
                (att.file.url,)
                for att in att_items
                if att.document_type == MaterialInfoAttachmentType.PICTURE.value
                and att.file
            ),
        )

    @admin.action(description='Bulk add attachments for selected items')
    def bulk_add_attachments(self, request, queryset):
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=MaterialInfoAttachmentForm,
            success_function=self.bulk_add_attachments_handler,
            success_function_kwargs={},
        )

    @staticmethod
    def bulk_add_attachments_handler(
        modeladmin,
        request,
        queryset,
        form: MaterialInfoAttachmentForm,
    ) -> None:
        codes = queryset.all()
        form.manufacturer_code = codes.first()
        attachment_prototype = form.save()
        for code in codes:
            attachment_prototype.pk = None
            attachment_prototype.manufacturer_code = code
            attachment_prototype.save()

    @admin.action(description='Export selected items to CSV')
    def export_manufacturer_code_to_csv(self, request, queryset):
        return self.export_to_csv_file(
            request,
            queryset,
            filename='Manufacturer Code',
            exporter_class=ManufacturerCodeExporter,
        )

    @admin.action(description='Export selected attachments info to CSV')
    def export_attachment_info_to_csv(self, request, queryset):
        return self.export_to_csv_file(
            request,
            queryset,
            filename='MatInfo Attachments',
            exporter_class=ManufacturerCodeAttachmentsExporter,
        )

    @admin.action(description='Export selected attachment files')
    def export_attachment_files(self, request, queryset):
        stream = BytesIO()
        with ZipFile(stream, 'w') as zipfile:
            for chunk in Paginator(queryset, 50):
                for entry in chunk.object_list:
                    attachments = getattr(entry, 'prefetched_att', [])
                    for attachment in attachments:
                        category = MaterialInfoAttachmentType.choices[
                            attachment.document_type
                        ][1]
                        if file := attachment.file:
                            name = file.name.split('/')[-1]
                            zipfile.writestr(
                                f'{entry.code}/{category}/{name}', file.read()
                            )
        stream.seek(0)
        return FileResponse(
            stream,
            as_attachment=True,
            filename='material-info-attachments.zip',
        )

    @admin.action(description='Import from CSV')
    def import_manufacturer_code_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            ManufacturerCodeImporter,
        )

    def get_queryset(self, request) -> QuerySet:
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related(
            Prefetch('pricing_factor_item', to_attr='prefetched_pfi'),
            Prefetch(
                'attachments',
                queryset=MaterialInfoAttachment.objects.with_validity(),
                to_attr='prefetched_att',
            ),
        )
        return queryset

    def get_search_results(self, request, queryset, search_term):
        """
        Overwritten to be used in CPF admin. Otherwise just return super call's result.

        When called from CPF return only results that are already assigned to this CPF.
        """
        queryset, use_distinct = super().get_search_results(
            request,
            queryset,
            search_term,
        )

        if (
            'autocomplete' in request.path
            and request.GET.get('model_name', '') == 'custompricingfactor'
        ):
            referer = request.META.get('HTTP_REFERER', '')
            regex_number_between_slashes = re.compile(r'.*\/(\d+)\/.*')
            cpf_id = int(regex_number_between_slashes.match(referer).group(1))
            if cpf_id:
                filtered_queryset = CustomPricingFactor.objects.get(
                    id=cpf_id
                ).pricing_factor_item.manufacturercode_set.all()
                queryset = queryset & filtered_queryset

        return queryset, use_distinct


class CustomPricingFactorAdmin(
    CSVExportActionMixin,
    CSVImportActionMixin,
    BulkUpdateDatesMixin,
    ButtonActionsBaseAdmin,
):
    """Admin for managing custom price factors."""

    model = CustomPricingFactor
    list_display = [
        'id',
        'pricing_factor_item',
        'manufacturer_code',
        'manufactor',
        'price',
        'loss_factor',
        'manufacturer_code_carbon_footprint',
        'unit',
        'date_from',
        'date_to',
        'short_note',
    ]
    fields = [
        'pricing_factor_item',
        'unit',
        'manufactor',
        'price',
        'loss_factor',
        'manufacturer_code',
        'manufacturer_code_note',
        'date_from',
        'date_to',
        'note',
    ]
    readonly_fields = [
        'manufacturer_code_note',
        'unit',
    ]
    search_fields = ('pricing_factor_item__codename', 'manufacturer_code__code')
    autocomplete_fields = ('pricing_factor_item', 'manufacturer_code')
    list_select_related = ('manufacturer_code', 'pricing_factor_item', 'manufactor')
    list_filter = (
        'manufactor',
        admin_filters.CustomPricingFactorCategoryFilter,
        admin_filters.CPFActiveFilter,
    )
    actions = [  # noqa: RUF005
        'custom_pricing_factors_export_to_csv',
        'averagepf_export_to_csv',
        'export_selected_item_to_xlsx_for_production',
        'bulk_edit_notes',
    ] + BulkUpdateDatesMixin.actions

    button_actions = (
        'search_by_codenames',
        'averagepf_push_to_ps',
        'import_custom_pricing_factors_from_csv',
    )

    @admin.display(description='Carbon Footprint [t]')
    def manufacturer_code_carbon_footprint(self, obj):
        return obj.manufacturer_code.carbon_footprint if obj.manufacturer_code else ''

    @admin.display(description='Name (will be reloaded on save)')
    def manufacturer_code_note(self, obj):
        return obj.manufacturer_code.name

    @admin.action(description='Export selected items to xslx for production')
    def export_selected_item_to_xlsx_for_production(self, request, queryset):
        filename = (
            f'custom_pricing_factor_report_{datetime.datetime.now():%Y-%m-%d}.xlsx'
        )

        response = FileResponse(
            CustomPricingFactorXLSXReport(queryset, filename).generate_report(),
            content_type=(
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ),
        )
        response['Content-Disposition'] = f'attachment; filename={filename}'
        return response

    def bulk_edit_notes(self, request, queryset):
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=NoteForm,
            success_function=self.bulk_update_notes_handler,
            success_function_kwargs={},
        )

    @staticmethod
    def bulk_update_notes_handler(
        modeladmin,
        request,
        queryset,
        form: NoteForm,
    ) -> None:
        note = form.cleaned_data['note']
        queryset.update(note=note)

    bulk_edit_notes.short_description = 'Bulk update notes for selected items'

    @admin.action(description='Export selected items to CSV')
    def custom_pricing_factors_export_to_csv(self, request, queryset):
        return self.export_to_csv_file(
            request,
            queryset,
            filename='Custom_Pricing_Factors',
            exporter_class=CustomPricingFactorsExporter,
        )

    @admin.action(description='Push Average PF to PS')
    def averagepf_push_to_ps(self, request):
        """Sends a request with APF data to Price Factors create endpoint in PS."""
        average_pricing_factors_dict = AveragePricingFactorsExporter().to_dict()
        with ProductionSystemClient(suppress_errors=True) as ps_client:
            response = ps_client.price_factors_create(
                pricing_factors_serialized_data=average_pricing_factors_dict,
            )
        if response.status_code == status.HTTP_201_CREATED:
            messages.success(
                request,
                'Price Factors created in Production System',
            )
        else:
            messages.error(
                request,
                'Error while attempting to push PF to Production System',
            )
        return HttpResponseRedirect('../')

    @admin.action(description='Export Average PF as CSV')
    def averagepf_export_to_csv(self, request, queryset):
        """Download current Average Pricing Factors data as CSV."""
        related_pfi_queryset = PricingFactorItem.objects.filter(
            id__in=queryset.values_list('pricing_factor_item', flat=True),
        ).distinct()
        return self.export_to_csv_file(
            request,
            queryset=related_pfi_queryset,
            filename='Average_Pricing_Factors',
            exporter_class=AveragePricingFactorsExporter,
            additional_export_fields=['description', 'conversion'],
        )

    @admin.action(description='Import Custom PF from CSV')
    def import_custom_pricing_factors_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            CustomPricingFactorsImporter,
        )

    @admin.action(description='Search by Codenames')
    def search_by_codenames(self, request):
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=[],
            form_class=SearchByCodenameForm,
            success_function=search_custom_pricing_factor_by_ids,
            success_function_kwargs={},
        )

    @admin.display(description='Note')
    def short_note(self, obj):
        return textwrap.shorten(obj.note, width=50, placeholder='...')

    @admin.display(description='Unit')
    def unit(self, obj):
        return MeasurementUnit(
            obj.pricing_factor_item.measurement_unit
        ).to_polish_admin_display_only()


class CustomPricingFactorChangeRequestAdmin(admin.ModelAdmin):
    """Admin for managing custom price factors change requests."""

    readonly_fields = (
        'request_number',
        'custom_pricing_factor_link',
        'get_attachment_link',
        'get_current_custom_pricing_factor_data',
        'get_custom_pricing_factor_data_after_accepting',
        'get_requested_custom_pricing_factor_data',
        'created_at',
        'updated_at',
    )
    fields = (
        'request_number',
        'custom_pricing_factor_link',
        'get_current_custom_pricing_factor_data',
        'get_custom_pricing_factor_data_after_accepting',
        'get_requested_custom_pricing_factor_data',
        'get_attachment_link',
        'status',
        'created_at',
        'updated_at',
    )
    model = CustomPricingFactorChangeRequest
    list_display = [
        'request_number',
        'custom_pricing_factor_link',
        'status',
        'get_current_custom_pricing_factor_data',
        'get_custom_pricing_factor_data_after_accepting',
        'get_requested_custom_pricing_factor_data',
        'get_attachment_link',
        'get_comments',
        'created_at',
        'updated_at',
    ]
    list_filter = ('status', 'custom_pricing_factor__manufactor')
    actions = [
        'accept_change_request',
    ]
    search_fields = [
        'request_number',
        'custom_pricing_factor__manufacturer_code__code',
        'manufacturer_code',
        'custom_pricing_factor__pricing_factor_item__codename',
    ]
    inlines = [CustomPricingFactorChangeRequestCommentInline]
    date_hierarchy = 'created_at'
    date_hierarchy_drilldown = False
    raw_id_fields = (
        'custom_pricing_factor',
        'attachment',
    )

    def get_queryset(self, request):
        previous_request_subquery = CustomPricingFactorChangeRequest.objects.filter(
            custom_pricing_factor=OuterRef('custom_pricing_factor'),
            status=CustomPricingFactorChangeRequestStatusEnum.NEW,
            created_at__lt=OuterRef('created_at'),
        ).order_by('-created_at')

        queryset = super().get_queryset(request)
        queryset = (
            queryset.select_related(
                'attachment',
                'custom_pricing_factor',
                'custom_pricing_factor__manufacturer_code',
                'custom_pricing_factor__pricing_factor_item',
            )
            .prefetch_related(
                Prefetch(
                    'custompricingfactorchangerequestcomment_set',
                    queryset=CustomPricingFactorChangeRequestComment.objects.all(),
                    to_attr='prefetched_comments',
                )
            )
            .annotate(
                previous_request=Subquery(previous_request_subquery.values('pk')[:1])
            )
        )
        return queryset

    @admin.display(description='Custom pricing factor link')
    def custom_pricing_factor_link(self, obj):
        link_url = reverse(
            'admin:production_margins_custompricingfactor_change',
            args=[obj.custom_pricing_factor.id],
        )
        return format_html(
            f'<a href={link_url}>{obj.custom_pricing_factor.pricing_factor_item}</a>',
        )

    @admin.display(description='Current CPF data')
    def get_current_custom_pricing_factor_data(self, obj):
        manufacturer_code = (
            obj.custom_pricing_factor.manufacturer_code
            if obj.custom_pricing_factor.manufacturer_code
            else ''
        )
        return format_html(
            f'Manufacturer code: {manufacturer_code}<br>'
            f'Price: {obj.custom_pricing_factor.price}<br>'
            f'Date from: {obj.custom_pricing_factor.date_from}<br>'
            f'Date to: {obj.custom_pricing_factor.date_to}<br>'
        )

    @admin.display(description='Requested CPF data')
    def get_requested_custom_pricing_factor_data(self, obj):
        return format_html(
            f'Manufacturer code: {obj.manufacturer_code}<br>'
            f'Price: {obj.price}<br>'
            f'Date from: {obj.date_from}<br>'
        )

    @admin.display(description='CPF data after accepting previous active request')
    def get_custom_pricing_factor_data_after_accepting(self, obj):
        previous_request_id = obj.previous_request
        if not previous_request_id:
            return '-'
        previous_request = CustomPricingFactorChangeRequest.objects.get(
            pk=previous_request_id
        )
        return format_html(
            f'Manufacturer code: {previous_request.manufacturer_code}<br>'
            f'Price: {previous_request.price}<br>'
            f'Date from: {previous_request.date_from}<br>'
        )

    @admin.display(description='Attachment link')
    def get_attachment_link(self, obj):
        if obj.attachment:
            url = reverse(
                'custom_pricing_factors_request_attachment', args=[obj.attachment.id]
            )
            return format_html(
                f'<a href="{url}">CPF_ATTACHMENT_{obj.attachment.id}</a>'
            )
        return 'No Attachment'

    @admin.display(description='Comments')
    def get_comments(self, obj):
        comments = [
            comment.text
            for comment in getattr(
                obj,
                'prefetched_comments',
                obj.custompricingfactorchangerequestcomment_set.all(),
            )
        ]
        return format_html('<br>'.join(comments))

    @admin.action(description='Accept CPF change request')
    def accept_change_request(self, request, queryset):
        for obj in queryset:
            (
                success,
                error_message,
            ) = CustomPricingFactorChangeRequest.objects.accept_change_request(obj)
            if not success:
                messages.error(request, error_message)

        messages.info(request, 'Selected change requests were processed.')


class ElementManagedInfoAdmin(
    CSVExportActionMixin,
    CSVImportActionMixin,
    ButtonActionsBaseAdmin,
):
    """Admin for management info about elements (codenames)."""

    model = ElementManagedInfo
    list_display = [field.name for field in ElementManagedInfo._meta.get_fields()]
    search_fields = ('pricing_factor_item__codename',)
    autocomplete_fields = ('pricing_factor_item',)

    list_filter = (
        'manufactor',
        'managed_by_us',
        admin_filters.ElementManagedInfoTypeFilter,
    )

    actions = ('export_to_csv',)
    button_actions = ('import_from_csv',)

    @admin.action(description='Export selected items as CSV')
    def export_to_csv(self, request, queryset):
        return self.export_to_csv_file(
            request,
            queryset=queryset,
            filename='Element_Mananged_Info',
            exporter_class=ElementManagedInfoExporter,
        )

    @admin.action(description='Import from CSV')
    def import_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            ElementManagedInfoImporter,
        )


class ElementsOrderAdmin(ButtonActionsBaseAdmin, admin.ModelAdmin):
    """Admin for managing elements orders generated automatically by admin action."""

    change_list_template = 'admin/production_margins/elementsorder/change_list.html'

    model = ElementsOrder
    form = ElementsOrderForm
    fields = (
        'order_file',
        'batches',
        'total_cost',
        'service_cost',
    )
    readonly_fields = ('total_cost', 'service_cost')
    raw_id_fields = ('batches',)
    date_hierarchy = 'generated_at'
    list_filter = (
        admin_filters.BatchIdFilter,
        admin_filters.ProductIdFilter,
        'manufactor',
        admin_filters.CalculatedOrderTypeFilter,
        'order_type',
        admin_filters.ProductShelfTypeFilter,
        ('generated_at', DateRangeFilter),
    )
    search_fields = (
        'id',
        'order_file',
    )
    list_display = (
        'id',
        'order_file',
        'invoice_number',
        'generated_at',
        'calc_order_type',
        'order_type',
        'service_cost',
        'total_cost',
    )
    list_editable = ('invoice_number',)
    actions = [
        'send_financial_report',
        'regenerate_elements_order',
        'get_as_json',
    ]
    button_actions = ('create_elements_order_to_invoice',)

    def get_queryset(self, request):
        qs = (
            super()
            .get_queryset(request)
            .annotate(
                calc_order_type=Case(
                    When(
                        Q(manufactor_fault=True)
                        & Q(batches__batch_type__exact=BatchType.COMPLAINTS),
                        then=Value('Complaint Manufactor'),
                    ),
                    When(
                        batches__batch_type__exact=BatchType.COMPLAINTS,
                        then=Value('Complaint'),
                    ),
                    When(invoice_to_order=True, then=Value('Invoice to Order')),
                    default=Value('New Order'),
                    output_field=CharField(),
                ),
            )
            .distinct()
        )
        return qs

    def get_as_json(self, request, queryset):
        if queryset.count() != 1:
            self.message_user(
                request,
                'Only one object can be selected',
                level=messages.ERROR,
            )
            return
        return JsonResponse(elements_order_file_as_json(queryset.first().order_file))

    @staticmethod
    def calc_order_type(elements_order):
        return elements_order.calc_order_type

    def changelist_view(self, request, extra_context=None):
        response = super().changelist_view(request, extra_context)
        if not hasattr(response, 'context_data') or not response.context_data.get('cl'):
            return response

        context = response.context_data
        element_orders = context['cl'].queryset
        element_orders = element_orders.aggregate(
            total_cost__sum=Sum('total_cost'),
            service_cost__sum=Sum('service_cost'),
        )

        context['total_cost'] = element_orders['total_cost__sum']
        context['service_cost'] = element_orders['service_cost__sum']
        return response

    def send_financial_report(self, request, elements_order_queryset):
        email = request.user.username or request.user.email
        generate_financial_report_task.delay(
            [element.pk for element in elements_order_queryset], email
        )
        self.message_user(
            request,
            'Report will be send via email {}'.format(email),
        )

    def delete_queryset(self, request, queryset):
        if settings.EXPORT_CASH_FLOW_TO_BIG_QUERY:
            for obj in queryset:
                update_cash_flow_data.delay(obj.id)
        queryset.update(deleted=True)

    def regenerate_elements_order(self, request, queryset):
        for obj in queryset:
            if settings.EXPORT_CASH_FLOW_TO_BIG_QUERY:
                update_cash_flow_data.delay(obj.id)
            batches_ids = list(obj.batches.values_list('pk', flat=True))
            if obj.is_manufactor_fault:
                generate_order_summary_xls_manufactor_fault.delay(
                    batches_ids, request.user.email, obj.id
                )
            elif obj.is_s93_packaging:
                generate_report_and_save_element_order_karton_s93_task.delay(
                    batches_ids, obj.id
                )
            else:
                generate_order_summary_xls.delay(
                    batches_ids, request.user.email, obj.id
                )
        queryset.update(deleted=True)

    def save_form(self, request, form, change):
        if change and 'batches' in form.changed_data:
            batches_ids = list(
                form.cleaned_data.pop('batches').values_list('id', flat=True)
            )
            generate_order_summary_xls.delay(
                batches_ids,
                request.user.email,
                form.instance.id,
            )
        return form.save(commit=False)

    def save_model(self, request, obj, form, change):
        if change and 'batches' in form.changed_data:
            if settings.EXPORT_CASH_FLOW_TO_BIG_QUERY:
                update_cash_flow_data.delay(obj.id)
            obj.deleted = True
            obj.save(update_fields=['deleted'])
        else:
            super().save_model(request, obj, form, change)

    @admin.action(description='Create elements order to invoice')
    def create_elements_order_to_invoice(self, request):
        form = None
        if 'apply' in request.POST:
            form = ProductsListForElementsOrderToInvoiceForm(request.POST)
            if form.is_valid():
                generate_elements_order_to_invoice_meblepl_report(
                    form.cleaned_data['products_lists'],
                    form.cleaned_data['invoice_number'],
                )
                return HttpResponseRedirect(
                    reverse('admin:production_margins_elementsorder_changelist')
                )
        if not form:
            form = ProductsListForElementsOrderToInvoiceForm(
                initial={'_selected_action': request.POST.getlist('_selected_action')}
            )
        return render(
            request,
            'admin/production_margins/product_list_for_elements_order_to_invoice.html',
            {
                'form': form,
                'opts': self.model._meta,
                'app_label': self.model._meta.app_label,
                'action': 'create_elements_order_to_invoice',
            },
        )


class ElementsOrderHistoryAdmin(ElementsOrderAdmin):
    list_display = (
        'id',
        'order_file',
        'invoice_number',
        'generated_at',
        'order_type',
        'total_cost',
        'admin_new_report',
    )
    actions = ()

    def has_delete_permission(self, request, obj=None):
        return False

    def admin_new_report(self, obj):
        if obj.new_report:
            view_name = 'admin:production_margins_elementsorder_changelist'
            if obj.new_report.new_report:
                view_name = 'admin:production_margins_elementsorderhistory_changelist'
            return format_html(
                '<a href="{0}?id={1}">{1}</a>',
                reverse(view_name),
                obj.new_report.id,
            )
        return ''

    admin_new_report.short_description = 'New Report'


class MaterialManagementCostAdmin(
    CSVExportActionMixin,
    CSVImportActionMixin,
    ButtonActionsBaseAdmin,
):
    """Admin for managing loss factors for manufactors."""

    model = MaterialManagementCost
    list_display = ['id', 'manufactor', 'management_cost', 'date_from', 'date_to']

    actions = ('export_to_csv',)
    button_actions = ('import_from_csv',)

    @admin.action(description='Export selected items as CSV')
    def export_to_csv(self, request, queryset):
        return self.export_to_csv_file(
            request,
            queryset=queryset,
            filename='Material_Management_Cost',
            exporter_class=MaterialManagementCostExporter,
        )

    @admin.action(description='Import from CSV')
    def import_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            MaterialManagementCostImporter,
        )


class PricingFactorItemImportAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'created_count',
        'created_at',
        'created_by',
    )
    readonly_fields = (
        'created_count',
        'created_at',
        'created_by',
    )

    def save_model(self, request, obj, form, change):
        importer = LegacyPricingFactorItemImporter()
        created_count = importer.load()

        obj.created_by = request.user
        obj.created_count = created_count
        super().save_model(request, obj, form, change)
        self.message_user(request, f'Created {created_count} pricing factor items.')


class PricingFactorItemAdmin(
    CSVExportActionMixin,
    CSVImportActionMixin,
    ButtonActionsBaseAdmin,
):
    form = PricingFactorItemForm
    list_display = (
        'codename',
        'category_display',
        'c1_category',
        'c2_type',
        'c3_version',
        'c4_description',
        'weight',
        'thickness',
        'length',
        'measurement_unit_display',
        'price',
        'loss_factor',
        'status',
    )

    inlines = (CustomPricingFactorInline,)

    search_fields = ('codename',)

    list_filter = (
        'category',
        PricingFactorItemCodenameTypeFilter,
        PricingFactorItemCodenameVersionFilter,
        'status',
    )
    actions = (
        'export_price_factor_items_to_csv',
        'export_price_factor_items_to_csv_with_cpf_columns',
    )
    button_actions = (
        'import_price_factor_items_from_csv',
        'create_pricing_factors_object_from_pfi',
    )
    model = PricingFactorItem

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ('codename', 'price', 'loss_factor', 'archived_at')
        return ()

    def has_add_permission(self, request):
        return request.user.groups.filter(name='PT_tech').exists()

    @admin.action(description='Export selected as CSV with CPF columns')
    def export_price_factor_items_to_csv_with_cpf_columns(self, request, queryset):
        """Download selected Price Factors Items data as CSV, with related CPF."""
        return self.export_to_csv_file(
            request,
            queryset,
            filename='Pricing_Factor_Items',
            exporter_class=PricingFactorItemCPFColumnsExporter,
        )

    @admin.action(description='Export selected as CSV')
    def export_price_factor_items_to_csv(self, request, queryset):
        """Download selected Price Factors Items data as CSV."""
        return self.export_to_csv_file(
            request,
            queryset,
            filename='Pricing_Factor_Items',
            exporter_class=PricingFactorItemExporter,
        )

    @admin.action(description='Import PFI from CSV')
    def import_price_factor_items_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            PricingFactorItemsImporter,
        )

    @admin.action(description='Create new PF')
    def create_pricing_factors_object_from_pfi(self, request):
        """Download selected Price Factors Items data as JSON."""
        exporter = PricingFactorItemPFDataExporter()
        pf_json_data = exporter.to_json()
        name = '{date} Admin'.format(
            date=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        )
        PricingFactors.objects.create(
            name=name,
            description='Generated from PFI set',
            data=pf_json_data,
            created_by=request.user,
        )
        messages.success(request, 'New Pricing Factors were created.')
        return HttpResponseRedirect('../')


class TelmexMagazineCodeAdmin(admin.ModelAdmin):
    list_display = ('codename', 'magazine_code')
    search_fields = ('codename', 'magazine_code')
    model = TelmexMagazineCode


class GalaStockLevelMaterialAdmin(admin.ModelAdmin):
    model = GalaStockLevelMaterial
    list_display = (
        'index',
        'invoice',
        'warehouse',
        'quantity',
        'entry_date',
        'defect',
        'defect_description',
        'supplier',
    )
    search_fields = ('index', 'invoice')


class MaterialInfoAttachmentAdmin(admin.ModelAdmin):
    model = MaterialInfoAttachment
    list_display = (
        'id',
        'manufacturer_code_link',
        'file_link',
        'document_type',
        'optional',
        'valid_today',
        'valid_from',
        'valid_to',
    )
    list_select_related = ('manufacturer_code',)
    search_fields = (
        'id',
        'manufacturer_code__code',
    )
    list_filter = (
        'document_type',
        ('valid_from', DateRangeFilter),
        ('valid_to', DateRangeFilter),
    )
    raw_id_fields = ('manufacturer_code',)

    def get_queryset(self, request) -> QuerySet:
        return super().get_queryset(request).with_validity()

    @admin.display(description='Manufacturer Code', ordering='manufacturer_code__code')
    def manufacturer_code_link(self, obj):
        if obj.manufacturer_code:
            link = reverse(
                'admin:production_margins_manufacturercode_change',
                args=[obj.manufacturer_code.id],
            )
            return format_html('<a href="{}">{}</a>', link, obj.manufacturer_code.code)
        return '-'

    @admin.display(description='File')
    def file_link(self, obj):
        if obj.file and obj.file.name:
            return format_html('<a href="{}">{}</a>', obj.file.url, obj.file.name)
        return 'No File'

    @admin.display(boolean=True)
    def valid_today(self, obj):
        return obj.is_valid_today


admin.site.register(GalaStockLevelMaterial, GalaStockLevelMaterialAdmin)
admin.site.register(PricingFactorItemImport, PricingFactorItemImportAdmin)
admin.site.register(PricingFactorItem, PricingFactorItemAdmin)
admin.site.register(PricingFactors, PricingFactorsAdmin)
admin.site.register(ManufacturerCode, ManufacturerCodeAdmin)
admin.site.register(CustomPricingFactor, CustomPricingFactorAdmin)
admin.site.register(
    CustomPricingFactorChangeRequest, CustomPricingFactorChangeRequestAdmin
)
admin.site.register(ElementManagedInfo, ElementManagedInfoAdmin)
admin.site.register(ElementsOrder, ElementsOrderAdmin)
admin.site.register(ElementsOrderHistory, ElementsOrderHistoryAdmin)
admin.site.register(MaterialManagementCost, MaterialManagementCostAdmin)
admin.site.register(TelmexMagazineCode, TelmexMagazineCodeAdmin)
admin.site.register(MaterialInfoAttachment, MaterialInfoAttachmentAdmin)
