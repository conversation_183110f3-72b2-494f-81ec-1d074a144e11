import abc
import datetime

from typing import ClassVar

from django.contrib import admin
from django.db.models import Q

from custom.admin_filters import InputCommaSeparatedFilter
from custom.filters import BaseMultipleChoiceListFilter
from producers.choices import BatchType
from producers.models import Manufactor
from production_margins.choices import (
    MaterialInfoAttachmentType,
    PricingFactorItemCategory,
)
from production_margins.models import PricingFactorItem


class ManufacturerFilter(admin.SimpleListFilter):
    title = 'Manufacturer filter'
    parameter_name = 'manufacturer'

    def lookups(self, request, model_admin):
        return (
            (manufacturer.id, manufacturer.name)
            for manufacturer in Manufactor.objects.all()
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(batches__manufactor=self.value()).distinct()
        return queryset


class CalculatedOrderTypeFilter(admin.SimpleListFilter):
    title = 'Order type filter'
    parameter_name = 'order_type'

    def lookups(self, request, model_admin):
        return (
            ('complaint', 'Complaint'),
            ('new', 'New Order'),
            ('complaint_manufactor', 'Complaint Manufactor'),
            ('invoice_to_order', 'Invoice to order'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'complaint':
            return queryset.filter(
                manufactor_fault=False, batches__batch_type=BatchType.COMPLAINTS
            ).distinct()
        if self.value() == 'new':
            return queryset.exclude(batches__batch_type=BatchType.COMPLAINTS).distinct()
        if self.value() == 'complaint_manufactor':
            return queryset.filter(
                manufactor_fault=True,
                batches__batch_type=BatchType.COMPLAINTS,
            ).distinct()
        if self.value() == 'invoice_to_order':
            return queryset.filter(invoice_to_order=True).distinct()
        return queryset


class ProductShelfTypeFilter(admin.SimpleListFilter):
    title = 'Shelf Type'
    parameter_name = 'shelftypefilter'

    def lookups(self, request, model_admin):
        return (
            ('type1', 'Type01'),
            ('type2', 'Type02'),
            ('veneer1', 'Veneer Type01'),
            ('type3', 'Type03'),
            ('type13', 'Type13'),
        )

    def queryset(self, request, queryset):
        cached_shelf_type = {
            'type1': 'T1',
            'type2': 'T2',
            'veneer1': 'F1',
            'type3': 'W3',
            'type13': 'W13',
        }.get(self.value())

        if cached_shelf_type:
            return queryset.filter(
                batches__batch_items__cached_shelf_type=cached_shelf_type
            ).distinct()
        return queryset


class CustomPricingFactorCategoryFilter(BaseMultipleChoiceListFilter):
    title = 'By category'
    parameter_name = 'category'
    template = 'admin/custom_filters.html'

    def lookups(self, request, model_admin):
        return PricingFactorItemCategory.choices

    def queryset(self, request, queryset):
        if self.value():
            values = self.value().split(',')
            return queryset.filter(pricing_factor_item__category__in=values)
        return queryset


class PricingFactoryItemCodenamePartFilter(admin.SimpleListFilter):
    title: ClassVar[str]
    parameter_name: ClassVar[str]
    attr_name: ClassVar[str]

    @classmethod
    def get_queryset(cls):
        return PricingFactorItem.objects.all()

    @property
    @abc.abstractmethod
    def regex_value(self):
        """Regex string to filter by codename field."""

    def lookups(self, request, model_admin):
        return sorted(
            set(  # noqa: C401
                (getattr(obj, self.attr_name), getattr(obj, self.attr_name))
                for obj in self.get_queryset()
            )
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        return queryset.filter(codename__iregex=self.regex_value)


class PricingFactorItemCodenameTypeFilter(PricingFactoryItemCodenamePartFilter):
    title = 'codename type'
    parameter_name = 'codename_type'
    attr_name = 'c2_type'

    @property
    def regex_value(self):
        return f'^[^_]+_{self.value()}_[^_]+_[^_]+$'


class PricingFactorItemCodenameVersionFilter(PricingFactoryItemCodenamePartFilter):
    title = 'codename version'
    parameter_name = 'codename_version'
    attr_name = 'c3_version'

    @property
    def regex_value(self):
        return f'^[^_]+_[^_]+_{self.value()}_[^_]+$'


class PricingFactorItemArchivedFilter(BaseMultipleChoiceListFilter):
    title = 'Archived filter'
    parameter_name = 'archived'

    def lookups(self, request, model_admin):
        return (
            ('1', 'Active'),
            ('0', 'Archival'),
        )

    def is_true(self):
        return bool(int(self.value()))

    def queryset(self, request, queryset):
        if self.value() in {'0', '1'}:
            return queryset.filter(archived__isnull=self.is_true())
        return queryset


class CPFActiveFilter(BaseMultipleChoiceListFilter):
    title = 'Custom pricing factors active filter'
    parameter_name = 'active'

    def lookups(self, request, model_admin):
        return (
            ('1', 'Active'),
            ('0', 'Inactive'),
        )

    def is_active(self, cpf):
        return not (cpf.date_to and datetime.date.today() > cpf.date_to)

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(
                id__in=[cpf.id for cpf in queryset if self.is_active(cpf)]
            )
        if self.value() == '0':
            return queryset.filter(
                id__in=[cpf.id for cpf in queryset if not self.is_active(cpf)]
            )
        return queryset


class ElementManagedInfoTypeFilter(BaseMultipleChoiceListFilter):
    title = 'By type'
    parameter_name = 'type'
    template = 'admin/custom_filters.html'

    lookup_types = {
        '0': 'packaging',
        '1': 'service',
        '2': 'fitting',
        '3': 'material',
        '4': 'semiproduct',
    }

    def lookups(self, request, model_admin):
        return [(value, name) for value, name in self.lookup_types.items()]

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        values = self.value().split(',')
        query = Q()
        for value, name in self.lookup_types.items():
            if value in values:
                query |= Q(pricing_factor_item__codename__istartswith=name)

        queryset = queryset.filter(query)
        return queryset


class BatchIdFilter(InputCommaSeparatedFilter):
    lookup_field = 'batches__id__in'
    parameter_name = 'batches__in'
    title = 'Batch ids'


class ProductIdFilter(InputCommaSeparatedFilter):
    lookup_field = 'batches__batch_items__id__in'
    parameter_name = 'batch_items__in'
    title = 'Product ids'


class MissingMaterialInfoAttachmentFilter(admin.SimpleListFilter):
    title = 'missing attachment'
    parameter_name = 'missing_attachment'

    def lookups(self, request, model_admin):
        choices = MaterialInfoAttachmentType.choices
        choices.remove(
            (
                MaterialInfoAttachmentType.ANY.value,
                MaterialInfoAttachmentType.ANY.label,
            )
        )
        return choices

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        attachment_type = int(self.value())
        today = datetime.date.today()

        valid_attachments = queryset.filter(
            Q(attachments__document_type=attachment_type)
            & Q(attachments__valid_from__lte=today)
            & Q(
                Q(attachments__valid_to__isnull=True)
                | Q(attachments__valid_to__gte=today)
            ),
        ).distinct()

        return queryset.exclude(pk__in=valid_attachments)


class ManufacturerCodePrefixFilter(admin.SimpleListFilter):
    title = 'Code prefix'
    parameter_name = 'code_prefix'

    def lookups(self, request, model_admin):
        return (
            ('TY', 'TY'),
            ('DR', 'DR'),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(code__istartswith=self.value())
        return queryset
