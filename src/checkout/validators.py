import logging

from decimal import Decimal

from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from checkout.constants import REQUIRED_ORDER_ADDRESS_FIELDS
from checkout.enums import ViesResponseStatus
from checkout.exceptions import LackingTransactionError
from checkout.models import ViesVatValidation
from checkout.services.region_restrictions import RegionRestrictionService
from custom.models import Countries
from orders.models import Order
from orders.services.vat_details import STANDARD_VAT
from regions.constants import OTHER_REGION_NAME
from regions.services.limitations import LimitationService

logger = logging.getLogger('cstm')

MAIN_ERROR_MESSAGE = _(
    'Ensure your delivery country is right or contact customer support.'
)
VAT_ERROR_LOG = 'VAT rate does not match country VAT for order #%s'


class CheckoutValidator:
    def __init__(self, order: Order):
        self.order = order

    def validate(self) -> None:
        self._validate_transaction_price()
        self._validate_promo()
        self._validate_region_other()
        self._validate_region_vs_country()
        self._validate_vat()
        self._validate_address_data()
        self._validate_products_and_services_limitations()
        # TODO: Uncomment when stock service is valid
        # self._validate_product_in_stock()

    def _validate_transaction_price(self) -> None:
        transaction = self.order.transactions.last()
        if not transaction:
            raise LackingTransactionError('Cart has no transactions')

        if transaction.amount != self.order.adyen_price:
            raise serializers.ValidationError(_('Prices missmatch'))

    def _validate_promo(self) -> None:
        for voucher in self.order.vouchers.all():
            if not voucher.is_active():
                raise serializers.ValidationError(_('Promo is not active'))

    def _validate_region_other(self) -> None:
        if self.order.region.name == OTHER_REGION_NAME:
            raise serializers.ValidationError(_('Declare your region'))

    def _validate_region_vs_country(self) -> None:
        if self.order.region.get_country(without_cache=True).name != self.order.country:
            logger.error(
                'Region and country do not match for order %s; region: %s, country: %s',
                self.order.id,
                self.order.region.name,
                self.order.country,
            )
            raise serializers.ValidationError(MAIN_ERROR_MESSAGE)

    def _validate_vat(self) -> None:
        special_vat_countries = {Countries.united_kingdom.name, Countries.poland.name}

        region = self.order.region
        country_vat = region.get_country().vat

        # special case: UK and PL clients always pay full VAT
        if region.name in special_vat_countries:
            if self.order.vat_rate != country_vat:
                logger.error(VAT_ERROR_LOG, self.order.id)
                raise serializers.ValidationError(MAIN_ERROR_MESSAGE)
            return

        # B2B case: Client has VAT number
        if self.order.vat:
            # check VAT validation status
            has_valid_vat = ViesVatValidation.objects.filter(
                vat_number=self.order.vat, response_status=ViesResponseStatus.VALID
            ).exists()

            has_invalid_vat = ViesVatValidation.objects.filter(
                vat_number=self.order.vat, response_status=ViesResponseStatus.INVALID
            ).exists()

            if has_valid_vat and self.order.vat_rate != Decimal(0):
                logger.error(
                    'VAT rate is not 0 for valid VAT number for order #%s',
                    self.order.id,
                )
            elif has_invalid_vat and self.order.vat_rate != STANDARD_VAT:
                logger.error(
                    'Order #%s with incorrect VAT number but wrong VAT rate',
                    self.order.id,
                )
            # if ViesVatValidation has connection errors, we do nothing as it provides
            # no info about the VAT number
            return

        # B2C case: No VAT number
        expected_vat = country_vat if region.is_eu else Decimal('0')
        if self.order.vat_rate != expected_vat:
            logger.error(VAT_ERROR_LOG, self.order.id)
            raise serializers.ValidationError(MAIN_ERROR_MESSAGE)

    def _validate_address_data(self) -> None:
        is_valid = all(
            getattr(self.order, field, None) for field in REQUIRED_ORDER_ADDRESS_FIELDS
        )
        if not is_valid:
            raise serializers.ValidationError(_('Empty address data'))

        region_restriction_service = RegionRestrictionService(
            postal_code=self.order.postal_code, region=self.order.region
        )
        if region_restriction_service.is_outside_of_tax_union():
            raise serializers.ValidationError(_('Address outside of tax union'))
        if (
            self.order.has_s01
            and region_restriction_service.is_sofa_delivery_unavailable()
        ):
            raise serializers.ValidationError(
                _('Address outside of sofa delivery area')
            )

    def _validate_products_and_services_limitations(self) -> None:
        limitation_service = LimitationService(region=self.order.region)
        if not limitation_service.is_assembly_available and self.order.assembly:
            raise serializers.ValidationError(_('No assembly service in this region'))
        if not limitation_service.is_corduroy_available and any(
            order_item.is_corduroy for order_item in self.order.items.all()
        ):
            msg = _('Corduroy material is not available in this region')
            raise serializers.ValidationError(msg)
        if not limitation_service.is_s01_available and any(
            order_item.is_s01 for order_item in self.order.items.all()
        ):
            raise serializers.ValidationError(_('Sofa is not available in this region'))
        if not limitation_service.is_t03_available and any(
            order_item.is_t03 for order_item in self.order.items.all()
        ):
            msg = _('Tone wardrobe is not available in this region')
            raise serializers.ValidationError(msg)

    # TODO: Uncomment when stock service is valid
    # def _validate_product_in_stock(self) -> None:
    #     sku_codes_in_order = self.order.items.values_list(
    #         'sku_variant__sku_code', flat=True
    #     )
    #
    #     filtered_sku_codes = [
    #         sku_code for sku_code in sku_codes_in_order if sku_code is not None
    #     ]
    #     if not filtered_sku_codes:
    #         return
    #
    #     sku_stock_service = SkuStockService()
    #     stock_levels = sku_stock_service.get_sku_variant_available_quantity(
    #         filtered_sku_codes
    #     )
    #
    #     if not all(stock_levels.values()):
    #         raise serializers.ValidationError(_('Some items are out of stock'))
