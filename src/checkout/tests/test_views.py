from decimal import Decimal
from unittest.mock import patch
from urllib.parse import urlencode

from django.urls import reverse
from rest_framework import status

import pytest

from carts.tests.factories import NO_ORDER
from custom.constants import (
    VAT_EU,
    VAT_NORMAL,
)
from custom.models import Countries
from events.models import Event
from orders.enums import OrderStatus
from skus.services import SkuStockService
from user_profile.choices import UserType


@pytest.mark.django_db
class TestCheckoutAPIViewSet:
    @pytest.fixture
    def checkout_form_post_data(self):
        return {
            'email': '<EMAIL>',
            'firstName': 'first_name',
            'lastName': 'last_name',
            'streetAddress1': 'street_address_1',
            'streetAddress2': 'street_address_2',
            'city': 'city',
            'phonePrefix': '+48',
            'phone': '*********',
            'postalCode': '55-100',
            'country': 'Poland',
            'floorNumber': 5,
            'noElevator': True,
            'invoicePhone': '*********',
            'notes': 'Some Additional info',
            'vat': 'Vat-7',
            'invoiceFirstName': 'invoice_first_name',
            'invoiceLastName': 'invoice_last_name',
            'invoiceStreetAddress1': 'invoice_street_address_1',
            'invoicePostalCode': '55-100',
            'invoiceCity': 'invoice_city',
            'invoiceCountry': 'Poland',
            'companyName': 'Company name',
            'newsletter': True,
            'sms': True,
        }

    def test_should_return_ok_and_form_validated_field_false(self, client, cart):
        cart.order.phone_prefix = None  # required field
        cart.order.save(update_fields=['phone_prefix'])
        url = reverse('checkout_v2_cart-status', kwargs={'pk': cart.id})

        client.force_login(cart.owner)
        response = client.get(url, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == cart.order.status
        assert response.data['is_empty'] is False
        assert response.data['form_validated'] is False

    def test_order_is_free(self, client, order_factory):
        order = order_factory(status=OrderStatus.DRAFT)
        url = reverse('checkout_v2_order-is-free', kwargs={'pk': order.id})

        client.force_login(order.owner)
        response = client.get(url, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['is_free'] is False
        assert response.data['order_id'] == order.id

    def test_cart_status_validated_order(
        self,
        client,
        order_factory,
    ):
        order_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'street_address_1': 'Test street 1',
            'city': 'Test city',
            'postal_code': '12345',
            'country': 'FI',
            'phone': '123456789',
            'phone_prefix': '+48',
        }
        order = order_factory(status=OrderStatus.DRAFT, **order_data)
        url = reverse('checkout_v2_order-status', kwargs={'pk': order.id})

        client.force_login(order.owner)
        response = client.get(url, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == OrderStatus.DRAFT
        assert response.data['is_empty'] is False
        assert response.data['form_validated'] is True
        assert response.data['cart_id'] == order.cart.id

    def test_cart_status_for_cart_which_doesnt_belong_to_user(
        self,
        client,
        order_factory,
        user,
    ):
        order = order_factory(status=OrderStatus.CART)
        assert order.cart.owner != user

        url = reverse('checkout_v2_cart-status', kwargs={'pk': order.cart.id})

        client.force_login(user)
        response = client.get(url, format='json')

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_retrieve_initial_form_data(self, client, cart):
        url = reverse('checkout_v2_cart-initial-form-data', kwargs={'pk': cart.id})

        client.force_login(cart.owner)
        response = client.get(url, format='json')
        assert response.status_code == status.HTTP_200_OK

    @patch('checkout.views.subscribe_email_event')
    @patch('checkout.views.subscribe_user_to_sms_group')
    def test_sync_with_order(
        self,
        _,  # noqa: PT019
        __,  # noqa: PT019
        client,
        user_profile_factory,
        cart_factory,
        checkout_form_post_data,
    ):
        user_profile = user_profile_factory(
            user_type=UserType.CUSTOMER,
            newsletter_agreed=False,
            sms_agreed=False,
        )
        cart = cart_factory(order=NO_ORDER, owner=user_profile.user)

        url = reverse('checkout_v2_cart-sync-with-order', kwargs={'pk': cart.id})

        client.force_login(cart.owner)
        response = client.patch(
            url,
            data=checkout_form_post_data,
            content_type='application/json',
        )
        assert response.status_code == status.HTTP_200_OK

        cart.refresh_from_db()
        order = cart.order
        assert order.email == '<EMAIL>'
        assert order.owner.profile.newsletter_agreed is True
        assert order.owner.profile.sms_agreed is True
        assert order.no_elevator is True
        assert order.street_address_1 == 'street_address_1'

    @pytest.mark.parametrize(
        ['postal_code', 'region_name'],  # noqa: PT006
        [
            ('110', 'denmark'),
            ('27498', 'germany'),
            ('35503', 'spain'),
            ('63086', 'greece'),
            ('22061', 'italy'),
            ('22104', 'finland'),
        ],
    )
    @patch('checkout.views.subscribe_email_event')
    def test_update_cart_with_postal_code_from_special_territories_of_eu(
        self,
        _,  # noqa: PT019
        region_name,
        postal_code,
        user_profile_factory,
        region_factory,
        cart_factory,
        client,
        checkout_form_post_data,
    ):
        region = region_factory(name=region_name)
        user_profile = user_profile_factory(
            user_type=UserType.CUSTOMER,
            newsletter_agreed=False,
            region=region,
        )
        cart = cart_factory(order=NO_ORDER, owner=user_profile.user, region=region)

        checkout_form_post_data['postalCode'] = postal_code
        checkout_form_post_data['country'] = region_name

        url = reverse('checkout_v2_cart-sync-with-order', kwargs={'pk': cart.id})
        client.force_login(cart.owner)
        response = client.patch(
            url,
            data=checkout_form_post_data,
            content_type='application/json',
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == {
            'errorCodes': {'postalCode': ['invalid']},
            'postalCode': [
                f'Postal code {postal_code} belongs to the special territory of EU.'
                'We do not deliver there',
            ],
        }

    @patch('checkout.vat.validation.vat_validator', return_value=True)
    def test_update_cart_with_correct_vat_number(
        self,
        _,  # noqa: PT019
        client,
        order_factory,
        country_factory,
    ):
        vat_number = 'DE262231084'
        germany_country = country_factory(germany=True)
        order = order_factory(
            email='<EMAIL>',
            country=Countries.united_kingdom.name,
            region=germany_country.region,
            items=[],
            status=OrderStatus.DRAFT,
            vat_type=VAT_NORMAL,
            vat='',
        )
        client.force_login(order.owner)
        data = {'email': '<EMAIL>', 'vat': vat_number}
        url = reverse('checkout_v2_cart-sync-with-order', kwargs={'pk': order.cart.id})

        response = client.patch(url, data=data, content_type='application/json')

        order.refresh_from_db()
        assert response.status_code == status.HTTP_200_OK
        assert order.email == '<EMAIL>'
        assert order.vat_type == VAT_EU

    @patch('orders.services.process_to_production.MoveOrderToProduction.move')
    @patch('b2b.tasks.update_pipedrive_account_after_order_payment')
    @patch('pricing_v3.models.abstract.PriceAbstractModel.is_free', return_value=True)
    @patch('orders.models.Order.is_free', return_value=True)
    def test_handle_free_order_emits_transact_related_events(
        self,
        _,  # noqa: PT019
        __,  # noqa: PT019
        ___,  # noqa: PT019
        ____,  # noqa: PT019
        order_factory,
        api_client,
    ):
        order = order_factory(status=OrderStatus.DRAFT)
        url = reverse('checkout_v2_order-handle-free-order', kwargs={'pk': order.id})
        api_client.force_authenticate(order.owner)

        response = api_client.post(url, {}, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert Event.objects.filter(event_name='PaymentEntryEvent').exists()
        assert Event.objects.filter(event_name='PurchaseEvent').exists()

    @patch('carts.services.cart_service.CartService.recalculate_cart')
    def test_validate_vat_when_vat_number_correct(
        self,
        recalculate_cart_mock,
        cart_factory,
        country,
        api_client,
    ):
        cart = cart_factory(region=country.region)
        correct_vat = 'DE262231084'

        url = reverse('checkout_v2_cart-validate-vat-number', kwargs={'pk': cart.id})
        api_client.force_authenticate(cart.owner)
        response = api_client.post(url, data={'vat_number': correct_vat}, format='json')

        cart.refresh_from_db()

        assert response.status_code == status.HTTP_200_OK
        assert response.data['validated'] is True
        assert recalculate_cart_mock.called
        assert cart.vat == correct_vat

    @patch('carts.services.cart_service.CartService.recalculate_cart')
    def test_validate_vat_when_vat_number_has_no_country_prefix(
        self,
        recalculate_cart_mock,
        api_client,
        country_factory,
        cart_factory,
    ):
        country = country_factory(united_kingdom=True)
        cart = cart_factory(region=country.region)

        url = reverse('checkout_v2_cart-validate-vat-number', kwargs={'pk': cart.id})
        api_client.force_authenticate(cart.owner)
        response = api_client.post(url, data={'vat_number': '123'}, format='json')

        cart.refresh_from_db()

        assert response.status_code == status.HTTP_200_OK
        assert response.data['validated'] is False
        assert recalculate_cart_mock.called
        assert cart.vat == f'{country.code.upper()}123'

    @patch('carts.services.cart_service.CartService.recalculate_cart')
    def test_validate_vat_when_vat_number_is_incorrect(
        self,
        recalculate_cart_mock,
        cart_factory,
        api_client,
        country,
    ):
        cart = cart_factory(region=country.region)

        url = reverse('checkout_v2_cart-validate-vat-number', kwargs={'pk': cart.id})
        api_client.force_authenticate(cart.owner)
        response = api_client.post(url, data={'vat_number': 'PL123'}, format='json')

        cart.refresh_from_db()

        assert response.status_code == status.HTTP_200_OK
        assert response.data['validated'] is False
        assert recalculate_cart_mock.called
        assert cart.vat == 'PL123'

    def test_validate_vat_when_no_vat_number_provided(
        self,
        cart_factory,
        api_client,
        country,
    ):
        cart = cart_factory(region=country.region)

        url = reverse('checkout_v2_cart-validate-vat-number', kwargs={'pk': cart.id})
        api_client.force_authenticate(cart.owner)
        response = api_client.post(url, data={'vat_number': None}, format='json')

        cart.refresh_from_db()

        assert response.status_code == status.HTTP_200_OK
        assert response.data['validated'] is False

    def test_order_with_no_transactions_returns_400(self, api_client, order_factory):
        order = order_factory(status=OrderStatus.CART)

        url = reverse(
            'checkout_v2_order-validate-order-before-payment', args=[order.id]
        )
        api_client.force_authenticate(order.owner)
        response = api_client.post(url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data.get('message') == 'Cart has no transactions'

    @pytest.mark.parametrize(
        'region_total_price, transaction_amount, expected_validated',  # noqa: PT006
        [
            (Decimal('100.00'), 10000, True),
            (Decimal('200.00'), 200, False),
        ],
    )
    def test_order_transaction_price_validation(
        self,
        api_client,
        order_factory,
        country_factory,
        transaction_factory,
        region_total_price,
        transaction_amount,
        expected_validated,
        mocker,
    ):
        germany = country_factory(germany=True)
        order = order_factory(
            region=germany.region,
            country=germany.name,
            vat_rate=germany.vat,
            status=OrderStatus.CART,
            region_total_price=region_total_price,
        )

        transaction_factory(order=order, amount=transaction_amount)

        mocker.patch.object(
            SkuStockService,
            'get_sku_variant_available_quantity',
            return_value={order.items.first().sku_code: 10},
        )

        url = reverse(
            'checkout_v2_order-validate-order-before-payment', args=[order.id]
        )
        api_client.force_authenticate(order.owner)
        response = api_client.post(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data.get('validated') == expected_validated


class TestPostalCodeValidationApiView:
    url = reverse('postal-code-validation')

    def test_no_cart_id(self, api_client, user):
        query_params = {'postal_code': '123', 'cart_id': 999}
        api_client.force_authenticate(user)

        response = api_client.get(f'{self.url}?{urlencode(query_params)}')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == 'Cart does not exist'

    def test_outside_of_tax_union_invalid(
        self, api_client, user, region_dk, cart_factory
    ):
        cart = cart_factory(owner=user, region=region_dk)
        query_params = {'postal_code': '110', 'cart_id': cart.id}
        api_client.force_authenticate(user)

        response = api_client.get(f'{self.url}?{urlencode(query_params)}')

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {'valid': False, 'reason': 'outside of tax union'}

    def test_outside_of_tax_union_valid(
        self, api_client, user, region_de, cart_factory
    ):
        cart = cart_factory(owner=user, region=region_de)
        query_params = {'postal_code': '110', 'cart_id': cart.id}
        api_client.force_authenticate(user)

        response = api_client.get(f'{self.url}?{urlencode(query_params)}')

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {'valid': True}

    def test_sofa_delivery_unavailable_invalid(
        self, api_client, user, region_it, cart_factory, cart_item_factory
    ):
        cart = cart_factory(
            owner=user, region=region_it, items=[cart_item_factory(is_sotty=True)]
        )
        query_params = {'postal_code': '90000', 'cart_id': cart.id}
        api_client.force_authenticate(user)

        response = api_client.get(f'{self.url}?{urlencode(query_params)}')

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            'valid': False,
            'reason': 'outside of sofa delivery area',
        }

    def test_sofa_delivery_unavailable_valid(
        self, api_client, user, region_de, cart_factory, cart_item_factory
    ):
        cart = cart_factory(
            owner=user, region=region_de, items=[cart_item_factory(is_sotty=True)]
        )
        query_params = {'postal_code': '90000', 'cart_id': cart.id}
        api_client.force_authenticate(user)

        response = api_client.get(f'{self.url}?{urlencode(query_params)}')

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {'valid': True}

    def test_sofa_delivery_unavailable_valid_case_no_sotty(
        self, api_client, user, region_it, cart_factory, cart_item_factory
    ):
        cart = cart_factory(
            owner=user, region=region_it, items=[cart_item_factory(is_jetty=True)]
        )
        query_params = {'postal_code': '90000', 'cart_id': cart.id}
        api_client.force_authenticate(user)

        response = api_client.get(f'{self.url}?{urlencode(query_params)}')

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {'valid': True}
