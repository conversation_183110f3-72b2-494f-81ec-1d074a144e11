from unittest.mock import patch

from django.test import override_settings
from rest_framework.serializers import ValidationError

import pytest

from pytest_cases import parametrize_with_cases

from carts.services.cart_service import CartService
from checkout.validators import CheckoutValidator
from skus.services import SkuStockService
from vouchers.enums import VoucherOrigin
from vouchers.services.voucher_service import VoucherService


class InvalidProductsAndServicesLimitationsCases:
    def case_assembly(self, order_factory):
        order = order_factory(assembly=True)
        return order, {'ASSEMBLY_REGION_KEYS': {}}

    def case_corduroy(self, order_factory, order_item_factory):
        order = order_factory()
        order_item_factory(is_sofa_corduroy=True, order=order)
        return order, {'CORDUROY_RESTRICTED_REGIONS': {order.region.name}}

    def case_s01(self, order_factory, order_item_factory):
        order = order_factory()
        order_item_factory(is_sofa_wool=True, order=order)
        return order, {'S01_REGION_KEYS': {}}

    def case_t03(self, order_factory, order_item_factory):
        order = order_factory()
        order_item_factory(is_tone_wardrobe=True, order=order)
        return order, {'T03_REGION_KEYS': {}}


class ValidProductsAndServicesLimitationsCases:
    def case_assembly(self, order_factory):
        order = order_factory(assembly=True)
        return order, {'ASSEMBLY_REGION_KEYS': {order.region.name}}

    def case_corduroy(self, order_factory, order_item_factory):
        order = order_factory()
        order_item_factory(is_sofa_corduroy=True, order=order)
        return order, {'CORDUROY_RESTRICTED_REGIONS': {}}

    def case_s01(self, order_factory, order_item_factory):
        order = order_factory()
        order_item_factory(is_sofa_wool=True, order=order)
        return order, {'S01_REGION_KEYS': {order.region.name}}

    def case_t03(self, order_factory, order_item_factory):
        order = order_factory()
        order_item_factory(is_tone_wardrobe=True, order=order)
        return order, {'T03_REGION_KEYS': {order.region.name}}


@pytest.mark.django_db
class TestCheckoutValidator:
    def test_influencers_barter_deal(
        self,
        cart_factory,
        voucher_factory,
        voucher_barter_deal_factory,
        country_factory,
        transaction_factory,
        sku_variant_factory,
        cart_item_factory,
        mocker,
    ):
        germany_country = country_factory(germany=True)
        cart = cart_factory(
            region=germany_country.region,
        )
        sku_variant = sku_variant_factory()
        cart_item_factory(cart=cart, cart_item=sku_variant, quantity=1)
        voucher = voucher_factory(
            origin=VoucherOrigin.INFLUENCERS_BARTER_DEAL,
        )
        voucher_barter_deal_factory(
            voucher=voucher,
            currency=germany_country.region.currency,
        )

        VoucherService(cart, voucher.code).process_voucher()
        cart.refresh_from_db()

        mocker.patch.object(
            SkuStockService,
            'get_sku_variant_available_quantity',
            return_value={sku_variant.sku_code: 5},
        )

        order = CartService(cart).sync_with_order()
        order.country = germany_country.name
        transaction_factory(order=order, amount=order.adyen_price)
        validator = CheckoutValidator(order)
        validator.validate()

    def test_price_mismatch(
        self,
        cart_factory,
        voucher_factory,
        country_factory,
        transaction_factory,
    ):
        germany_country = country_factory(germany=True)
        cart = cart_factory(
            region=germany_country.region,
        )
        voucher = voucher_factory(
            origin=VoucherOrigin.MANUAL,
        )

        VoucherService(cart, voucher.code).process_voucher()
        cart.refresh_from_db()

        order = CartService(cart).sync_with_order()
        order.country = germany_country.name
        transaction_factory(order=order, amount=order.adyen_price + 2)
        validator = CheckoutValidator(order)
        with pytest.raises(ValidationError) as e:  # noqa: PT012
            validator.validate()
            assert 'Price mismatch' in str(e.value)

    @pytest.mark.parametrize(
        'nullable_field',
        ['email', 'street_address_1', 'postal_code', 'city'],
    )
    def test_address_data_case_nullable_field(
        self,
        nullable_field,
        cart_factory,
        voucher_factory,
        country_factory,
        transaction_factory,
    ):
        germany_country = country_factory(name='germany', germany=True)
        cart = cart_factory(
            region=germany_country.region,
        )
        voucher = voucher_factory(
            origin=VoucherOrigin.MANUAL,
        )
        VoucherService(cart, voucher.code).process_voucher()
        cart.refresh_from_db()
        order = CartService(cart).sync_with_order()
        order.refresh_from_db()
        transaction_factory(order=order, amount=order.adyen_price)

        setattr(order, nullable_field, None)
        order.save(update_fields=[nullable_field])
        order.refresh_from_db()

        validator = CheckoutValidator(order)
        with pytest.raises(ValidationError) as e:
            validator.validate()
        assert 'Empty address data' in str(e.value)

    # TODO: Uncomment when stock service is valid
    # def test_item_out_of_stock(
    #     self,
    #     cart_factory,
    #     cart_item_factory,
    #     sku_variant_factory,
    #     transaction_factory,
    #     region_de,
    #     mocker,
    # ):
    #     cart = cart_factory(region=region_de)
    #     sku_variant = sku_variant_factory()
    #     cart_item_factory(cart=cart, cart_item=sku_variant, quantity=10)
    #     cart.refresh_from_db()
    #     order = CartService(cart).sync_with_order()
    #     order.refresh_from_db()
    #     transaction_factory(order=order, amount=order.adyen_price)
    #     order.refresh_from_db()
    #     mocker.patch.object(
    #         SkuStockService,
    #         'get_sku_variant_available_quantity',
    #         return_value={sku_variant.sku_code: 0},
    #     )
    #
    #     validator = CheckoutValidator(order)
    #     with pytest.raises(ValidationError):
    #         validator.validate()

    @patch(
        'checkout.validators.RegionRestrictionService.is_outside_of_tax_union',
        return_value=True,
    )
    def test_address_data_case_region_restrictions_outside_of_tax_union(self, _, order):  # noqa: PT019
        validator = CheckoutValidator(order)

        with pytest.raises(ValidationError) as e:
            validator._validate_address_data()

        assert 'Address outside of tax union' in str(e.value)

    @patch(
        'checkout.validators.RegionRestrictionService.is_outside_of_tax_union',
        return_value=False,
    )
    @patch(
        'checkout.validators.RegionRestrictionService.is_sofa_delivery_unavailable',
        return_value=True,
    )
    def test_address_data_case_region_restrictions_outside_of_sofa_delivery_area(
        self,
        _,  # noqa: PT019
        __,  # noqa: PT019
        order_factory,
        order_item_factory,
    ):
        order = order_factory(items=None)
        order_item_factory(is_sotty=True, order=order)
        validator = CheckoutValidator(order)

        with pytest.raises(ValidationError) as e:
            validator._validate_address_data()

        assert 'Address outside of sofa delivery area' in str(e.value)

    @parametrize_with_cases(
        ('order', 'settings_kwargs'), cases=InvalidProductsAndServicesLimitationsCases
    )
    def test_products_and_services_limitations_raise_exception(
        self, order, settings_kwargs
    ):
        validator = CheckoutValidator(order)

        with override_settings(**settings_kwargs):
            with pytest.raises(ValidationError):
                validator._validate_products_and_services_limitations()

    @parametrize_with_cases(
        ('order', 'settings_kwargs'), cases=ValidProductsAndServicesLimitationsCases
    )
    def test_products_and_services_limitations_validated(self, order, settings_kwargs):
        validator = CheckoutValidator(order)

        with override_settings(**settings_kwargs):
            validator._validate_products_and_services_limitations()
