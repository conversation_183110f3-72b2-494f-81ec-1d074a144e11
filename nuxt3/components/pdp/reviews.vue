<template>
  <div
    v-if="currentAverageScore"
    class="bg-beige-100 lg:py-96 py-48"
  >
    <div class="grid-container mb-48">
      <h3 class="mb-16 uppercase semibold-14 xl:semibold-18 md:mb-24">
        {{ t('pdp.reviews.subheading') }}
      </h3>
      <h2 class="semibold-28 md:semibold-32 lg:semibold-46 xl:semibold-54 max-w-[1080px]">
        {{ t('pdp.reviews.heading') }}
      </h2>
    </div>
    <section
      id="reviews-list"
      class="grid-container"
    >
      <div class="grid grid-cols-1 lg:grid-cols-12">
        <div class="col-span-12 lg:col-span-3">
          <aside class="flex flex-col items-center px-24 py-48 rounded-12 border border-neutral-500 lg:mt-56 lg:sticky lg:top-[200px] lg:left-0">
            <p
              class="semibold-72 lg:semibold-96 mb-24"
              data-testid="review-score"
              v-html="`${currentAverageScore.toFixed(1)}/5`"
            />
            <div
              class="flex rating-stars"
              data-testid="review-global-stars"
            >
              <IconStar
                v-for="number in 5"
                :key="number"
                data-testid="review-global-star"
                class="w-24 h-24 gap-4"
                :style="{
                  color: number <= currentAverageScore ? 'var(--active)' : 'var(--inactive)',
                  opacity: number <= currentAverageScore ? 1 : 0.38
                }"
              />
            </div>
            <p
              class="mt-12 normal-16 text-center"
              v-html="t('lp.reviews-list.globalscore', { percentage: scorePercentage, count: currentReviewsCount })"
            />
            <BaseLink
              class="inline-flex semibold-16 text-neutral-900 mt-24"
              data-testid="reviews-learn-more"
              variant="underlined"
              target="_blank"
              v-bind="{
                href: t('lp.reviews-list.globalscorehref'),
                trackData: {
                  eventType: 'NOEEC',
                  eventCategory: 'pdp_clicks',
                  eventAction: 'Reviews',
                  eventLabel: 'See all reviews'
                }
              }"
            >
              {{ t('lp.reviews-list.globalscorecta') }}
            </BaseLink>
          </aside>
        </div>
        <div class="col-span-12 lg:col-span-8 lg:col-start-5 lg-max:mt-48">
          <aside class="flex flex-row lg-max:flex-wrap items-center pb-16 lg:border-b border-neutral-500 gap-12">
            <ul
              class="flex flex-row flex-wrap gap-12"
              data-testid="review-filters"
            >
              <li
                v-for="filter in filters"
                :key="filter.name"
                data-testid="review-filter"
              >
                <BaseButton
                  class="rounded-full normal-16 px-16 py-8 h-48 border-neutral-500 border"
                  :variant="activeFilters.includes(filter.value) ? 'filled' : 'outlined'"
                  :class="!activeFilters.includes(filter.value) ? 'text-neutral-900' : ''"
                  v-on:click="toggleFilter(filter.value)"
                >
                  {{ t(filter.name) }}
                </BaseButton>
              </li>
            </ul>
          </aside>
          <section class="mt-32">
            <article
              v-for="(review, index) in reviews"
              :key="index"
              class="py-24 border-b border-neutral-400 last-of-type:border-b-0 flex lg:flex-row flex-col gap-32"
              data-testid="review-article"
            >
              <img
                v-if="review.photoMediumWebp"
                v-bind="{
                  src: review.photoMediumWebp,
                  alt: review.showingOriginal ? review.originalTitle : review.translatedTitle
                }"
                class="aspect-square w-full lg:max-w-[242px] lg:max-h-[242px] object-cover rounded-8 cursor-pointer lg-max:order-0"
                data-testid="review-article-img"
                v-on:click="handleImageZoom(review)"
              >
              <div class="w-full">
                <div
                  class="flex flex-wrap rating-stars lg-max:order-1"
                  data-testid="review-article-stars"
                >
                  <IconStar
                    v-for="number in 5"
                    :key="number"
                    data-testid="review-article-star"
                    class="w-16 h-16 gap-4"
                    :style="{
                      fill: number <= review.score ? 'var(--active)' : 'var(--inactive)',
                      gap: 4
                    }"
                  />
                  <span class="normal-16 ml-12">{{ review.name }}</span>
                  <NuxtTime
                    class="ml-auto text-neutral-700 normal-10 normal-14"
                    data-testid="review-article-date"
                    :datetime="new Date(review.createdAt)"
                    year="numeric"
                    month="long"
                    day="numeric"
                    :locale="localeCode"
                  />
                  <div class="w-full flex flex-row gap-8 text-neutral-700 normal-14 lg-max:flex-wrap lg-max:mt-4">
                    <span v-if="review.material">{{ t('lp.reviews-list.material') }} {{ review.material }}</span>
                    <span v-if="review.color">{{ t('lp.reviews-list.color') }} {{ review.color }}</span>
                  </div>
                </div>
                <h3
                  class="mt-16 semibold-24 lg-max:order-2"
                  data-testid="review-article-title"
                  v-html="review.showingOriginal ? review.originalTitle : review.translatedTitle"
                />
                <p
                  class="mt-8 normal-16 lg-max:order-3"
                  data-testid="review-article-location"
                  v-html="review.showingOriginal ? review.originalDescription : review.translatedDescription"
                />
                <p class="mt-16 normal-14 text-neutral-700 lg-max:order-4">
                  {{ t('lp.reviews-list.originalLanguage') }}:
                  <span class="capitalize">{{ getLanguageFromCountry(review.country) }}</span>
                  <span
                    v-if="review.country !== regionName"
                    class="underline cursor-pointer ml-8"
                    v-on:click="handleShowOriginal(review)"
                  >{{ review.showingOriginal ? t('lp.reviews-list.showTranslated') : t('lp.reviews-list.showOriginal') }}</span>
                </p>
              </div>
            </article>
            <BaseLink
              class="mt-32"
              variant="outlined"
              v-bind="{
                href: $addLocaleToPath('review-list'),
                trackData: {
                  eventType: 'NOEEC',
                  eventCategory: 'pdp_clicks',
                  eventAction: 'Reviews',
                  eventLabel: 'See all reviews'
                }
              }"
            >
              {{ t('common.view_all') }}
            </BaseLink>
          </section>
        </div>
      </div>
    </section>

    <LazyBaseModal
      v-if="isDrawerOpen && activeDrawer && activeDrawer.properPhoto"
      v-model="isDrawerOpen"
      wrapper-class="flex items-center justify-center"
    >
      <template #default>
        <div class="flex flex-row items-center w-max h-max justify-center relative">
          <img
            class="rounded-24 max-w-[80vw] max-h-[80vh] object-contain"
            :src="activeDrawer?.properPhoto"
            :alt="activeDrawer?.title"
          >
          <BaseButton
            class="block z-2 ml-auto top-16 right-16 absolute lg:top-24 lg:right-24"
            data-testid="drawer-close-button"
            v-bind="{
              variant: 'close',
              trackData: {}
            }"
            v-on:click="isDrawerOpen = false"
          >
            <IconPlus class="rotate-45" />
          </BaseButton>
        </div>
      </template>
    </LazyBaseModal>
  </div>
</template>

<script setup lang="ts">
import { PDP_REVIEWS, CATEGORY_SCORE, REVIEWS } from '~/api/reviews';
import { getLanguageFromCountry } from '~/utils/countryToLanguage';
import IconStar from '~/assets/icons/star.svg';
import IconPlus from '~/assets/icons/plus.svg';
import { positiveFeedbackPercentage } from '~/composables/useReviews';

const { t } = useI18n();

const { locale, localeCode } = useLocale();
const { reviewsAverageScore, reviewsCount, regionName } = useGlobal();

const props = defineProps<{
  category: string,
  shelfType: number,
  material: number,
  allProducts?: boolean
}>();

interface item {
  name: string,
  title: string,
  translatedTitle: string,
  translatedDescription: string,
  description: string,
  originalTitle?: string,
  originalDescription?: string,
  showingOriginal?: boolean,
  score: number,
  createdAt: string,
  country: string,
  properPhoto: string,
  photoSmallWebp: string,
  photoMediumWebp: string,
  productLine: string,
  category: string,
  productName: string,
  material: string,
  color: string,
  tags: string[]
}

const activeFilters = ref<string[]>([]);
const isDrawerOpen = ref(false);
const reviews = ref<item[]>([]);
const activeDrawer = ref<item | null>(null);
const currentReviewsCount = ref(reviewsCount);
const currentAverageScore = ref(reviewsAverageScore);

const [response, categoryScore] = await Promise.all([
  props.allProducts ? REVIEWS(locale.value, 1, undefined, undefined, undefined, true) : PDP_REVIEWS(props.category, props.shelfType, props.material, true),
  CATEGORY_SCORE(props.shelfType === 10 ? 'sofa' : props.category)
]);

const initialResults = props.allProducts ? response.data.value?.results.slice(0, 3) ?? [] : response.data.value ?? [];

currentReviewsCount.value = categoryScore.data.value?.count;
currentAverageScore.value = categoryScore.data.value?.averageScore;

const scorePercentage = computed(() => positiveFeedbackPercentage(currentReviewsCount.value, currentAverageScore.value));

reviews.value = initialResults.map((review: any) => ({
  ...review,
  originalTitle: review.title,
  originalDescription: review.description,
  showingOriginal: false
})) || [];

const allFilters = [
  { name: 'Assembly', value: 'assembly' },
  { name: 'Comfort', value: 'comfort' },
  { name: 'Customer Support', value: 'customer_support' },
  { name: 'Delivery', value: 'delivery' },
  { name: 'Fabric', value: 'fabric' },
  { name: 'Style', value: 'style' },
  { name: 'Quality', value: 'quality' }
];

const filters = allFilters.filter(filter => categoryScore.data.value?.activeTags?.includes(filter.value));

const handleImageZoom = (review: item) => {
  activeDrawer.value = review;
  isDrawerOpen.value = true;
};

const handleShowOriginal = (review: item) => {
  review.showingOriginal = !review.showingOriginal;
};

const toggleFilter = (filterValue: string) => {
  const index = activeFilters.value.indexOf(filterValue);

  if (index > -1) {
    activeFilters.value.splice(index, 1);
  } else {
    activeFilters.value.push(filterValue);
  }
};

watch(activeFilters, async (filters) => {
  const tags = filters.length > 0 ? filters : undefined;

  const filteredResponse = props.allProducts ? await REVIEWS(locale.value, 1, undefined, undefined, tags, true) : await PDP_REVIEWS(props.category, props.shelfType, props.material, true, tags);
  const filteredResults = props.allProducts ? filteredResponse.data.value?.results.slice(0, 3) ?? [] : filteredResponse.data.value ?? [];

  reviews.value = filteredResults.map((review: any) => ({
    ...review,
    originalTitle: review.title,
    originalDescription: review.description,
    showingOriginal: false
  })) || [];
}, { deep: true });
</script>

<style lang="scss">
.rating-stars {
  --active: #1D1E1F;
  --inactive: #6F7173;
}
</style>
