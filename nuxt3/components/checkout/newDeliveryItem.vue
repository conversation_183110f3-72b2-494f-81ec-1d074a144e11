<template>
  <div
    class="assembly-item mb-16"
    data-testid="checkout-assembly-item"
    :class="{ active }"
    v-on:click="() => !active && $emit('handleDeliveryItem', serviceName)"
  >
    <div class="flex items-center justify-center gap-x-16">
      <div class="w-full">
        <p
          v-if="badge"
          class="bg-neutral-900 text-white inline px-8 py-2 rounded-4 normal-12"
        >
          {{ badge }}
        </p>
        <p class="mt-8 semibold-16 text-neutral-900 flex items-center gap-4">
          {{ $t(copy[serviceName === 'home_delivery' ? (pricePromo === 0 ? 'home_delivery_free' : 'home_delivery') : serviceName].title) }}
        </p>
        <p class="mt-2 text-neutral-700 normal-12">
          {{ $t(copy[serviceName === 'home_delivery' ? (pricePromo === 0 ? 'home_delivery_free' : 'home_delivery') : serviceName].content) }}
        </p>
      </div>
      <p
        v-if="price === pricePromo"
        class="normal-16 text-neutral-900"
        data-testid="checkout-assembly-price"
      >
        {{ format(price) }}
      </p>
      <div
        v-else
        class="flex"
      >
        <p
          class="normal-16 text-neutral-700 mr-4 relative"
          data-testid="checkout-assembly-price"
        >
          {{ format(price) }}
          <span
            class="absolute left-0 right-0 top-1/2 h-[1px] bg-neutral-700"
            style="content: ''; transform: translateY(-50%);"
          />
        </p>
        <p
          class="normal-16 text-orange"
          data-testid="checkout-assembly-promo-price"
        >
          {{ format(pricePromo) }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { format } = usePrice();

const emit = defineEmits<{
  'handleDeliveryItem': [value: string]
}>();

defineProps({
  badge: {
    type: String,
    default: ''
  },
  active: {
    type: Boolean,
    required: true
  },
  serviceName: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  pricePromo: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    required: true
  }
});

const copy = {
  home_delivery: {
    title: 'checkout.delivery.home_headline',
    content: 'checkout_delivery_doorstep_title'
  },
  home_delivery_free: {
    title: 'checkout.delivery.home_headline',
    content: 'checkout.free.delivery_subheadline'
  },
  doorstep_delivery: {
    title: 'checkout.delivery.Curbside_headline',
    content: 'checkout_delivery_room_curbside'
  },
  assembly: {
    title: 'checkout.delivery.assembly_headline',
    content: 'checkout.delivery.assembly_info'
  },
  white_gloves_delivery: {
    title: 'checkout_delivery_white_gloves_title',
    content: 'checkout_delivery_white_gloves_content'
  },
  old_sofa_collection: {
    title: 'checkout_old_sofa_collection_title',
    content: 'checkout_old_sofa_collection_content'
  }

};
</script>

<style lang="scss" scoped>
    .assembly-item {
        @apply cursor-pointer rounded-8 border border-neutral-500 px-16 py-24;
        &.active {
            @apply border-neutral-900 bg-transparent relative overflow-hidden;
            &::before {
                    @apply absolute right-0 bottom-0 w-20 h-20 bg-neutral-900 bg-no-repeat rounded-tl-6;
                    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="1447.4 687.6 17.6 13.4"><g id="group" transform="translate(1421 687)"><path id="path" fill="%23FFFFFF" class="cls-1" d="M9,16.2,4.8,12,3.4,13.4,9,19,21,7,19.6,5.6Z" transform="translate(23 -5)"/></g></svg>');
                    background-size: 8px 8px;
                    background-position: 6px 6px;
                    content: '';
                }
        }
    }
</style>
