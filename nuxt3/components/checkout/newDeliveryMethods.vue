<template>
  <div>
    <p
      v-if="deliveries && deliveries.length === 2"
      class="text-neutral-900 normal-16 mb-32"
    >
      {{ $t('checkout.new_delivery_subheading') }}
    </p>

    <div
      v-for="(delivery, index) in deliveries"
      :key="`delivery-${index}`"
    >
      <p
        v-if="deliveries.length > 1"
        class="flex justify-start items-center text-neutral-900 semibold-18 mb-4"
      >
        <span><IconVan /></span>
        <span class="ml-8">{{ $t('checkout.new_delivery_delivery_item', { item: index + 1, all: deliveries.length }) }}</span>
      </p>
      <p class="text-[#107D2A] normal-16 mb-32">
        {{ delivery.estimatedDeliveryTime }}
      </p>
      <div
        class="flex justify-start flex-wrap items-center gap-16 mb-16"
      >
        <div
          v-for="(option, oIndex) in cartItems.filter(item => delivery?.itemIds.includes(item.id))"
          :key="`option-${oIndex}`"
          class="min-w-[96px] min-h-[96px] w-[96px] h-[96px] overflow-hidden"
        >
          <img
            class="w-full"
            :src="option.itemImageUrl"
            :alt="getAlt(option)"
            loading="lazy"
          >
        </div>
      </div>
      <div class="mb-48">
        <template
          v-for="(service, iIndex) in reversedServices(delivery.availableServices)"
          :key="`service-${iIndex}`"
        >
          <CheckoutNewDeliveryItem
            v-if="service.serviceName !== 'old_sofa_collection'"
            :badge="service.serviceName === 'white_gloves_delivery' ? $t('checkout.recommended_label') : ''"
            :service-name="service.serviceName"
            :price="service.price"
            :price-promo="service.promoPrice"
            :active="service.active"
            v-on:handle-delivery-item="(serviceName) => handleDelivery(serviceName)"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useScartStore } from '~/stores/scart';
import { CART_ITEM_NAME } from '~/composables/useCart';
import useSofaCartInfo from '~/composables/useSofaCartInfo';

const { cartItems } = storeToRefs(useScartStore());
const { deliveries } = storeToRefs(useCheckoutStore());

const { getItemName } = CART_ITEM_NAME();
const { fetchUserStatus, fetchDeliveries } = useCartStatus();
const { handleWhiteGlovesDelivery, handleAssembly } = useCart();

const handleDelivery = async (serviceName: string) => {
  if (serviceName === 'white_gloves_delivery') {
    await handleWhiteGlovesDelivery(true);
  } else if (serviceName === 'home_delivery' || serviceName === 'doorstep_delivery') {
    await handleWhiteGlovesDelivery(false);
  } else if (serviceName === 'assembly') {
    await handleAssembly(true);
  }

  await fetchUserStatus();
  await fetchDeliveries();
};

function getAlt (cartItem: any) {
  if (cartItem.itemFurnitureType === 'sample_box') {
    return cartItem.itemDescriptionMaterial;
  } else if (cartItem.content_type === 'sotty') {
    const {
      sofaItemName
    } = useSofaCartInfo(cartItem.material, cartItem.itemId);
    return sofaItemName;
  } else if (cartItem.content_type === 'skuvariant') {
    return cartItem.color?.translated;
  } else {
    return getItemName(cartItem.itemFurnitureType, cartItem.pattern_name, cartItem.category, cartItem.shelf_type);
  }
}

function reversedServices (services: Array<any>) {
  return typeof services.toReversed === 'function'
    ? services.toReversed()
    : [...services].reverse();
}

watch(cartItems, async () => {
  await fetchDeliveries();
});
</script>
