<template>
  <div>
    <p
      v-if="errorMessage"
      v-html="$t('checkout.payment_methods.technical_error')"
    />
    <p
      v-if="isCheckoutLoading"
      v-html="$t('checkout.payment_methods.please_wait')"
    />
    <div
      v-show="!errorMessage"
      id="dropin-container"
    />
    <ModalPaymentFailed
      v-model="isPaymentFailed"
    />
  </div>
</template>

<script setup lang="ts">

import '@adyen/adyen-web/styles/adyen.css';
import { debounce } from 'lodash-es';
import { useToast } from 'vue-toastification';
import useAdyenDropin from '~/composables/useAdyenDropin';

import {
  PAYMENT_SESSION
} from '~/api/checkout';

const global = useGlobal();

const { $i18n, $logException } = useNuxtApp();
const toast = useToast();

const props = defineProps({
  isBusinessClient: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    required: true
  }
});

const {
  initializeAdyen,
  errorTimeout,
  errorMessage,
  isCheckoutLoading,
  isPaymentFailed,
  disablePayments,
  handleUpdateAdyen
} = useAdyenDropin();

const session = async () => {
  if (totalPrice.value === 0) {
    disablePayments.value = true;
    return;
  }

  const { error, data } = await PAYMENT_SESSION(global);

  if (error.value) {
    toast.error($i18n.t('checkout.payment_methods.payment_failure'));
    $logException(error.value);
  } else if (data.value) {
    initializeAdyen(data.value, props.isBusinessClient, props.formData);
    onBeforeUnmount(() => {
      if (errorTimeout) {
        clearTimeout(errorTimeout);
      }
    });
  }
};

onMounted(async () => {
  await session();
});

const cartStore = useScartStore();
const { regionName, currencyCode } = storeToRefs(global);
const { totalPrice } = storeToRefs(cartStore);

const valuesToWatch = computed(() => [totalPrice.value, regionName.value, currencyCode.value]);

const debouncedWatchCallback = debounce(
  async ([newValue1, newValue2, newValue3], [oldValue1, oldValue2, oldValue3]) => {
    if (newValue1 !== oldValue1 || newValue2 !== oldValue2 || newValue3 !== oldValue3) {
      handleUpdateAdyen(props.formData);
    }
  },
  300 // debounce delay in milliseconds
);

const unwatch = watch(valuesToWatch, debouncedWatchCallback);
onUnmounted(() => {
  unwatch();
});

</script>

<style lang="scss">

#dropin-container {
    .adyen-checkout-contextual-text {
         @apply text-neutral-900 normal-12;
    }
   .adyen-checkout__paypal {
        display: none !important;
   }
    .adyen-checkout__card__brands {
        @apply mt-8;
    }
  .adyen-checkout__threeds2__challenge {
    @apply flex justify-center items-center;
    @apply fixed top-0 left-0 w-full h-full z-5;
    @apply bg-black/40;

    .adyen-checkout__iframe{
      @apply bg-white rounded-24 static p-24 min-h-[500px];
    }
  }

    .adyen-checkout__payment-method__image__wrapper--outline {
        @apply mr-16;
        &:after {
            @apply border-transparent;
        }

    }

    .adyen-checkout__payment-method__header__title {
        @apply h-[22px];
    }
    .adyen-checkout-form-instruction {
        @apply my-16 normal-14 text-black;
    }

    .adyen-checkout {
        &__card__form {
            .adyen-checkout__payment-method__name {
                @apply semibold-16 text-neutral-900;
            }

            .adyen-checkout__input {
                @apply border-transparent h-[22px] p-0 mt-32 pl-[14px];
                box-shadow: none;

            }

            .adyen-checkout__error-text {
                @apply text-error-500 normal-12 mt-8;
            }

            .adyen-checkout__input-wrapper {
                box-shadow: none;

                &:hover {
                    @apply border-neutral-900;
                }

                @apply h-64;
                &:has(.adyen-checkout__input--focus) {
                    @apply shadow-[0_0_0_3px_#EBEEF0] border-neutral-900;
                }

                &:has(.adyen-checkout__input--error) {
                    @apply border-2 border-error-500;
                    box-shadow: none;
                }

                @apply border border-neutral-500 rounded-8

            }

            .adyen-checkout__input, .adyen-checkout__input--focus, .adyen-checkout__input--focus:hover {
                @apply border-transparent;
                box-shadow: none;
            }

            .adyen-checkout__label {
                &__text {
                    @apply text-neutral-700 normal-12;
                }

                @apply z-1 absolute top-12 left-16 flex justify-start items-center;
            }

            .adyen-checkout__field {
                @apply relative mb-8;
                box-shadow: none;

                input {
                    background-color: red;
                    font-size: 12px;
                }
            }
        }

        &__payment-method {
            @apply rounded-8 border-neutral-500 max-h-none;
            &__radio {
                @apply hidden;
            }

            &__header {
                @apply pl-16 py-24;
            }

            &--selected {
                @apply border-neutral-900 bg-transparent relative;
                &::before {
                    @apply absolute right-0 bottom-0 w-20 h-20 bg-neutral-900 bg-no-repeat rounded-tl-6 rounded-br-6;
                    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="1447.4 687.6 17.6 13.4"><g id="group" transform="translate(1421 687)"><path id="path" fill="%23FFFFFF" class="cls-1" d="M9,16.2,4.8,12,3.4,13.4,9,19,21,7,19.6,5.6Z" transform="translate(23 -5)"/></g></svg>');
                    background-size: 8px 8px;
                    background-position: 6px 6px;
                    content: '';
                }
            }
        }
    }
}
</style>
