<template>
  <ModalBasic
    v-bind:model-value="modelValue"
    v-bind:default-close="defaultClose"
    v-bind:static="true"
    class="w-[90%]"
    modal-classes="max-w-[720px]"
    v-on:update:model-value="$emit('update:modelValue', $event);"
  >
    <template
      v-if="checkoutStore.regionToSet"
      #default
    >
      <div class="min-h-[400px] py-96 px-32 md:px-80 text-center">
        <h2
          class="text-offblack-800 bold-32 mb-32"
          v-html="$t('checkout.modal_switching_stores.heading')"
        />
        <p
          class="text-grey-900 normal-16 mb-40"
          v-html="$t(`checkout.modal_switching_stores.${checkoutStore.regionToSet?.value}`)"
        />
        <BaseButton
          variant="accent"
          class="w-min whitespace-nowrap"
          v-bind="{ trackData: {} }"
          v-on="{ click: () => $emit('on-success') }"
          v-html="$t('checkout.modal_switching_stores.button')"
        />
      </div>
    </template>
  </ModalBasic>
</template>

<script setup lang="ts">
const checkoutStore = useCheckoutStore();

defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  defaultClose: {
    type: Boolean,
    default: false
  }
});

defineEmits<{
  'update:modelValue': [value: false],
  'on-success': any
}>();

</script>
