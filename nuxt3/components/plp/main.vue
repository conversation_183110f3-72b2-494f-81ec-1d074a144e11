<template>
  <section class="min-h-screen bg-beige-100">
    <LazyPlpNavigation
      id="plp-navigation-modern"
      hydrate-on-idle
      data-section="section-plp-navigation"
      class="mt-16"
    />

    <div
      id="plp-category-description"
      class="grid-container"
    >
      <BaseLink
        v-if="FF_BLACKFRIDAY"
        data-section="blackfriday-promo-bar"
        class="relative block h-[150px] bg-[#C4C4C4] rounded-[16px] overflow-hidden my-16 md:my-32"
        variant="custom"
        :href="$addLocaleToURL('/blackfriday')"
        v-bind="{
          trackData: {
            eventLabel: 'blackfriday_banner_click',
          }
        }"
      >
        <BasePicture
          disable-lazy
          class="w-full h-full"
          img-classes="block h-full w-full object-cover object-center"
          v-bind="{
            path: 'library/jumbo/',
            alt: $t('common.error.title'),
            type: 'M T SD LD'
          }"
        />
        <div
          class="ty-btn-filled ty-btn-filled--dark position absolute md-max:left-1/2 md-max:bottom-28 md-max:-translate-x-1/2
                  md:top-1/2 md:right-56 md:-translate-y-1/2"
          v-text="$t('plp.banner.blackfriday.cta')"
        />
      </BaseLink>
      <div
        v-else-if="promoBanner"
        class="mt-32 border-2 rounded-24 aspect-[3/1] md:aspect-[7/1] flex items-center justify-center"
        :style="{
          backgroundColor: bannerBackgroundColor,
          color: bannerTextColor,
          borderColor: bannerBorderColor
        }"
      >
        <p
          class="text-center bold-46 md:bold-54 xl:bold-96"
          v-html="$t('plp.banner.winterSale.title')"
        />
      </div>
      <LazyPlpCategoryBanner
        v-else-if="!promoBanner && categoryBanner"
        class="mt-16"
        v-bind="{
          imageAlt: categoryBanner.heading,
          title: categoryBanner.heading,
          ctaCopy: categoryBanner.ctaCopy,
          ctaUrl: categoryBanner.ctaUrl,
          promoCode: categoryBanner.promoCode,
          isThemeLight: categoryBanner.isThemeLight,
          imagePathMobile: categoryBanner.bannerImageMobile,
          imagePathTablet: categoryBanner.bannerImageTablet,
          imagePathDesktopSD: categoryBanner.bannerImageDesktopSD,
          imagePathDesktopLD: categoryBanner.bannerImageDesktopLD,
          imagePathDesktopXLD: categoryBanner.bannerImageDesktopXLD,
          'data-section': `category-banner-${activeCategory?.name}`
        }"
      />
    </div>

    <PlpFiltersPillBar
      v-if="!activeCategory?.areFiltersBarDisabled"
      id="plp-filter-pills"
      data-section="plp-filters-bar"
      class="md-max:hidden"
    />

    <BaseDrawer
      v-bind="{
        additionalOverlayClasses: '!bg-opacity-60',
        additionalDialogPanelClasses: '!max-w-[330px] !bg-white text-offblack-600',
        enterFrom: 'translate-x-[100%]',
        enterTo: 'translate-x-0',
        leaveFrom: 'translate-x-0',
        leaveTo: 'translate-x-[100%]'
      }"
      v-model="isDrawerOpened"
    >
      <LazyPlpFiltersDrawer :hydrate-when="isDrawerOpened" />
    </BaseDrawer>

    <div class="grid-container">
      <p class="normal-16 text-neutral-900 text-right col-span-12">
        {{ resultsCount }} {{ $t('common.products') }}
      </p>
    </div>

    <div
      id="plp"
      class="md-max:px-0 grid-container mt-12"
    >
      <template v-if="productsList?.length > 0">
        <LazyPlpGridBoard
          hydrate-on-idle
          data-section="grid-v2-board"
          :class="{ 'plp-loading': isFetching }"
        />
      </template>

      <section
        v-else-if="productsList?.length === 0"
        class="my-40 text-center grid-container"
      >
        <IconNoResults class="inline-block mb-16" />
        <h2
          class="mb-4 normal-20 xl:normal-28 text-neutral-900 xl:mb-8"
          v-html="$t('plp.no_results.title')"
        />
        <p
          class="normal-16 text-neutral-900 mb-32 max-w-[360px] mx-auto"
          v-html="$t('plp.no_results.body')"
        />
        <BaseLink
          variant="accent"
          class="mb-4 whitespace-nowrap"
          v-bind="{
            trackData: {},
            href: activeCategory && getLinkToGenericFurniture(activeCategory.name)
          }"
        >
          {{ $t('plp.no_results.button') }}
        </BaseLink>
      </section>
    </div>

    <span ref="endOfGridObserverEl" />

    <div
      v-if="productsList"
      class="flex flex-col items-center justify-center mt-32"
    >
      <template v-if="productsList.length < resultsCount">
        <BaseButton
          variant="filters-pill"
          class="mb-4 whitespace-nowrap"
          data-testid="plp-show-more-items-button"
          v-bind="{
            trackData: {}
          }"
          v-on="{
            click: showMoreItems,
          }"
        >
          {{ $t('plp.board.button.more') }}
        </BaseButton>
        <p
          class="mb-32 normal-12 text-grey-900"
          data-testid="plp-pagination-counter-bottom"
        >
          {{ $t('plp.board.current_view_1') }} {{ productsList?.length }} {{ $t('plp.board.current_view_2') }}
          {{ resultsCount }}
        </p>
      </template>
    </div>

    <LazySectionMinigridRecommedation
      campaign-id="yP14ZlvVh8X3"
      :title="$t('plp.recommendation.title')"
      hydrate-on-visible
      data-observe-view="section-recommendation"
      data-section="section-recommendation"
      data-testid="section-recommendation"
      class="bg-beige-100"
    />

    <LazyNeedSomeHelp />
    <LazySectionTextCenteredBackground
      v-if="activeCategory?.name === FurnitureCategory.WARDROBES"
      hydrate-on-visible
      data-section="customise-section"
      v-bind="{
        title: $t('plp.search-banner.wardrobes.headline'),
        imagePath: 'plp/banner',
        imageType: 'M T SD LD XLD',
        cta: $t('plp.search-banner.wardrobes.cta'),
        withIcon: false,
        isLightTheme: false,
        ctaUrl: activeCategory && getLinkToGenericFurniture(activeCategory.name)
      }"
    />
    <LazySectionTextCenteredBackground
      v-else
      hydrate-on-visible
      data-section="customise-section"
      v-bind="{
        title: $t('common.ui.customize1'),
        secondaryTitle: $t('common.ui.customize2'),
        imagePath: 'lp/type02sideboard/customise',
        cta: $t('lp.type02sideboard.customise.cta'),
        ctaUrl: activeCategory && getLinkToGenericFurniture(activeCategory.name)
      }"
    />

    <LazyPlpSectionSeo
      v-if="!isWebView && seoHtmlDescription"
      hydrate-on-visible
      v-bind="{
        body: seoHtmlDescription
      }"
    />

    <ClientOnly>
      <Transition name="fade">
        <div
          v-show="isScrollToTopButtonVisible"
          class="fixed z-2 cursor-pointer right-16 md:right-40 lg:right-64 xl:right-[112px] flex justify-center items-center
            w-40 h-40 rounded-full bg-white border border-solid border-grey-700 text-offblack-600"
          :class="isDixaVisible ? 'lg-max:hidden' : 'bottom-96 lg:bottom-24'"
          v-on:click="handleScrollToTopClick"
        >
          <IconCaretUp />
        </div>
      </Transition>
    </ClientOnly>
    <ClientOnly>
      <LazyPlpFiltersNotificationPill
        v-if="!activeCategory?.areFiltersBarDisabled"
        hydrate-on-idle
      />
    </ClientOnly>
  </section>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { isEmpty } from 'lodash-es';
import { FurnitureCategory } from '~/utils/filters';
import useShowMoreProducts from '~/composables/plp/useShowMoreProducts';
import useScrollToTopButton from '~/composables/plp/useScrollToTopButton';
import useGenericFurnitureLink from '~/composables/plp/useGenericFurnitureLink';
import { useGlobal, useI18n } from '#imports';

const route = useRoute();

const { locale } = useLocale();
const isDixaVisible = useState('isDixaEnabled', () => false);
const global = useGlobal();
const { FF_BLACKFRIDAY } = storeToRefs(global);
const { extraData } = global;

const { getLinkToGenericFurniture } = useGenericFurnitureLink();

const { isScrollToTopButtonVisible, handleScrollToTopClick, scrollToProductList } = useScrollToTopButton();
const plpStore = usePlpStore();
const { productsList, promoData, activeCategory, resultsCount, isDrawerOpened, catalogQuery, sanityQuery, nextCatalogPageLink } = storeToRefs(plpStore);

const isWebView = global.device.isWebView;

const promoBanner = computed(() => extraData?.plpBanner);
const seoHtmlDescription = computed(() => sanityData?.value?.filterSeoData?.htmlValue || sanityData?.value?.seoSectionBody || '');
const categoryBanner = computed(() => sanityData?.value?.customUsps?.filter(item => item?.type?.type === 'categoryBanner')[0]?.type);

let trackingList = '';

const [{ data: sanityData, error: sanityError }, { data: catalogData, status, error: catalogError }] = await Promise.all([
  useSanityQuery<ISanityData>(sanityQuery.value),
  useFetch('/nuxt-api/catalog', {
    key: 'plp-catalog',
    watch: [catalogQuery],
    query: catalogQuery,
    headers: {
      referer: useRequestURL().href
    },
    onResponse: (response: any) => {
      trackingList = response.request.toString().split('?')[1];
    },
    transform: (res) => {
      res.results.forEach((product) => {
        // convert shelfType to number
        product.shelfType = +product.shelfType;
        product.trackingList = trackingList;
      });

      return res;
    }
  })
]);

const isFetching = computed(() => status.value === 'pending');
const toast = useToast();
const { $logException } = useNuxtApp();
const $i18n = useI18n();
const { endOfGridObserverEl, showMoreItems } = useShowMoreProducts();
watch(catalogData, () => {
  if (catalogData.value) {
    promoData.value = catalogData.value?.promo || null;
    productsList.value = catalogData.value?.results || [];
    nextCatalogPageLink.value = catalogData.value?.next || null;
    resultsCount.value = catalogData.value?.count || 0;
  }
}, { immediate: true });
watch(sanityData, () => {
  if (sanityData.value) {
    plpStore.sanityData = sanityData.value;
  }
}, { immediate: true });

watch([sanityError, catalogError], () => {
  if (sanityError.value || catalogError.value) {
    toast.error($i18n.t('common.error.connection'));
    $logException((sanityError.value || catalogError.value) as Error);
    plpStore.RESET_FILTERS_TO_DEFAULT();
  }
}, { immediate: true });

watch(() => route.query, () => {
  if (!isEmpty(route.query)) { scrollToProductList(); }
});

const setI18nParams = useSetI18nParams();

watch(() => route.fullPath, () => {
  setI18nParams(plpStore.localeRouteParams);
}, { immediate: true });

useSeoMeta({
  title: () => sanityData?.value?.filterSeoData?.metaTitle || sanityData.value?.seoTitle || '',
  ogTitle: () => sanityData?.value?.filterSeoData?.metaTitle || sanityData.value?.seoTitle || '',
  description: () => sanityData?.value?.filterSeoData?.metaDescription || sanityData.value?.seoDescription || '',
  ogDescription: () => sanityData?.value?.filterSeoData?.metaDescription || sanityData.value?.seoDescription || '',
  ogLocale: () => locale.value,
  ogUrl: () => route.fullPath
});
</script>

<style lang="scss">

</style>
