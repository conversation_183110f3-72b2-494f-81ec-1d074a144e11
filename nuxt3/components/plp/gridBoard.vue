<script setup lang="ts">
import { pick } from 'lodash-es';
import { vIntersectionObserver } from '@vueuse/components';
import { usePlpAnalytics } from '~/composables/plp/usePlpAnalytics';
import useUSP from '~/composables/plp/useUSP';
import useProductImpressions from '~/composables/plp/useProductImpressions';
import { useGlobal } from '~/stores/global';

const {
  productsList, categoriesInPromotion, activeCategory,
  sanityData, promoData, filteredColors, filteredMaterials, filteredProductLines
} = storeToRefs(usePlpStore());

const gtm = useGtm();
const { reviewsAverageScore, reviewsCount } = useGlobal();
const { GET_SHELF_URL } = useProductUrl();
const { selectItemGA4Event } = usePlpAnalytics();
const { onIntersectionObserver } = useProductImpressions();
const { categories: allCategories } = useCategories();
const { getColorSwatchesForProductCategory } = useColors();

const normalizedItems = computed(() => productsList.value?.map((item) => {
  const shelfType = GET_SHELF_TYPE(item.shelfType);

  const availableMaterials = getColorSwatchesForProductCategory(shelfType, allCategories[item.category]);
  let url = '';

  try {
    url = GET_SHELF_URL(
      item.configuratorType,
      item.category,
      item.id,
      item.slug,
      '',
      item
    );
  } catch (e) {
    console.error('Error generating URL for product', item, e);
  }

  return {
    ...(pick(item, [
      'id',
      'labels',
      'width',
      'height',
      'depth',
      'shelfType'
    ])),
    productLine: item?.productLine,
    productUnrealImage: item.productUnrealImage,
    preview: item.productUnrealImage,
    previewInstagrid: item.previewInstagrid || item.lifestyleUnrealImage,
    analyticsData: {
      ...item
    },
    productName: item.seoSlug,
    availableMaterials,
    url,
    availableColors: item.availableColors
  };
}));

const {
  productLinesUsp,
  freeDeliveryUsp,
  sampleUsp,
  promoUsp,
  assemblyUsp,
  freeReturnsUsp,
  shopTheLookUsp,
  shopByRoomUsp,
  halfImageBreaks,
  fullImageBreaks,
  solidColorBreaks,
  productsDouble,
  uspList,
  ratingUsp,
  isDesktopViewport
} = useUSP();

const productsCount = computed(() => {
  const items = [...normalizedItems.value];

  uspList.value.forEach((usp) => {
    usp.lp < items.length && items.splice(usp.lp - 1, 0, usp);
  });

  return items.reduce((acc, item) => {
    if (item.size) {
      acc += item.size;
    } else {
      acc++;
    }

    return acc;
  }, 0);
});

const isVisible = (mobileRow, mobileCol, desktopRow, desktopCol) => (isDesktopViewport.value
  ? ((desktopRow * 4) + desktopCol) <= productsCount.value
  : ((mobileRow * 2) + mobileCol) <= productsCount.value);

const selectItemGA4 = (data) => {
  gtm?.push({ ecommerce: null });
  gtm?.push(selectItemGA4Event({ ...data, itemListName: 'grid' }));
};

const isVeneerUspVisible = computed(() => filteredMaterials.value?.includes(OptionsMaterials.WOODEN));
const isWoodenUspVisible = computed(() => filteredColors.value?.includes(OptionsColor.WOODEN));
const isExpressionsUspVisible = computed(() =>
  (filteredProductLines.value.includes('6') ||
  filteredProductLines.value.includes('7') ||
  filteredProductLines.value.includes('8')) &&
  (activeCategory.value.name === allCategories.all.name ||
    activeCategory.value.name === allCategories.sideboard.name ||
    activeCategory.value.name === allCategories.chest.name ||
    activeCategory.value.name === allCategories.bedsidetable.name ||
    activeCategory.value.name === allCategories.tvstand.name
  ));

</script>

<template>
  <ul class="grid grid-cols-2 gap-8 md:gap-16 lg:grid-cols-4">
    <li
      v-for="({ analyticsData, id, ...rest }, index) in normalizedItems"
      :key="`product-tile-${id}-${index}`"
      data-testid="product-card"
    >
      <PlpProductCard
        v-intersection-observer="[onIntersectionObserver, { threshold: 0.2 }]"
        v-bind="{
          trackData: {},
          ...rest,
          item: analyticsData,
          index,
          lazyLoading: index > 3
        }"
        v-on:select-item-g-a4-event="selectItemGA4"
      />
    </li>

    <!-- Veneer 2024 -->
    <template v-if="isVeneerUspVisible || isWoodenUspVisible">
      <li class="relative w-full h-full bg-[#BFB8AA] grid-row-start-2 grid-col-start-1 lg:grid-row-start-2 lg:grid-col-start-3">
        <BasePicture
          class="w-full h-full absolute inset-0"
          img-classes="w-full h-full"
          v-bind="{
            alt: isWoodenUspVisible ? $t('grid.usp.veneer24_color.title') : $t('grid.usp.veneer24.title'),
            path: 'plp/breaks/veneer24',
            type: 'M T SD LD XLD'
          }"
        />
        <h2
          class="relative z-1 bold-24 xl:bold-32 text-white px-8 py-28 md:px-16 lg:px-28 lg:py-48 h-full"
          v-html="isWoodenUspVisible ? $t('grid.usp.veneer24_color.title') : $t('grid.usp.veneer24.title')"
        />
      </li>
    </template>

    <!-- Expressions 2024 -->
    <template v-if="isExpressionsUspVisible">
      <li class="relative w-full h-full bg-[#BFB8AA] grid-row-start-2 grid-col-start-1 lg:grid-row-start-2 lg:grid-col-start-3">
        <BasePicture
          class="w-full h-full absolute inset-0"
          img-classes="w-full h-full"
          v-bind="{
            alt: $t('grid.usp.veneer24_color.title'),
            path: 'plp/breaks/expressions2024',
            type: 'M T SD LD XLD'
          }"
        />
        <div class="p-8 md:pt-48 md:p-16 text-offwhite-800 relative z-1">
          <h2
            class="bold-20 xl:bold-24 mb-4 md:mb-8"
            :class="['text-offwhite-800']"
            v-html="$t('grid.usp.expressions.title')"
          />
          <p
            class="normal-14 xl:normal-16 mb-8 md:mb-16"
            v-html="$t('grid.usp.expressions.description')"
          />
        </div>
      </li>
    </template>

    <!-- CUSTOM BREAKS FROM SANITY -->
    <!-- Product (double) - Sanity -->
    <template v-if="productsDouble">
      <PlpBreakProduct
        v-for="(item, index) in productsDouble"
        :key="`${index}-usp-double-product`"
        v-bind="{
          positionMobile: { row: item.mobileRow, column: item.mobileColumn },
          positionDesktop: { row: item.desktopRow, column: item.desktopColumn },
          item: {
            shelfType: item.type.shelfType,
            category: item.type.productCategory,
          },
          productName: item.type.productName,
          productURL: item.type.productUrl,
          shelfType: item.type.shelfType,
          image: item.type.image,
          instagridImage: item.type.instagridImage,
          label: item.type.label,
          productCategory: item.type.productCategory,
          width: +item.type.width,
          height: +item.type.height,
          productsCount,
          categoriesInPromotion,
          index
        }"
      />
    </template>

    <!-- Half image - Sanity -->
    <template v-if="halfImageBreaks">
      <template v-for="(item, index) in halfImageBreaks">
        <li
          v-if="isVisible(item.mobileRow, item.mobileColumn, item.desktopRow, item.desktopColumn)"
          :key="`${index}-usp-half-image`"
          :class="[
            `grid-row-start-${item.mobileRow} grid-col-start-${item.mobileColumn}`,
            `lg:grid-row-start-${item.desktopRow} lg:grid-col-start-${item.desktopColumn}`
          ]"
        >
          <PlpBreakCard
            data-section="plp-usp"
            v-bind="{
              heading: item.type.heading,
              body: item.type.body,
              ctaUrl: item.type.ctaUrl,
              ctaCopy: item.type.ctaCopy,
              image: item.type.image,
              icon: item.type.icon,
              bgColor: item.type.bgColor,
              isThemeLight: item.type.isThemeLight,
              positionMobile: { row: item.mobileRow, column: item.mobileColumn },
              positionDesktop: { row: item.desktopRow, column: item.desktopColumn },
              productsCount
            }"
          />
        </li>
      </template>
    </template>

    <!-- Full image - Sanity -->
    <template v-if="fullImageBreaks">
      <li
        v-for="(item, index) in fullImageBreaks"
        :key="`${index}-usp-full-image`"
        :class="[
          `grid-row-start-${item.mobileRow} grid-col-start-${item.mobileColumn}`,
          `lg:grid-row-start-${item.desktopRow} lg:grid-col-start-${item.desktopColumn}`
        ]"
      >
        <PlpBreakCardFullImage
          data-section="plp-usp"
          v-bind="{
            heading: item.type.heading,
            body: item.type.body,
            ctaUrl: item.type.ctaUrl,
            image: item.type.image,
            icon: item.type.icon,
            isThemeLight: item.type.isThemeLight
          }"
        />
      </li>
    </template>

    <!-- Solid -->
    <template v-if="solidColorBreaks">
      <li
        v-for="(item, index) in solidColorBreaks"
        :key="`${index}-usp-solid-color`"
        :class="[
          `grid-row-start-${item.mobileRow} grid-col-start-${item.mobileColumn}`,
          `lg:grid-row-start-${item.desktopRow} lg:grid-col-start-${item.desktopColumn}`
        ]"
      >
        <PlpBreakCardSolidColor
          data-section="plp-usp"
          v-bind="{
            heading: item.type.heading,
            body: item.type.body,
            ctaUrl: item.type.ctaUrl,
            ctaCopy: item.type.ctaCopy,
            icon: item.type.icon,
            bgColor: item.type.bgColor,
            isThemeLight: item.type.isThemeLight
          }"
        >
          <template #icon>
            <IconUspFreeDelivery />
          </template>
        </PlpBreakCardSolidColor>
      </li>
    </template>
    <!-- END CUSTOM ADDED BREAKS FROM SANITY -->

    <!-- PREDEFINED BREAKS -->
    <!-- Promo -->
    <li
      v-if="promoData && promoUsp && isVisible(3, 1, 2, 1)"
      class="col-start-1 row-start-3 lg:row-start-2 lg:col-start-1"
    >
      <PlpBreakPromoCard
        data-section="plp-usp-promo"
        v-bind="{
          heading: promoData.gridHeader,
          imagePathPromo: promoData.gridImageUrl,
          promoCode: promoData.code,
          ctaCopy: $t('grid.usp.promo.copy_promo_code'),
          copiedPromoCode: $t('grid.usp.promo.copy_promo_code_copied')
        }"
      />
    </li>

    <!-- Overal rating -->
    <li
      v-else-if="ratingUsp && isVisible(3, 1, 2, 1)"
      class="col-start-1 row-start-3 lg:row-start-2 lg:col-start-1"
    >
      <PlpBreakCard
        data-section="plp-usp-overal-rating"
        class="bg-[#EFD4CD]"
        v-bind="{
          heading: $t('grid.usp.reviews.title', { rating: reviewsAverageScore }),
          body: $t('grid.usp.reviews.body', { count: reviewsCount }),
          ctaUrl: $addLocaleToPath('review-list'),
          ctaCopy: $t('grid.usp.reviews.cta'),
          imagePath: 'plp/breaks/social-proof-reviews/regular',
          imageType: 'M T SD LD XLD',
        }"
      >
        <template #icon>
          <IconUspRating />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Free delivery -->
    <li
      v-if="freeDeliveryUsp && isVisible(
        sanityData.freeDeliveryUsp.mobileRow,
        sanityData.freeDeliveryUsp.mobileColumn,
        sanityData.freeDeliveryUsp.desktopRow,
        sanityData.freeDeliveryUsp.desktopColumn)"
      :class="[
        `grid-row-start-${sanityData.freeDeliveryUsp.mobileRow} grid-col-start-${sanityData.freeDeliveryUsp.mobileColumn}`,
        `lg:grid-row-start-${sanityData.freeDeliveryUsp.desktopRow} lg:grid-col-start-${sanityData.freeDeliveryUsp.desktopColumn}`
      ]"
    >
      <PlpBreakCard
        data-section="plp-usp-free-delivery"
        class="bg-[#F8EFCA] pointer-events-none"
        v-bind="{
          heading: $t('grid.usp.free_delivery.title'),
          body: $t('grid.usp.free_delivery.body'),
          imagePath: 'plp/breaks/free-delivery/regular',
          imageType: 'M T SD LD XLD',
        }"
      >
        <template #icon>
          <IconUspFreeDelivery />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Product lines -->
    <li
      v-if="productLinesUsp && isVisible(
        sanityData.productLinesUsp.mobileRow,
        sanityData.productLinesUsp.mobileColumn,
        sanityData.productLinesUsp.desktopRow,
        sanityData.productLinesUsp.desktopColumn)"
      :class="[
        `grid-row-start-${sanityData.productLinesUsp.mobileRow} grid-col-start-${sanityData.productLinesUsp.mobileColumn}`,
        `lg:grid-row-start-${sanityData.productLinesUsp.desktopRow} lg:grid-col-start-${sanityData.productLinesUsp.desktopColumn}`
      ]"
    >
      <PlpBreakCard
        data-section="plp-usp-product-lines"
        class="bg-[#5C7893]"
        v-bind="{
          heading: `Hide and display`,
          body: `Mix doors and open compartments to hide or display your staff.`,
          ctaUrl: $addLocaleToPath('product-lines.index'),
          ctaCopy: 'Learn more',
          imagePath: 'plp/breaks/product-lines/regular',
          imageType: 'M T SD LD XLD',
          isThemeLight: true
        }"
      >
        <template #icon>
          <IconUspProductLines />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Samples -->
    <li
      v-if="sampleUsp && isVisible(
        sanityData.sampleUsp.mobileRow,
        sanityData.sampleUsp.mobileColumn,
        sanityData.sampleUsp.desktopRow,
        sanityData.sampleUsp.desktopColumn)"
      :class="[
        `grid-row-start-${sanityData.sampleUsp.mobileRow} grid-col-start-${sanityData.sampleUsp.mobileColumn}`,
        `lg:grid-row-start-${sanityData.sampleUsp.desktopRow} lg:grid-col-start-${sanityData.sampleUsp.desktopColumn}`
      ]"
    >
      <PlpBreakCard
        data-section="plp-usp-samples"
        class="bg-[#E3E1F9]"
        v-bind="{
          heading: $t('grid.usp.samples.title'),
          body: $t('grid.usp.samples.body'),
          ctaUrl: $addLocaleToPath('lp.samples'),
          ctaCopy: $t('grid.usp.samples.cta'),
          imagePath: 'plp/breaks/sample/regular',
          imageType: 'M T SD LD XLD',
          isThemeLight: false
        }"
      >
        <template #icon>
          <IconUspSamples />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Assembly service -->
    <li
      v-if="assemblyUsp && isVisible(
        sanityData.assemblyUsp.mobileRow,
        sanityData.assemblyUsp.mobileColumn,
        sanityData.assemblyUsp.desktopRow,
        sanityData.assemblyUsp.desktopColumn)"
      :class="[
        `grid-row-start-${sanityData.assemblyUsp.mobileRow} grid-col-start-${sanityData.assemblyUsp.mobileColumn}`,
        `lg:grid-row-start-${sanityData.assemblyUsp.desktopRow} lg:grid-col-start-${sanityData.assemblyUsp.desktopColumn}`
      ]"
    >
      <PlpBreakCard
        data-section="plp-usp-assembly-service"
        class="bg-[#C0D1E7]"
        v-bind="{
          heading: $t('grid.usp.assembly_service.title'),
          body: $t('grid.usp.assembly_service.body'),
          ctaUrl: $addLocaleToPath('faq-links.assembly'),
          ctaCopy: $t('grid.usp.assembly_service.cta'),
          imagePath: 'plp/breaks/assembly-service/regular',
          imageType: 'M T SD LD XLD',
        }"
      >
        <template #icon>
          <IconUspAssembly />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Free returns -->
    <li
      v-if="freeReturnsUsp && isVisible(
        sanityData.freeReturnsUsp.mobileRow,
        sanityData.freeReturnsUsp.mobileColumn,
        sanityData.freeReturnsUsp.desktopRow,
        sanityData.freeReturnsUsp.desktopColumn)"
      :class="[
        `grid-row-start-${sanityData.freeReturnsUsp.mobileRow} grid-col-start-${sanityData.freeReturnsUsp.mobileColumn}`,
        `lg:grid-row-start-${sanityData.freeReturnsUsp.desktopRow} lg:grid-col-start-${sanityData.freeReturnsUsp.desktopColumn}`
      ]"
    >
      <PlpBreakCard
        data-section="plp-usp-free-returns"
        class="bg-[#EFD4CD]"
        v-bind="{
          heading: $t('grid.usp.free_returns.title'),
          body: $t('grid.usp.free_returns.body'),
          imagePath: 'plp/breaks/free-returns/regular',
          imageType: 'M T SD LD XLD',
          isThemeLight: false
        }"
      >
        <template #icon>
          <IconUspFreeReturns />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Shop the look -->
    <li
      v-if="shopTheLookUsp && isVisible(
        sanityData.shopTheLookUsp.mobileRow,
        sanityData.shopTheLookUsp.mobileColumn,
        sanityData.shopTheLookUsp.desktopRow,
        sanityData.shopTheLookUsp.desktopColumn)"
      :class="[
        `grid-row-start-${sanityData.shopTheLookUsp.mobileRow} grid-col-start-${sanityData.shopTheLookUsp.mobileColumn}`,
        `lg:grid-row-start-${sanityData.shopTheLookUsp.desktopRow} lg:grid-col-start-${sanityData.shopTheLookUsp.desktopColumn}`
      ]"
    >
      <PlpBreakCard
        data-section="plp-usp-free-returns"
        class="bg-[#EFE4E8]"
        v-bind="{
          heading: $t('grid.usp.ugc.title'),
          body: $t('grid.usp.ugc.body'),
          ctaUrl: $addLocaleToPath('lp.gallery'),
          ctaCopy: $t('grid.usp.ugc.cta'),
          imagePath: 'plp/breaks/category-inspiration-sideboard/regular',
          imageType: 'M T SD LD XLD',
        }"
      >
        <template #icon>
          <IconUspInspiration />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Shop by room -->
    <li
      v-if="shopByRoomUsp && isVisible(
        sanityData.shopByRoomUsp.mobileRow,
        sanityData.shopByRoomUsp.mobileColumn,
        sanityData.shopByRoomUsp.desktopRow,
        sanityData.shopByRoomUsp.desktopColumn)"
      :class="[
        `grid-row-start-${sanityData.shopByRoomUsp.mobileRow} grid-col-start-${sanityData.shopByRoomUsp.mobileColumn}`,
        `lg:grid-row-start-${sanityData.shopByRoomUsp.desktopRow} lg:grid-col-start-${sanityData.shopByRoomUsp.desktopColumn}`
      ]"
    >
      <PlpBreakCard
        data-section="plp-usp-free-returns"
        class="bg-offwhite-600"
        v-bind="{
          heading: $t('grid.usp.rooms.title'),
          body: $t('grid.usp.rooms.body'),
          ctaUrl: $addLocaleToPath('lp.rooms'),
          ctaCopy: $t('grid.usp.rooms.cta'),
          imagePath: 'plp/breaks/shop-by-room/regular/1',
          imageType: 'M T SD LD XLD',
        }"
      >
        <template #icon>
          <IconUspInspiration />
        </template>
      </PlpBreakCard>
    </li>

    <!-- Customise yours - last break -->
    <li v-if="productsCount > 0 && !activeCategory.areFiltersBarDisabled">
      <PlpBreakCard
        data-section="plp-usp-configure-yours"
        class="bg-[#FFEADB] pointer-events-none"
        v-bind="{
          heading: $t('grid.usp.customise_yours.title'),
          body: $t('grid.usp.customise_yours.body'),
          imagePath: `plp/breaks/customization/${activeCategory.name}/regular`,
          imageType: 'M T SD LD XLD',
          isThemeLight: false
        }"
      >
        <template #icon>
          <IconUspConfigure />
        </template>
      </PlpBreakCard>
    </li>
  </ul>
</template>
