<template>
  <section
    class="overflow-hidden slim py-64 lg:py-96 bg-beige-200"
    :class="wrapperClass"
    :data-section="carouselName"
  >
    <div class="grid-container">
      <p
        class="mb-16 uppercase lg:mb-24 semibold-14 lg:semibold-18 text-neutral-750"
        data-testid="carousel-tagline"
        v-text="tagline"
      />
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-24 lg:mb-48">
        <h2
          class="semibold-32 lg:semibold-54 text-neutral-900"
          data-testid="carousel-headline"
          v-text="headline"
        />
        <BaseLink
          class="md-max:mt-16"
          variant="outlined"
          v-bind="{
            href: buttonHref,
            trackData: { eventLabel: 'Creators view all' }
          }"
        >
          {{ $t('common.view_all') }}
        </BaseLink>
      </div>
      <CarouselGeneric v-bind="{ swiperOptions: { loop: true, loopAdditionalSlides: 1, initialSlide: 1 } }">
        <CarouselGenericCard
          v-for="(item, index) in items"
          :key="`categories-${index}`"
          class="flex-shrink-0 !w-[278px] !h-auto md:!w-auto mr-[var(--gutter)] last:mr-0 overflow-hidden rounded-24 after:h-[80%] after:w-full after:absolute after:bottom-0 after:left-0"
          data-testid="swiper-slide"
        >
          <BaseLink
            variant="custom"
            v-bind="{
              href: `${$addLocaleToPath('lp.influencers')}${item.query}`,
              trackData: { eventLabel: item.trackLabel }
            }"
            class="relative block overflow-hidden group/category z-1 text-center"
          >
            <BasePicture
              picture-classes="w-full h-full"
              type="M T SD LD"
              disable-lazy
              loading="lazy"
              data-testid="swiper-slide-picture"
              v-bind="{
                imgClasses: ['w-full h-full transform zoom-in-transition group-hover/category:scale-[1.05]', imageClasses],
                path: item.picturePath,
                alt: $t(item.title)
              }"
            />

            <aside class="absolute top-24 lg:top-32 left-0 right-0 flex items-center justify-center">
              <IconTylkoLogo class="h-[18px] text-white" />
              <p
                class="text-white normal-14 lg:normal-16 uppercase ml-12"
                v-text="$t(item.productLine)"
              />
            </aside>

            <div class="absolute left-0 bottom-0 w-full lg:px-24 lg:py-32 px-8 py-24 flex flex-col items-start bg-gradient-to-b justify-end h-3/5 from-transparent to-[#00000080]">
              <p
                class="text-white normal-14 lg:normal-16 uppercase w-full"
                v-text="$t('common.creator')"
              />
              <h2
                class="text-white semibold-24 lg:semibold-32 w-full mt-4"
                data-testid="swiper-slide-text"
                v-text="$t(item.title)"
              />
              <p
                class="text-white normal-14 lg:normal-16 w-full mt-4"
                data-testid="swiper-slide-text"
                v-text="$t(item.description)"
              />
            </div>
          </BaseLink>
        </CarouselGenericCard>
      </CarouselGeneric>
    </div>
  </section>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  tagline?: string;
  headline?: string;
  buttonHref?: string;
  carouselName: string;
  wrapperClass?: string;
  imageClasses?: string;
  items: Array<{
    query : string;
    title: string;
    trackLabel: string;
    description: string;
    picturePath: string;
    productLine: string;
}>;
}>(), {
  tagline: '',
  headline: '',
  buttonHref: '',
  imageClasses: 'aspect-[2/3]',
  wrapperClass: ''
});
</script>
