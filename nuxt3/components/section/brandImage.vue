<template>
  <section class="bg-beige-100">
    <div
      class="relative flex items-end custom-aspect-ratio grid-container overflow-hidden"
      :style="`--image-aspect-ratio-mobile: ${aspectRatio?.mobile}; --image-aspect-ratio-desktop: ${aspectRatio?.desktop};`"
      :class="containerAdditionalClasses"
    >
      <div class="absolute inset-0 w-full h-full grid-container md-max:px-0">
        <BasePicture
          class="rounded-24 overflow-hidden w-full h-full md-max:rounded-none"
          img-classes="block h-full w-full object-cover"
          v-bind="{
            path: imagePath,
            alt: title,
            type: imageType
          }"
        />
      </div>

      <aside class="grid-container w-full relative z-1 py-32 xl:py-48 md-max:px-0">
        <h2
          v-if="tagline"
          class="uppercase semibold-12 md:semibold-14 lg:semibold-16 text-neutral-200"
          v-text="tagline"
        />
        <h1
          v-if="title"
          class="semibold-32 md:semibold-44 lg:semibold-54 mt-8 text-neutral-100"
          v-text="title"
        />
        <p
          v-if="description"
          class="semibold-18 lg:normal-18 mt-8 text-neutral-100"
          v-text="description"
        />
        <div class="mt-24 md:mt-32 flex gap-16">
          <BaseLink
            v-if="ctaCopy && ctaUrl"
            class=" ty-btn--xl"
            v-bind="{
              ...ctaUrl && { href: ctaUrl },
              variant: 'filled-dark',
              trackData: {
                eventLabel: 'cta',
                eventPath: ctaUrl
              }
            }"
          >
            {{ ctaCopy }}
          </BaseLink>
          <BaseLink
            v-if="additionalCtaCopy && additionalCtaUrl"
            class=" ty-btn--xl"
            v-bind="{
              ...additionalCtaUrl && { href: additionalCtaUrl },
              variant: 'outlined-dark',
              trackData: {
                eventLabel: 'additional-cta',
                eventPath: additionalCtaUrl
              }
            }"
          >
            {{ additionalCtaCopy }}
          </BaseLink>
        </div>
      </aside>
    </div>
  </section>
</template>

<script setup lang="ts">
defineProps({
  tagline: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  imagePath: {
    type: String,
    default: ''
  },
  imageType: {
    type: String as PropType<'A' | 'M D' | 'M T SD LD' | 'M T SD LD XLD'>,
    default: 'A'
  },
  aspectRatio: {
    type: Object as PropType<{
      mobile: string;
      desktop: string;
    }>,
    default: () => ({
      mobile: '3/4',
      desktop: '16/9'
    })
  },
  ctaCopy: {
    type: String,
    default: ''
  },
  ctaUrl: {
    type: String,
    default: ''
  },
  containerAdditionalClasses: {
    type: String,
    default: ''
  },
  imageContainerAdditionalClasses: {
    type: String,
    default: ''
  },
  imageAdditionalClasses: {
    type: String,
    default: ''
  },
  additionalCtaCopy: {
    type: String,
    default: ''
  },
  additionalCtaUrl: {
    type: String,
    default: ''
  }
});
</script>

<style lang="scss" scoped>
.custom-aspect-ratio {
  aspect-ratio: var(--image-aspect-ratio-mobile);

  @media (min-width: 768px) {
    aspect-ratio: var(--image-aspect-ratio-desktop);
  }
}
</style>
