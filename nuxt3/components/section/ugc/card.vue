<template>
  <figure
    class="flex flex-col h-full bg-white relative"
    :class="cardWrapperAdditionalClass"
  >
    <BaseLink
      variant="custom"
      v-bind="{
        href: url ,
        trackData: {
          ...(Object.keys(trackData).length ? { ...trackData, eventLabel: item.category } : {}),
        },
        preventDefault: true,
        'data-index': index
      }"
      data-testid="product-card-link"
      class="group/minigrid-product-card"
      v-on:click="handleLeftMouseButtonClick"
      v-on:click.right="handleOtherMouseButtonsClick('right')"
      v-on:click.middle="handleOtherMouseButtonsClick('middle')"
    >
      <div class="relative w-full overflow-hidden pt-[100%]">
        <img
          v-bind="{
            alt: productName,
            src: productUnrealImage,
            loading: lazyLoading ? 'lazy' : 'eager'
          }"
          class="absolute top-0 w-full duration-500 ease-in-out"
          data-testid="product-card-image"
          :class="activeGridView === 'lifestyle' && previewInstagrid ?
            'opacity-0 transition-opacity group-hover/minigrid-product-card:opacity-100 z-[11]' :
            'transform transition-transform group-hover/minigrid-product-card:scale-[1.04]'"
        >
        <img
          v-if="previewInstagrid"
          v-bind="{
            src: previewInstagrid,
            alt: productName
          }"
          loading="lazy"
          class="absolute top-0 w-full duration-500 ease-in-out"
          data-testid="product-card-image-instagrid"
          :class="activeGridView === 'lifestyle' ?
            'transform transition-transform group-hover/minigrid-product-card:scale-[1.04] z-1' :
            'opacity-0 transition-all group-hover/minigrid-product-card:opacity-100'"
        >
        <BaseBadge
          v-if="labels.length && saleLabel"
          :variant="extraData?.customBadges ? 'custom' : 'generic'"
          class="mr-4 absolute top-12 left-16 z-[15] flex py-[3px] !px-12 rounded-30"
          :class="extraData?.customBadges && 'custom-color custom-bg-color'"
          data-testid="product-card-badge"
        >
          {{ saleLabel.value }}
        </BaseBadge>
        <BaseBadge
          v-else-if="extraData?.customBadges && isSofa"
          :variant="extraData?.customBadges ? 'custom' : 'generic'"
          class="mr-4 absolute top-12 left-16 z-[15] flex py-[3px] !px-12 rounded-30"
          :class="extraData?.customBadges && 'custom-color custom-bg-color'"
          data-testid="product-card-badge"
          :style="{
            backgroundColor: extraData?.customBadgeTextColor,
            color: extraData?.customBadgeBackgroundColor
          }"
        >
          {{ extraData?.translateLabel ? $t(extraData?.customLabelSofa) : extraData?.customLabelSofa }}
        </BaseBadge>
      </div>
    </BaseLink>

    <aside
      class="absolute z-[11] top-8 right-8 w-48 h-48 bg-white rounded-full flex items-center justify-center cursor-pointer"
      v-on:click="handleWishlistClick"
    >
      <IconHeart
        ref="wishlistIcon"
        class="h-24"
        :class="isWishlistActive ? 'text-orange fill-orange' : ''"
      />
    </aside>

    <div class="flex flex-col justify-between flex-1 py-16">
      <div class="flex flex-col gap-2">
        <aside class="flex flex-row justify-between items-center">
          <p
            class="h-[18px] semibold-12 uppercase"
            data-testid="product-card-label"
            :class="bottomLabel?.textColorClass || ''"
            v-html="bottomLabel && bottomLabel.translationKey && $t(bottomLabel.translationKey)"
          />
          <div
            class="flex gap-4 flex-1 py-12 lg:py-2 items-center justify-end"
            data-testid="product-card-swatches"
          >
            <template v-if="availableMaterials?.length">
              <img
                v-for="(material, swatchIndex) in availableMaterials.slice(0, SWATCHES_TO_SHOW_COUNT)"
                :key="`material-${swatchIndex}`"
                width="16"
                height="16"
                class="block object-cover overflow-hidden rounded-full swatch max-h-[16px]"
                v-bind="{
                  loading: lazyLoading ? 'lazy' : 'eager',
                  src: `${shelfType === 10 ? getAssetPath(`common/swatch/${material.iconPath}/A`, 'webp') : getAssetPath(`common/swatch/${material.iconPath}/small/A`, 'svg')}`,
                  class: swatchWithBorder(material.materialName) && 'border border-neutral-400'
                }"
              >
              <span
                v-if="availableMaterialsCount > SWATCHES_TO_SHOW_COUNT"
                class="normal-14 text-offblack-600"
                v-html="`+${availableMaterialsCount - SWATCHES_TO_SHOW_COUNT}`"
              />
            </template>
          </div>
        </aside>

        <p
          class="semibold-14 mt-8 text-neutral-900"
          data-testid="product-card-furniture-type"
          v-html="props?.item?.productLine || (productLineKey && $t(productLineKey))"
        />
        <h2
          class="one-liner normal-12 md:normal-14 text-neutral-750"
          data-testid="product-card-furniture-description"
          v-html="productName"
        />
        <h3
          v-if="isSofa"
          class="normal-12 md:normal-14 text-neutral-750"
          data-testid="product-card-furniture-size"
          v-html="`${width} x ${depth} cm`"
        />
        <h3
          v-else
          class="normal-12 md:normal-14 text-neutral-750"
          data-testid="product-card-furniture-size"
          v-html="`${width / 10} x ${height / 10} cm`"
        />
      </div>

      <SectionMinigridCardPrice
        v-bind="{
          item
        }"
      />

      <BaseLink
        class="mt-16 ty-btn--s w-max"
        variant="outlined"
        v-bind="{
          href: url,
          variant: 'outlined',
          trackData: {
            eventLabel: ''
          }
        }"
      >
        {{ $t('common.configure') }}
      </BaseLink>
    </div>
  </figure>
</template>

<script setup lang="ts">
import getAssetPath from '~/utils/assets';
import { ADD_TO_WISHLIST_BY_ID } from '~/api/wishlist';
import { GET_PRODUCT_LINE_KEY_BY_SHELF_TYPE } from '~/utils/types';

export interface Item {
  id: number,
  price: string,
  brand: string,
  material: string,
  category: string,
  shelfType: number,
  regionPrice: number,
  variant: Array<any>,
  omnibusPrice: number,
  trackingList: string,
  furnitureType: string,
  configuratorType: number,
  regionPriceInEuro: number,
  physicalProductVersion: number,
  regionPriceWithDiscount: number,
  regionPriceWithDiscountInEuro: number,
  discountValue: string,
}

const props = withDefaults(defineProps<{
  item:Item,
  url: string,
  index: number,
  width: number,
  height: number,
  depth: number,
  trackData?: any,
  filterId: string | number,
  productName: string,
  shelfType: SHELF_TYPE,
  lazyLoading?: boolean,
  availableMaterials?: [],
  previewInstagrid?: string,
  productUnrealImage: string,
  cardWrapperAdditionalClass?: string,
  activeGridView?: 'lifestyle' | 'product',
  labels?: Array<{ type: string, value: string }>
}>(), {
  availableMaterials: () => [],
  trackData: {},
  depth: 0,
  labels: () => [],
  lazyLoading: true,
  previewInstagrid: '',
  activeGridView: 'product',
  cardWrapperAdditionalClass: ''
});

const $gtm = useGtm();

const { isSm } = useMq();
const { FETCH_GLOBAL } = useGlobal();

const isSale = false;

const isWishlistActive = ref(false);

const isSofa = computed(() => +props.shelfType === 10);
const SWATCHES_TO_SHOW_COUNT = computed(() => isSm.value ? 2 : 5);
const saleLabel = computed(() => props.labels.find(label => label?.type === 'discount'));
const productLineKey = computed(() => GET_PRODUCT_LINE_KEY_BY_SHELF_TYPE(props.shelfType));
const availableMaterialsCount = computed(() => Object.keys(props.availableMaterials).length);

const labelsProps = {
  soon: {
    textColorClass: 'text-[#8868AA]',
    translationKey: 'plp.board.product_card.label.soon'
  },
  new: {
    textColorClass: 'text-[#5A834B]',
    translationKey: 'plp.board.product_card.label.new_arrival'
  },
  discount: {
    textColorClass: isSale ? 'text-[#ffad01]' : 'text-orange',
    translationKey: isSale ? 'plp.board.product_card.label.season_sale' : 'plp.board.product_card.label.sale'
  },
  top_seller: {
    textColorClass: 'text-[#BE7958]',
    translationKey: 'plp.filters.features.top_seller'
  },
  special_edition: {
    textColorClass: 'text-neutral-800',
    translationKey: 'plp.filters.features.special_edition'
  },
  special: {
    textColorClass: 'text-orange',
    translationKey: 'plp.filters.features.special'
  }
} as const;

const bottomLabel = computed(() => {
  const label = props.labels.find(
    el => el.value === 'new' ||
          el.value === 'soon' ||
          el.value === 'top_seller' ||
          el.value === 'special_edition' ||
          el.value === 'special'
  );

  return labelsProps[label?.value] || '';
});

const handleLeftMouseButtonClick = async () => {
  emit('selectItemGA4Event', { item: props.item, index: props.index, eventCategory: 'left_click' });
  await navigateTo(props.url, { external: true });
};

const handleOtherMouseButtonsClick = (buttonType: 'middle' | 'right') => {
  const isMiddleButtonClick = buttonType === 'middle';

  $gtm && $gtm.push({
    event: 'userInteraction',
    eventCategory: 'PLP',
    event_section: 'item_miniatures',
    eventAction: isMiddleButtonClick ? 'scroll_click' : 'contextmenu',
    eventLabel: 'undefined'
  });

  isMiddleButtonClick && emit('selectItemGA4Event', { item: props.item, index: props.index, eventCategory: 'scroll' });
};

const handleWishlistClick = async () => {
  await ADD_TO_WISHLIST_BY_ID(props.item.id, props.item.furnitureType as FurnitureModel);
  await FETCH_GLOBAL();

  isWishlistActive.value = true;
};

const swatchWithBorder = (materialName: string) => {
  return materialName.includes('white') || materialName.includes('cotton');
};

watch(() => props.filterId, () => {
  isWishlistActive.value = false;
});

const emit = defineEmits(['selectItemGA4Event']);
</script>

<style lang="scss" scoped>
.one-liner {
  -webkit-box-orient: vertical; /* stylelint-disable-line property-no-vendor-prefix */
  display: -webkit-box; /* stylelint-disable-line value-no-vendor-prefix */
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
}
</style>
