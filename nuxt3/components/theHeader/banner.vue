<template>
  <!-- Promo image-->
  <BaseLink
    class="block relative max-w-[360px] w-full place-self-start"
    v-bind="{
      href: $addLocaleToPath('plp'),
      variant: 'custom',
      trackData: {
        eventLabel: 'cta',
        eventPath: `${$addLocaleToPath('plp')}`
      }
    }"
  >
    <div class="relative">
      <BasePicture
        class="relative "
        variant="custom"
        img-classes="object-cover object-center w-full overflow-hidden rounded-8"
        v-bind="{
          type: 'M T SD LD XLD',
          path: 'common/menu/megamenu/halloween2025',
          disablePlaceholder: true,
          alt: $t('menu.banner.halloween.tagline')
        }"
      />
      <div class="flex flex-col gap-4 absolute top-0 left-0 w-full h-full p-16 xl:p-20 md:p-12 lg2:p-16 text-balance text-offwhite-700">
        <span class="normal-14 text-balance">
          {{ $t('menu.banner.halloween.tagline') }}
        </span>
        <h1
          class="bold-20 lg:bold-18 lg2:bold-20 xl:bold-24 text-balance"
          v-html="$t('menu.banner.halloween.body', { value: format(promoValues[regionName].value2) })"
        />
        <div class="mx-auto justify-self-end xl-max:ty-btn--s ty-btn-outlined ty-btn-outlined--dark mt-auto">
          {{ $t('menu.banner.halloween.cta') }}
        </div>
      </div>
    </div>
  </BaseLink>
</template>

<script setup lang="ts">
const { regionName } = storeToRefs(useGlobal());
const { format } = usePrice();

const promoValues = {
  _other: {
    value1: 1,
    value2: 200
  },
  netherlands: {
    value1: 1,
    value2: 200
  },
  poland: {
    value1: 5,
    value2: 850
  },
  bulgaria: {
    value1: 1,
    value2: 200
  },
  croatia: {
    value1: 1,
    value2: 200
  },
  czech: {
    value1: 1,
    value2: 200
  },
  estonia: {
    value1: 1,
    value2: 200
  },
  finland: {
    value1: 1,
    value2: 200
  },
  france: {
    value1: 1,
    value2: 200
  },
  germany: {
    value1: 1,
    value2: 200
  },
  greece: {
    value1: 1,
    value2: 200
  },
  hungary: {
    value1: 1,
    value2: 200
  },
  ireland: {
    value1: 1,
    value2: 200
  },
  italy: {
    value1: 1,
    value2: 200
  },
  latvia: {
    value1: 1,
    value2: 200
  },
  lithuania: {
    value1: 1,
    value2: 200
  },
  luxembourg: {
    value1: 1,
    value2: 200
  },
  norway: {
    value1: 19,
    value2: 2000
  },
  portugal: {
    value1: 1,
    value2: 200
  },
  romania: {
    value1: 1,
    value2: 200
  },
  slovakia: {
    value1: 1,
    value2: 200
  },
  slovenia: {
    value1: 1,
    value2: 200
  },
  spain: {
    value1: 1,
    value2: 200
  },
  switzerland: {
    value1: 2,
    value2: 200
  },
  belgium: {
    value1: 1,
    value2: 200
  },
  united_kingdom: {
    value1: 1,
    value2: 200
  },
  sweden: {
    value1: 18,
    value2: 2000
  },
  denmark: {
    value1: 12,
    value2: 1500
  },
  austria: {
    value1: 1,
    value2: 100
  }
};
</script>
