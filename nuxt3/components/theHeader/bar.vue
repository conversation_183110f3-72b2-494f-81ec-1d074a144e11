<template>
  <div
    class="grid-container h-56 md:h-64 relative normal-16
            flex items-center justify-between
            transition-all ease-out duration-300"

    :class="(isThemeLight || isMegaMenuOpened ) ? '[--text-color:#1D1E1F] [--bg-color:#ffffff]' : '[--text-color:#ffffff] [--bg-color:#1D1E1F]'"
  >
    <!-- Logo Tylko -->
    <div
      class="flex-1 h-full inline-flex flex-col justify-center items-start"
      v-on:mouseover="toggleMegaMenu(false)"
    >
      <BaseLink
        variant="custom"
        data-testid="logo"
        class="!inline-block"
        v-bind="{ href: $addLocaleToPath('homepage'), trackData: {} }"
      >
        <IconTylkoLogo
          class="h-24 xl:h-32 text-orange !inline-block"
          :class="{ '!text-white' : isMegaMenuOpened }"
        />
      </BaseLink>
    </div>

    <div
      class="h-full p-4 md:p-8 md-max:-mr-8 lg:p-[6px] rounded-full flex lg:flex-[2]
              transition-[background-color] basic-transition"
      v-bind="{
        class: [
          { 'bg-[#e3e3e3]/[0.01] backdrop-blur-[50px]': variant === 'transparent' && !isDarkNavigationActive },
          { 'bg-black/[0.5] backdrop-blur-[50px]': variant === 'transparent' && isDarkNavigationActive },
          { 'bg-white': variant === 'light' },
          { 'bg-neutral-900': variant === 'dark' },
          { '!backdrop-blur-none !bg-transparent': isMegaMenuOpened },
        ]
      }"
    >
      <!-- Shop buttons -->
      <TheHeaderBarButtons
        class="[--width:100px] [--duration:500ms] lg-max:hidden flex-1 mr-32"
        link-extra-classes="px-24"
        v-bind="{
          buttonsList: shopMenuButtons,
          class: {
            'mega-menu-opened': isMegaMenuOpened
          }
        }"
      />

      <!-- Account buttons -->
      <div class="flex-1 flex justify-end">
        <TheHeaderBarButtons
          class="[--width:52px] [--duration:400ms]"
          link-extra-classes="px-12 lg:px-[14px]"
          v-bind="{
            class: {
              'mobile-mega-menu-opened': isMegaMenuOpened
            },
            buttonsList: accountMenuButtons,
          }"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useMounted } from '@vueuse/core';
import { useGlobal } from '~/stores/global';
import IconNotification from 'assets/icons/notification.svg';
import IconProfile from 'assets/icons/profile.svg';
import IconHeart from 'assets/icons/heart.svg';
import IconTrolley from 'assets/icons/trolley.svg';
import IconHamburger from 'assets/icons/hamburger.svg';
const props = defineProps<{
    variant : 'light' | 'dark' | 'transparent';
  }>();

export interface MenuButtonConfig {
  testId: string;
  href?: string;
  trackData?: {
    eventCategory: string;
    eventAction: string;
    eventLabel: string;
  };
  extraClasses?: string;
  label?: string;
  icon?: any;
  iconExtraClasses?: string;
  component?: any;
  badge?: number | null;
  attributes?: any;
  handlers: { };
}
const { t } = useI18n();
const { cartItemsCount, libraryItemsCount, brazeNotificationCount, userId, FF_BLACKFRIDAY } = storeToRefs(useGlobal());
const { $addLocaleToPath, $addLocaleToURL, $braze } = useNuxtApp();
const {
  isMegaMenuOpened,
  toggleMegaMenu,
  toggleMobileMegaMenu
} = useHeader();

const { isDarkNavigationActive } = storeToRefs(useHeaderStore());
const isThemeLight = computed(() => props.variant === 'dark' || props.variant === 'transparent');

const isMounted = useMounted();
const accountMenuButtons = computed<Array<MenuButtonConfig>>(() => [
  {
    testId: 'open-braze',
    trackData: {
      eventAction: 'Content Card Show',
      eventCategory: 'Content Card',
      eventLabel: 'Content Card Label'
    },
    icon: IconNotification,
    badge: brazeNotificationCount.value,
    attributes: {
      id: 'braze-button'
    },
    handlers: {
      click: () => $braze?.showFeedDrawer()
    }
  },
  {
    testId: 'redirect-account',
    href: $addLocaleToPath('account'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'account',
      eventLabel: 'opened'
    },
    icon: IconProfile,
    handlers: {},
    attributes: {
      asNuxtLink: true
    }
  },
  {
    testId: 'redirect-wishlist',
    href: $addLocaleToPath('library'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'favourites',
      eventLabel: 'opened'
    },
    icon: IconHeart,
    badge: libraryItemsCount.value,
    handlers: { }
  },
  {
    testId: 'open-cart',
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'basket',
      eventLabel: 'opened'
    },
    href: `${$addLocaleToPath('cart')}${(isMounted.value && userId.value) ? '?uuid=' + userId.value : ''}`,
    icon: IconTrolley,
    badge: cartItemsCount.value,
    handlers: { },
    attributes: {
      asNuxtLink: true
    }
  },
  {
    testId: 'mobile-menu-burger',
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'hamburger',
      eventLabel: 'opened'
    },
    extraClasses: 'lg:!hidden relative',
    icon: IconHamburger,
    iconExtraClasses: 'w-24 h-24',
    handlers: {
      click: () => toggleMobileMegaMenu()
    }
  }
]
);

const shopMenuButtons = computed<Array<MenuButtonConfig>>(() => [
  FF_BLACKFRIDAY.value && {
    testId: 'toggle-bf-tab',
    href: $addLocaleToURL('/blackfriday'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'black_friday',
      eventLabel: 'opened'
    },
    label: t('plp.board.product_card.label.season_sale'),
    attributes: {
      id: 'toggle-blackfriday-tab',
      href: $addLocaleToURL('/blackfriday'),
      trackData: {
        eventCategory: 'navigation',
        eventAction: 'black_friday',
        eventLabel: 'opened'
      }
    },
    extraClasses: 'whitespace-nowrap',
    handlers: {
      mouseover: () => (toggleMegaMenu(false))
    }
  },
  {
    testId: 'toggle-products-tab',
    href: $addLocaleToPath('plp'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'shop',
      eventLabel: 'opened'
    },
    label: t('menu.labels.shop'),
    attributes: {
      id: 'toggle-products-tab',
      href: $addLocaleToPath('lp.products'),
      trackData: {
        eventCategory: 'navigation',
        eventAction: 'shop',
        eventLabel: 'opened'
      }
    },
    extraClasses: '[.mega-menu-opened_&]:!text-[var(--text-color)]',
    handlers: {
      mouseover: () => (toggleMegaMenu(true))
    }
  },
  {
    testId: 'toggle-inspirations-tab',
    href: $addLocaleToPath('lp.inspirations'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'community',
      eventLabel: 'opened'
    },
    label: t('menu.labels.community'),
    handlers: {
      mouseover: () => (toggleMegaMenu(false))
    }
  },
  !FF_BLACKFRIDAY.value && {
    testId: 'redirect-tylko-pro',
    href: $addLocaleToPath('lp.tylkopro'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'tylko_for_business',
      eventLabel: 'opened'
    },
    label: t('menu.labels.tylko_pro'),
    handlers: {
      mouseover: () => (toggleMegaMenu(false))
    }
  },
  {
    testId: 'redirect-showroooms',
    href: $addLocaleToPath('showrooms'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'showrooms',
      eventLabel: 'opened'
    },
    label: 'Showrooms',
    handlers: {
      mouseover: () => (toggleMegaMenu(false))
    }
  }
].filter(Boolean)
);
</script>
