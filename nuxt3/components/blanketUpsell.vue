<template>
  <div class="bg-white p-16">
    <div class="flex md-max:gap-x-16 md:gap-x-32">
      <BaseLink
        variant="custom"
        v-bind="{
          trackData: {},
          href: $addLocaleToURL(`/${$t('common.category.pdp.blanket_url_path')}/${blankets[activeIndex].id}`),
          'data-testid': 'upsell-cart-link'
        }"
      >
        <BasePicture
          class="w-full aspect-square md-max:w-[159px] md:max-w-[259px] bg-transparent flex-shrink-0"
          img-classes="object-cover w-full aspect-square"
          type="M T SD LD XLD"
          data-testid="cart-item-image"
          v-bind="{
            alt: 'blanket',
            path: `pdp/blanket/gallery/${blankets[activeIndex].customFields[1].fabric.value}/${blankets[activeIndex].customFields[0].color.value}/1`,
            pictureClasses: 'w-full',
            isRetinaUploaded: false
          }"
        />
      </BaseLink>

      <div class="w-full flex-1 min-w-0">
        <div class="flex justify-between items-center pt-[23px] md:pt-12">
          <h2 class="text-neutral-900 semibold-16 block overflow-hidden text-ellipsis whitespace-nowrap">
            {{ blankets[activeIndex].name }}
          </h2>
          <CheckoutPriceItem
            class="md-max:hidden mb-8 max-w-[200px]"
            price-font-class="semibold-16 text-neutral-900"
            promo-price-font-class="semibold-16"
            lowest-price-font-class="normal-12"
            v-bind="{
              cartItem: {
                omnibus_price: blankets[activeIndex].pricing.omnibusPrice,
                item_price_without_discount_regionalized_number: blankets[activeIndex].pricing.regionPrice,
                item_price_regionalized_number: blankets[activeIndex].pricing.regionPriceWithDiscount
              },
            }"
          />
        </div>

        <p class="normal-14 text-neutral-700 mt-8">
          {{ $t('scart.item.label.color') }} {{ blankets[activeIndex].customFields[0].color.translated }}
        </p>
        <p class="normal-14 text-neutral-700 mt-2">
          {{ $t('scart.item.label.size') }}: {{ $t('scart.item_dimensions_without_depth', { height: blankets[activeIndex].height,
                                                                                            width: blankets[activeIndex].width }) }}
        </p>
        <CheckoutPriceItem
          class="md:hidden max-w-[200px] inline-block mt-16"
          price-font-class="semibold-16 text-neutral-900"
          promo-price-font-class="semibold-16"
          lowest-price-font-class="normal-12"
          v-bind="{
            cartItem: {
              omnibus_price: blankets[activeIndex].pricing.omnibusPrice,
              item_price_without_discount_regionalized_number: blankets[activeIndex].pricing.regionPrice,
              item_price_regionalized_number: blankets[activeIndex].pricing.regionPriceWithDiscount
            },
          }"
        />
        <div class="md-max:hidden">
          <div class="flex gap-x-8 mt-8">
            <button
              v-for="(blanket, index) in blankets"
              :key="index"
              class="w-[56px] h-[56px] overflow-hidden cursor-pointer"
              :class="{
                'border-b-2 border-neutral-900': activeIndex === index,
              }"
              v-on:click="handleColorChange(index)"
            >
              <BasePicture
                disable-lazy
                class="bg-transparent w-[56px] h-[56px]"
                v-bind="{
                  path: `configurator/tiles/skus/blankets/${camelToSnakeCase(blanket.customFields[0].color.value)}`,
                  alt: blanket.customFields[0].color.translated,
                  type: 'RAW'
                }"
              />
            </button>
          </div>
          <hr class="block my-24 border-t border-t-neutral-400">
          <div class="flex gap-x-16">
            <BaseButton
              variant="filled"
              v-on:click="() => handleA2c(blankets[activeIndex].id)"
            >
              {{ $t('common.add_to_cart') }}
            </BaseButton>
            <BaseButton
              variant="outlined"
              v-on:click="$emit('hideUpsell')"
            >
              {{ $t('common.no_thanks') }}
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
    <div class="md:hidden">
      <div class="flex gap-x-8 mt-24">
        <button
          v-for="(blanket, index) in blankets"
          :key="index"
          class="w-[48px] h-[48px] md:w-[56px] md:h-[56px] overflow-hidden cursor-pointer"
          :class="{
            'border-b-2 border-neutral-900': activeIndex === index,
          }"
          v-on:click="handleColorChange(index)"
        >
          <BasePicture
            disable-lazy
            class="bg-transparent w-[48px] h-[48px] md:w-[56px] md:h-[56px]"
            v-bind="{
              path: `configurator/tiles/skus/blankets/${camelToSnakeCase(blanket.customFields[0].color.value)}`,
              alt: blanket.customFields[0].color.translated,
              type: 'RAW'
            }"
          />
        </button>
      </div>
      <div class="flex gap-x-16 mt-24 md-max:flex-row-reverse">
        <BaseButton
          variant="filled"
          class="w-full whitespace-nowrap"
          v-on:click="() => handleA2c(blankets[activeIndex].id)"
        >
          {{ $t('common.add_to_cart') }}
        </BaseButton>
        <BaseButton
          variant="outlined"
          class="w-full whitespace-nowrap"
          v-on:click="$emit('hideUpsell')"
        >
          {{ $t('common.no_thanks') }}
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import useSkus from '~/composables/useSkusData';
import useSKUAnalytics, { type Blanket } from '~/composables/cart/useSKUAnalytics';

const $gtm = useGtm();

const { getSkusResults } = useSkus();
const { handelAddToCartPreset } = useCart();
const { sendA2CAnalytics } = useSKUAnalytics();

const blankets = await getSkusResults('Blanket');

const activeIndex = ref(0);

const handleColorChange = (index: number) => {
  activeIndex.value = index;

  $gtm?.push({
    event: 'userInteraction',
    eventCategory: 'blanket_upsell',
    eventAction: 'listing_color_change',
    eventLabel: blankets[activeIndex.value].customFields[0].color.value
  });
};

function camelToSnakeCase (str: string): string {
  return str.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
}

defineEmits(['hideUpsell']);

const handleA2c = async (id: number) => {
  await handelAddToCartPreset(id.toString(), 'skuvariant', 1, false);

  const analyticsData = {
    regionPrice: blankets[activeIndex.value].pricing.regionPrice,
    regionPriceWithDiscount: blankets[activeIndex.value].pricing.regionPriceWithDiscount,
    id: blankets[activeIndex.value].id,
    colorName: blankets[activeIndex.value].customFields[0].color.value
  };

  sendA2CAnalytics(analyticsData as Blanket, true);
};
</script>
