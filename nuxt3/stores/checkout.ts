import { isNil } from 'lodash-es';
import { useGlobal } from '~/stores/global';

export interface CheckoutState {
    status?: string | null;
    regionToSet?: {
      label: string
      value: string
    } | null;
    firstName: string | null;
    lastName: string | null;
    streetAddress1: string | null;
    houseNumber: string | null;
    streetAddress2: string | null;
    floorNumber: string | null;
    noElevator: string | null;
    postalCode: string | null;
    city: string | null;
    email: string | null;
    phone: string | null;
    invoicePhone: string | null;
    notes: string | null;
    invoiceFirstName: string | null;
    invoiceLastName: string | null;
    invoiceStreetAddress1: string | null;
    invoicePostalCode: string | null;
    invoiceCity: string | null;
    invoiceCountry: string | null;
    vat: string | null;
    companyName: string | null;
    newsletter: string | null;
    sameAsDelivery: boolean | null;
    invoiceAddressUsed?: boolean | null;
    password: string | null,
    signup: boolean,
    formIsMounted: boolean,
    sms: boolean | null;
    deliveries?: Array<any>
    oldSofaCollection?: any
}
export const useCheckoutStore = defineStore('checkout', {
  state: (): CheckoutState => ({
    status: null,
    regionToSet: null,
    firstName: null,
    lastName: null,
    streetAddress1: null,
    houseNumber: null,
    streetAddress2: null,
    floorNumber: null,
    noElevator: null,
    postalCode: null,
    city: null,
    email: null,
    phone: null,
    invoicePhone: null,
    notes: null,
    invoiceFirstName: null,
    invoiceLastName: null,
    invoiceStreetAddress1: null,
    invoicePostalCode: null,
    invoiceCity: null,
    invoiceCountry: null,
    vat: null,
    companyName: null,
    newsletter: null,
    sameAsDelivery: null,
    password: null,
    signup: false,
    formIsMounted: false,
    sms: null,
    deliveries: [],
    oldSofaCollection: null
  }),
  getters: {
    getRegionToSet: state => state.regionToSet
  },
  actions: {
    SET_STATUS (status: string) {
      this.status = status;
    },
    SET_CHECKOUT_DATA (data: CheckoutState) {
      const global = useGlobal();
      this.firstName = data.firstName;
      this.lastName = data.lastName;
      this.streetAddress1 = data.streetAddress1;
      this.houseNumber = data.houseNumber;
      this.streetAddress2 = data.streetAddress2;
      this.floorNumber = data.floorNumber;
      this.noElevator = isNil(data.noElevator) ? null : (data.noElevator ? 'no' : 'yes');
      this.postalCode = data.postalCode;
      this.city = data.city;
      this.email = data.email;
      this.phone = data.phone;
      this.invoicePhone = data.invoicePhone;
      this.notes = data.notes;
      this.invoiceFirstName = data.invoiceFirstName;
      this.invoiceLastName = data.invoiceLastName;
      this.invoiceStreetAddress1 = data.invoiceStreetAddress1;
      this.invoicePostalCode = data.invoicePostalCode;
      this.invoiceCity = data.invoiceCity;
      this.invoiceCountry = data.invoiceCountry ? data.invoiceCountry : global.regionName;
      this.vat = data.vat;
      this.companyName = data.companyName;
      this.newsletter = data.newsletter;
      this.password = data.password;
      this.sameAsDelivery = !data.invoiceAddressUsed;
      this.signup = data.signup;
      this.sms = data.sms;
    },
    SET_REGION_TO_SET (region: {
      label: string;
      value: string;
    }) {
      this.regionToSet = region;
    },
    SET_FROM_IS_MOUNTED () {
      this.formIsMounted = true;
    },
    DELIVERIES_UPDATE (data: any) {
      this.deliveries = data;
    },
    OLD_SOFA_COLLECTION_UPDATE (data: any) {
      this.oldSofaCollection = data;
    }
  }
});
