export interface YotpoPhoto {
  id: string;
  imageUrl: string;
  mediumUrl: string;
  thumbnailUrl: string;
  username: string;
  caption: string;
  createdAt: string;
  mediaType: string;
  taggedProducts: Array<{
    id: number;
    name: string;
    product_link: string;
    domain_key: string;
  }>;
  videoUrl?: string;
}

export interface YotpoAlbum {
  name: string;
  prettyName?: string;
  total: number;
  viewMore: boolean;
  images: YotpoPhoto[];
}

export interface YotpoAlbumsResponse {
  success: boolean;
  albums: YotpoAlbum[];
}
