<template>
  <main class="bg-beige-100 md:pt-128">
    <Head>
      <Title>{{ t('lp.gallery.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        :content="t('hp.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        :content="t('lp.gallery.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        :content="t('lp.gallery.meta.description')"
      />
    </Head>

    <section class="grid-container pt-24">
      <div class="flex justify-start items-center md-max:hidden mb-40">
        <BaseLink
          v-bind="{
            variant: 'custom',
            href: $addLocaleToPath('homepage'),
            trackData: {}
          }"
          class="normal-14 text-neutral-900 after:content-[''] relative after:absolute after:-right-[18px] after:top-1/2 after:-translate-y-1/2 after:w-4 after:h-4 after:bg-neutral-900"
        >
          {{ t('menu.labels.home') }}
        </BaseLink>
        <p
          class="ml-28 normal-14 text-neutral-700"
          v-text="t('menu.labels.inspiration')"
        />
      </div>
      <div class="md:hidden mb-24">
        <BaseLink
          v-bind="{
            variant: 'custom',
            href: $addLocaleToPath('homepage'),
            trackData: {}
          }"
          class="semibold-14 text-neutral-900 flex justify-start items-center"
        >
          <IconArrowRight class="ty-icon--s text-neutral-900 rotate-180 -mt-[2px] mr-8" />
          {{ $t('menu.labels.back_to_home') }}
        </BaseLink>
      </div>
      <h1
        class="semibold-32 md:semibold-54 mb-8"
        v-text="t('lp.gallery.text-centered.title')"
      />
      <p
        class="normal-16 md:normal-24"
        v-text="t('lp.gallery.text-centered.paragraph')"
      />
    </section>

    <section
      v-if="albumsWithAll.length > 0"
      class="grid-container mx-auto !px-0 mt-24"
    >
      <BaseCarousel
        data-section="gallery-album-carousel"
        data-testid="gallery-album-carousel"
        class="grid-container overflow-hidden"
        v-bind="{
          isNavigation: false,
          name: 'galleryAlbumCarousel',
          options: swiperOptions,
          swiperRootClasses: '!w-full gallery-album-carousel !overflow-visible',
          navPrevElClasses: ['md:!left-16 gallery-album-nav-button', !isSwiperInitialized && 'swiper-button-disabled'],
          navNextElClasses: ['md:!right-16 gallery-album-nav-button', !isSwiperInitialized && 'swiper-button-disabled']
        }"
      >
        <BaseCarouselSlide
          v-for="album in albumsWithAll"
          :key="album.name"
          data-testid="gallery-album-carousel-slide"
          class="!w-[120px] mr-16 last:mr-0"
        >
          <button
            class="relative flex flex-col justify-center items-center group box-content w-full"
            data-testid="gallery-album-carousel-slide-button"
            :class="album.name === selectedAlbum?.name ? 'pointer-events-none' : ''"
            v-on:click="selectAlbum(album)"
          >
            <BasePicture
              class="w-full aspect-square"
              img-classes="w-full aspect-square"
              type="A"
              v-bind="{
                path: `common/menu/categories/square/${getAlbumCategoryPath(album.name)}`,
                alt: album.prettyName ? $t(album.prettyName) : album.name,
                isRetinaUploaded: false
              }"
              disable-lazy
              disable-placeholder
            />
            <h3
              class="flex items-center justify-center h-full min-h-24 max-h-36 text-center pb-2 normal-12 lg:normal-14 text-neutral-900
              border-neutral-900 transition-all group-hover:text-neutral-800"
              data-testid="gallery-album-carousel-slide-name"
              :class="album.name === selectedAlbum?.name ? 'border-b semibold-12 lg:semibold-14' : 'border-transparent'"
            >
              {{ album.prettyName ? $t(album.prettyName) : album.name }}
            </h3>
          </button>
        </BaseCarouselSlide>
      </BaseCarousel>
    </section>

    <section class="grid-container mt-[80px] text-center">
      <UiDotsLoader
        v-if="pending"
        class="ml-8 text-orange w-56 !static !inline-flex gap-4 !translate-x-0 !translate-y-0"
        bounce-class="!w-8 !h-8 bg-orange"
      />

      <BaseButton
        v-else-if="error"
        variant="accent"
        v-on:click="loadAlbums"
      >
        {{ t('common.error.connection') }}
      </BaseButton>

      <div
        v-else-if="currentPhotos.length > 0"
        class="masonry-grid mt-24"
      >
        <article
          v-for="(photo, index) in currentPhotos"
          :key="photo?.id"
          :class="getMasonryItemClass(index)"
          :style="getMasonryItemStyle(index)"
          class="rounded-12 overflow-hidden relative cursor-pointer"
          v-on:click="handleShowProducts(photo)"
        >
          <aside class="absolute bottom-16 right-16 flex flex-col items-end gap-8">
            <a
              :href="`http://instagram.com/_u/${photo?.username}`"
              target="_blank"
              class="bg-beige-100 rounded-full flex items-center justify-center cursor-pointer p-12 transition-all duration-300 ease-out overflow-hidden group"
            >
              <InstagramIcon class="w-24 h-24 min-w-24 min-h-24 transition-[margin] duration-300 ease-out group-hover:mr-8" />
              <span
                class="opacity-0 max-w-0 transition-[opacity,max-width] duration-300 ease-out normal-14 text-neutral-900 whitespace-nowrap overflow-hidden
                group-hover:opacity-100 group-hover:max-w-[120px] lowercase"
              >{{ photo?.username }}</span>
            </a>

            <div
              class="bg-beige-100 rounded-full flex items-center justify-center cursor-pointer p-12 transition-all duration-300 ease-out overflow-hidden group"
              v-on:click="handleShowProducts(photo)"
            >
              <TagIcon class="w-24 h-24 min-w-24 min-h-24 transition-[margin] duration-300 ease-out group-hover:mr-8" />
              <span
                class="opacity-0 max-w-0 text-nowrap transition-[opacity,max-width] duration-300 ease-out normal-14 text-neutral-900 overflow-hidden
                group-hover:opacity-100 group-hover:max-w-[160px]"
              >{{ t('lp.gallery.show_products') }}</span>
            </div>
          </aside>
          <img
            v-if="photo?.mediaType === 'image'"
            :src="photo.mediumUrl"
            :alt="photo.caption || `Photo by ${photo?.username || ''}`"
            class="w-full h-full object-cover transition-transform transition-basic"
            loading="lazy"
          >
          <video
            v-else-if="photo?.mediaType === 'video'"
            :poster="photo.thumbnailUrl"
            class="w-full h-full object-cover"
            controls
          >
            <source
              :src="photo.videoUrl"
              type="video/mp4"
            >
          </video>
        </article>
      </div>

      <BaseButton
        v-if="selectedAlbum?.viewMore"
        variant="outlined"
        v-bind="{ trackData: {} }"
        class="mt-64"
        v-on="{ click: loadMore }"
      >
        {{ t('lp.gallery.loadMore') }}
      </BaseButton>
    </section>

    <LazySectionCreators
      id="creators"
      hydrate-on-visible
      data-observe-view="creators"
      data-section="creators"
      data-testid="creators"
      v-bind="{
        wrapperClass: '!bg-beige-100',
        tagline: t('lp.inspirations.creators.tagline'),
        headline: t('lp.inspirations.creators.headline'),
        buttonHref: $addLocaleToPath('lp.influencers'),
        carouselName: 'creators',
        items: creators
      }"
    />

    <LazyBaseDrawer
      v-if="currentProduct"
      v-model="isDrawerOpen"
      v-bind="{
        modernDrawer: true,
        customCloseButtonClass: 'bg-neutral-900 text-white hover:bg-neutral-800'
      }"
    >
      <div>
        <h2 class="semibold-32 mb-16">
          {{ t('lp.gallery.text-centered.title') }}
        </h2>

        <SectionUgcCard
          class="w-1/2 md:w-[288px]"
          v-bind="{
            trackData: {},
            item: currentProduct as Item,
            instagridImage: currentProduct?.preview || '',
            productName: currentProduct?.seoSlug || '',
            width: currentProduct?.width || 0,
            height: currentProduct?.height || 0,
            index: 0,
            shelfType: (currentProduct?.shelfType || 1) as SHELF_TYPE_ID,
            availableMaterials: getColorSwatchesForProductCategory(currentProduct?.shelfType as SHELF_TYPE_ID, currentProduct.category as Categories),
            url: currentProduct ? currentProduct.shelfType === 10
              ? GET_SOTTY_CONFIGURATOR_URL(currentProduct.id, currentProduct.seoSlug)
              : GET_SHELF_URL_INCLUDING_WEBVIEW(
                currentProduct.shelfType,
                currentProduct.physicalProductVersion,
                currentProduct.configuratorType,
                currentProduct.category,
                currentProduct.id,
                '',
                '',
                currentProduct.material
              ) : '',
            productUnrealImage: currentProduct?.preview || '',
            filterId: currentProduct?.id || 0,
            depth: currentProduct?.depth || 0,
            onSelectItemGA4Event: () => {}
          }"
        />
      </div>
    </LazyBaseDrawer>
  </main>
</template>

<script setup lang="ts">
import type { Swiper } from 'swiper';
import type { SwiperOptions } from 'swiper/types';
import type { Categories } from '~/consts/categories';
import type { YotpoAlbum, YotpoAlbumsResponse } from '~/types/yotpo';

import { GET_UGC_PRODUCT } from '~/api/pdp';
import { SHELF_TYPE_ID } from '~/consts/types';
import { FurnitureCategory } from '~/utils/filters';
import { creators } from '~/consts/homepage/creators';
import { SHORT_MODEL_TO_MODEL } from '~/utils/configuratorTypeToModel';

import TagIcon from '~/assets/icons/gallery/tag.svg';
import InstagramIcon from '~/assets/icons/gallery/instagram.svg';

const { t } = useI18n();
const { getColorSwatchesForProductCategory } = useColors();
const { GET_SHELF_URL_INCLUDING_WEBVIEW, GET_SOTTY_CONFIGURATOR_URL } = useProductUrl();

interface Item {
  category: Categories,
  configuratorType: ConfiguratorType,
  depth: number,
  height: number,
  id: number,
  material: number,
  omnibusPrice: number,
  physicalProductVersion: number,
  preview: string,
  regionPrice: number,
  regionPriceWithDiscount: number,
  seoSlug: string,
  shelfType: SHELF_TYPE_ID,
  width: number
}

const getAlbumCategoryPath = (albumName: string): string => {
  const normalizedName = albumName.toLowerCase();

  if (normalizedName.includes('all products')) {
    return FurnitureCategory.ALL;
  }

  if (normalizedName.includes('sideboard')) {
    return FurnitureCategory.SIDEBOARDS;
  }

  if (normalizedName.includes('wardrobe')) {
    return FurnitureCategory.WARDROBES;
  }

  if (normalizedName.includes('bookcase')) {
    return FurnitureCategory.BOOKCASES;
  }

  if (normalizedName.includes('wallstorage')) {
    return FurnitureCategory.WALL_STORAGE;
  }

  if (normalizedName.includes('chest')) {
    return FurnitureCategory.CHEST_OF_DRAWERS;
  }

  if (normalizedName.includes('vinyl_storage')) {
    return FurnitureCategory.VINYL_STORAGE;
  }

  if (normalizedName.includes('shoerack')) {
    return FurnitureCategory.SHOE_RACKS;
  }

  if (normalizedName.includes('tvstand')) {
    return FurnitureCategory.TV_STANDS;
  }

  if (normalizedName.includes('bedside_table')) {
    return FurnitureCategory.BEDSIDE_TABLE;
  }

  if (normalizedName.includes('desk')) {
    return FurnitureCategory.DESKS;
  }

  return 'default';
};

const currentPage = ref(1);
const albums = ref<YotpoAlbum[]>([]);
const allProductsAlbum = ref<YotpoAlbum>({
  name: 'All products',
  total: 0,
  images: [],
  viewMore: false
});
const selectedAlbum = ref<YotpoAlbum | null>(null);
const pending = ref(false);
const error = ref<string | null>(null);
const isSwiperInitialized = ref(false);
const currentProduct = ref<Item | null>(null);
const isDrawerOpen = ref(false);

const swiperOptions: SwiperOptions = {
  slidesPerView: 'auto',
  slidesPerGroupAuto: true,
  slideToClickedSlide: true,
  shortSwipes: false,
  freeMode: true,
  threshold: 4,
  on: {
    afterInit: (_swiper: Swiper) => {
      isSwiperInitialized.value = true;
    }
  }
};

const currentPhotos = computed(() => {
  return selectedAlbum.value?.images || [];
});

function updateAllProductsAlbum () {
  const totalCount = albums.value.reduce((acc, album) => {
    return acc + album.total;
  }, 0);

  const hasMorePages = albums.value.some(album => album.viewMore);

  allProductsAlbum.value.total = totalCount;
  allProductsAlbum.value.viewMore = hasMorePages;
}

const albumsWithAll = computed(() => {
  if (albums.value.length === 0) { return []; }

  return [allProductsAlbum.value, ...albums.value];
});

async function loadAlbums (page: number = 1) {
  pending.value = true;
  error.value = null;

  try {
    const response = await $fetch<YotpoAlbumsResponse>(`/nuxt-api/yotpo-albums?page=${page}`);

    if (response.success && response.albums) {
      if (page === 1) {
        albums.value = response.albums;
        allProductsAlbum.value.images = response.albums.reduce((acc, album) => {
          return acc.concat(album.images);
        }, [] as YotpoAlbum['images']);
      } else {
        response.albums.forEach((newAlbum) => {
          const existingAlbumIndex = albums.value.findIndex(album => album.name === newAlbum.name);

          if (existingAlbumIndex !== -1) {
            albums.value[existingAlbumIndex].images.push(...newAlbum.images);
            albums.value[existingAlbumIndex].total = newAlbum.total;
            albums.value[existingAlbumIndex].viewMore = newAlbum.viewMore;

            if (selectedAlbum.value?.name === 'All products') {
              allProductsAlbum.value.images.push(...newAlbum.images);
            }
          } else {
            albums.value.push(newAlbum);
          }
        });
      }

      updateAllProductsAlbum();

      if (albums.value.length > 0) {
        await nextTick();

        if (page === 1) {
          selectedAlbum.value = allProductsAlbum.value;
        }
      }
    } else {
      throw new Error('Failed to load albums');
    }
  } catch (err) {
    error.value = 'Failed to load albums. Please try again.';
    console.error('Error loading albums:', err);
  } finally {
    pending.value = false;
  }
}

await loadAlbums();

function loadMore () {
  const scrollPosition = window.scrollY;

  currentPage.value++;

  loadAlbums(currentPage.value).then(() => {
    nextTick(() => {
      window.scrollTo(0, scrollPosition);
    });
  });
}

function selectAlbum (album: YotpoAlbum) {
  selectedAlbum.value = album;
  currentPage.value = 1;
}

function getMasonryItemClass (index: number) {
  const position = index % 18;

  switch (position) {
    case 0: return 'masonry-item-1';
    case 1: return 'masonry-item-2';
    case 2: return 'masonry-item-3';
    case 3: return 'masonry-item-4';
    case 4: return 'masonry-item-5';
    case 5: return 'masonry-item-6';
    case 6: return 'masonry-item-7';
    case 7: return 'masonry-item-8';
    case 8: return 'masonry-item-9';
    case 9: return 'masonry-item-10';
    case 10: return 'masonry-item-11';
    case 11: return 'masonry-item-12';
    case 12: return 'masonry-item-13';
    case 13: return 'masonry-item-14';
    case 14: return 'masonry-item-15';
    case 15: return 'masonry-item-16';
    case 16: return 'masonry-item-17';
    case 17: return 'masonry-item-18';
    default: return 'masonry-item-3';
  }
}

function getMasonryItemStyle (index: number) {
  const cycle = Math.floor(index / 18);
  const rowOffset = cycle * 9;

  return { '--row-offset': rowOffset };
}

function getProductModel (productUrl: string) {
  const match = productUrl.match(/\/(\d+),([jws]),\//);

  return match ? match[2] : null;
}

async function handleShowProducts (photo: YotpoAlbum['images'][number]) {
  const productModel = getProductModel(photo.taggedProducts[0].product_link);
  const properModel = SHORT_MODEL_TO_MODEL(productModel as 'j' | 'w' | 's');

  const data = await GET_UGC_PRODUCT(photo.taggedProducts[0].domain_key.split('-')[0], properModel as FurnitureModel);

  if (data) {
    const product = data as Item;

    currentProduct.value = {
      ...product,
      price: product.regionPrice?.toString() || '0',
      variant: [],
      trackingList: '',
      furnitureType: properModel,
      omnibusPrice: product.regionPriceWithDiscount || 0,
      regionPriceWithDiscount: product.regionPriceWithDiscount || 0
    } as Item;
  } else {
    console.error(error);
  }

  isDrawerOpen.value = true;
}
</script>

<style lang="scss" scoped>
$grid-gap: 16px;
$mobile-gap: 24px;
$mobile-breakpoint: 1023px;

$aspect-large: 600/716;
$aspect-medium: 292/384;
$aspect-small: 292/307;
$aspect-mobile-odd: 361/430;
$aspect-mobile-even: 361/495;

@mixin grid-position($column, $row, $column-span: 1, $row-span: 1) {
  grid-column: #{$column} #{'/'} span #{$column-span};
  grid-row: calc(#{$row} + var(--row-offset, 0)) #{'/'} span #{$row-span};
}

@mixin masonry-item($column, $row, $aspect-ratio, $column-span: 1, $row-span: 1) {
  @include grid-position($column, $row, $column-span, $row-span);
  aspect-ratio: $aspect-ratio;
}

.masonry-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: auto;
  gap: $grid-gap;

  @media (max-width: $mobile-breakpoint) {
    grid-template-columns: 1fr;
    gap: $mobile-gap;

    article {
      grid-column: 1;
      grid-row: auto;

      &:nth-child(odd) {
        aspect-ratio: $aspect-mobile-odd;
      }

      &:nth-child(even) {
        aspect-ratio: $aspect-mobile-even;
      }
    }
  }
}

.masonry-item-1 {
  @include masonry-item(1, 1, $aspect-large, 2, 3);
}

.masonry-item-2 {
  @include masonry-item(3, 1, $aspect-medium, 1, 2);
}

.masonry-item-3 {
  @include masonry-item(4, 1, $aspect-small);
}

.masonry-item-4 {
  @include masonry-item(3, 3, $aspect-small);
}

.masonry-item-5 {
  @include masonry-item(4, 2, $aspect-medium, 1, 2);
}

.masonry-item-6 {
  @include masonry-item(1, 4, $aspect-small);
}

.masonry-item-7 {
  @include masonry-item(2, 4, $aspect-medium, 1, 2);
}

.masonry-item-8 {
  @include masonry-item(3, 4, $aspect-small);
}

.masonry-item-9 {
  @include masonry-item(4, 4, $aspect-medium, 1, 2);
}

.masonry-item-10 {
  @include masonry-item(1, 5, $aspect-medium, 1, 2);
}

.masonry-item-11 {
  @include masonry-item(2, 6, $aspect-small);
}

.masonry-item-12 {
  @include masonry-item(3, 5, $aspect-medium, 1, 2);
}

.masonry-item-13 {
  @include masonry-item(4, 6, $aspect-small);
}

.masonry-item-14 {
  @include masonry-item(1, 7, $aspect-medium, 1, 2);
}

.masonry-item-15 {
  @include masonry-item(2, 7, $aspect-small);
}

.masonry-item-16 {
  @include masonry-item(3, 7, $aspect-large, 2, 3);
}

.masonry-item-17 {
  @include masonry-item(2, 8, $aspect-medium, 1, 2);
}

.masonry-item-18 {
  @include masonry-item(1, 9, $aspect-small);
}
</style>
