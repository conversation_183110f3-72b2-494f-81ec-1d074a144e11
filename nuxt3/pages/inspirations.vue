<template>
  <main class="bg-beige-100 reset-modern-navigation-page-padding">
    <Head>
      <Title>{{ t('lp.inspirations.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        :content="t('lp.inspirations.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        :content="t('lp.inspirations.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        :content="t('lp.inspirations.meta.description')"
      />
    </Head>

    <SectionHeroImage
      data-section="hero"
      v-bind="{
        variant: 'center',
        containerAdditionalClasses: 'bg-gradient-to-b from-transparent to-black/50',
        imagePath: 'lp/inspirations/hero',
        imageAlt: t('lp.inspirations.hero.image_alt'),
        imageType: 'M T SD LD XLD',
        headingCopy: t('lp.inspirations.hero.headline'),
        subheadingCopy: t('lp.inspirations.hero.subheading'),
        headerColorClass: 'text-offwhite-600',
        additionalClassHeading: 'xl:semibold-54',
        additionalClassSubheading: 'xl:!semibold-18 xl:!mb-8',
        additionalTextClass: 'xl:normal-18 xl:!mt-8',
        backgroundColor: '#d8d1cc',
      }"
    >
      <template #ctaSlot>
        <div class="flex flex-row flex-wrap gap-16 justify-center mt-32">
          <BaseLink
            variant="filled-dark"
            class="py-12 px-20 lg:py-20 lg:px-32 rounded-full text-center"
            data-testid="reviews-add-cta"
            v-bind="{
              href: $addLocaleToPath('lp.influencers'),
              trackData: {
                eventType: 'NOEEC',
                eventCategory: 'lp_clicks',
                eventAction: 'Inspirations_creators',
                eventLabel: 'Write a review'
              }
            }"
          >
            {{ t('lp.inspirations.hero.cta_creators') }}
          </BaseLink>
          <BaseLink
            variant="filled-dark"
            class="py-12 px-20 lg:py-20 lg:px-32 rounded-full text-center"
            data-testid="reviews-add-cta"
            v-bind="{
              href: $addLocaleToPath('lp.gallery'),
              trackData: {
                eventType: 'NOEEC',
                eventCategory: 'lp_clicks',
                eventAction: 'Inspirations_shop',
                eventLabel: 'Write a review'
              }
            }"
          >
            {{ t('lp.inspirations.hero.cta_shop') }}
          </BaseLink>
          <BaseLink
            variant="filled-dark"
            class="py-12 px-20 lg:py-20 lg:px-32 rounded-full text-center"
            data-testid="reviews-add-cta"
            v-bind="{
              href: $addLocaleToPath('lp.reviews'),
              trackData: {
                eventType: 'NOEEC',
                eventCategory: 'lp_clicks',
                eventAction: 'Inspirations_reviews',
                eventLabel: 'Write a review'
              }
            }"
          >
            {{ t('lp.inspirations.hero.cta_reviews') }}
          </BaseLink>
        </div>
      </template>
    </SectionHeroImage>

    <LazySectionCreators
      id="creators"
      hydrate-on-visible
      data-observe-view="creators"
      data-section="creators"
      data-testid="creators"
      v-bind="{
        wrapperClass: '!bg-beige-100',
        tagline: t('lp.inspirations.creators.tagline'),
        headline: t('lp.inspirations.creators.headline'),
        buttonHref: $addLocaleToPath('lp.influencers'),
        carouselName: 'creators',
        items: creators
      }"
    />

    <section class="bg-beige-200 lg:py-96 py-48">
      <div class="grid-container">
        <div class="flex flex-row flex-wrap gap-y-16 justify-between items-center mb-48">
          <div>
            <h3 class="mb-16 uppercase semibold-14 xl:semibold-18 md:mb-24">
              {{ t('lp.inspirations.ugc.tagline') }}
            </h3>
            <h2 class="semibold-28 md:semibold-32 lg:semibold-46 xl:semibold-54 max-w-[1080px]">
              {{ t('lp.inspirations.ugc.headline') }}
            </h2>
          </div>
          <BaseLink
            variant="outlined"
            v-bind="{
              trackData: { eventLabel: 'cta' },
              href: $addLocaleToPath('lp.gallery')
            }"
          >
            {{ t('common.view_all') }}
          </BaseLink>
        </div>

        <div
          v-if="currentPhotos.length > 0"
          class="masonry-grid mt-24"
        >
          <article
            v-for="(photo, index) in currentPhotos.slice(0, 8)"
            :key="photo?.id"
            :class="getMasonryItemClass(index)"
            :style="getMasonryItemStyle(index)"
            class="rounded-12 overflow-hidden relative cursor-pointer"
            v-on:click="handleShowProducts(photo)"
          >
            <aside class="absolute bottom-16 right-16 flex flex-col items-end gap-8">
              <a
                :href="`http://instagram.com/_u/${photo?.username}`"
                target="_blank"
                class="bg-beige-100 rounded-full flex items-center justify-center cursor-pointer p-12 transition-all duration-300 ease-out overflow-hidden group"
              >
                <InstagramIcon class="w-24 h-24 min-w-24 min-h-24 transition-[margin] duration-300 ease-out group-hover:mr-8" />
                <span
                  class="opacity-0 max-w-0 transition-[opacity,max-width] duration-300 ease-out normal-14 text-neutral-900 whitespace-nowrap overflow-hidden
                group-hover:opacity-100 group-hover:max-w-[120px]"
                >{{ photo?.username }}</span>
              </a>

              <div
                class="bg-beige-100 rounded-full flex items-center justify-center cursor-pointer p-12 transition-all duration-300 ease-out overflow-hidden group"
                v-on:click="handleShowProducts(photo)"
              >
                <TagIcon class="w-24 h-24 min-w-24 min-h-24 transition-[margin] duration-300 ease-out group-hover:mr-8" />
                <span
                  class="opacity-0 max-w-0 text-nowrap transition-[opacity,max-width] duration-300 ease-out normal-14 text-neutral-900 overflow-hidden
                group-hover:opacity-100 group-hover:max-w-[160px]"
                >{{ t('lp.gallery.show_products') }}</span>
              </div>
            </aside>
            <img
              v-if="photo?.mediaType === 'image'"
              :src="photo.mediumUrl"
              :alt="photo.caption || `Photo by ${photo?.username || ''}`"
              class="w-full h-full object-cover transition-transform transition-basic"
              loading="lazy"
            >
            <video
              v-else-if="photo?.mediaType === 'video'"
              :poster="photo.thumbnailUrl"
              class="w-full h-full object-cover"
              controls
            >
              <source
                :src="photo.videoUrl"
                type="video/mp4"
              >
            </video>
          </article>
        </div>
      </div>
    </section>

    <LazyPdpReviews
      hydrate-on-visible
      data-observe-view="Reviews"
      data-testid="pdp-reviews"
      v-bind="{
        allProducts: true
      }"
    />

    <LazyBaseDrawer
      v-if="currentProduct"
      v-model="isDrawerOpen"
      v-bind="{
        modernDrawer: true,
        customCloseButtonClass: 'bg-neutral-900 text-white hover:bg-neutral-800'
      }"
    >
      <div class="p-48">
        <h2 class="semibold-32 mb-16">
          {{ t('lp.gallery.text-centered.title') }}
        </h2>

        <SectionMinigridCard
          class="w-[288px]"
          v-bind="{
            trackData: {},
            item: currentProduct as Item,
            instagridImage: currentProduct?.preview || '',
            productName: currentProduct?.seoSlug || '',
            width: currentProduct?.width || 0,
            height: currentProduct?.height || 0,
            index: 0,
            shelfType: (currentProduct?.shelfType || 1) as SHELF_TYPE_ID,
            availableMaterials: getColorSwatchesForProductCategory(currentProduct?.shelfType as SHELF_TYPE_ID, currentProduct.category as Categories),
            url: currentProduct ? currentProduct.shelfType === 10
              ? GET_SOTTY_CONFIGURATOR_URL(currentProduct.id, currentProduct.seoSlug)
              : GET_SHELF_URL_INCLUDING_WEBVIEW(
                currentProduct.shelfType,
                currentProduct.physicalProductVersion,
                currentProduct.configuratorType,
                currentProduct.category,
                currentProduct.id,
                '',
                '',
                currentProduct.material
              ) : '',
            productUnrealImage: currentProduct?.preview || '',
            filterId: currentProduct?.id || 0,
            depth: currentProduct?.depth || 0,
            onSelectItemGA4Event: () => {}
          }"
        />
      </div>
    </LazyBaseDrawer>
  </main>
</template>

<script setup lang="ts">
import type { YotpoAlbum, YotpoAlbumsResponse } from '~/types/yotpo';
import type { Categories } from '~/consts/categories';

import { creators } from '~/consts/homepage/creators';

import { GET_UGC_PRODUCT } from '~/api/pdp';
import { SHELF_TYPE_ID } from '~/consts/types';
import { SHORT_MODEL_TO_MODEL } from '~/utils/configuratorTypeToModel';

import TagIcon from '~/assets/icons/gallery/tag.svg';
import InstagramIcon from '~/assets/icons/gallery/instagram.svg';

const { t } = useI18n();
const { getColorSwatchesForProductCategory } = useColors();
const { GET_SHELF_URL_INCLUDING_WEBVIEW, GET_SOTTY_CONFIGURATOR_URL } = useProductUrl();

interface Item {
  category: Categories,
  configuratorType: ConfiguratorType,
  depth: number,
  height: number,
  id: number,
  material: number,
  omnibusPrice: number,
  physicalProductVersion: number,
  preview: string,
  regionPrice: number,
  regionPriceWithDiscount: number,
  seoSlug: string,
  shelfType: SHELF_TYPE_ID,
  width: number
}

const pending = ref(false);
const isDrawerOpen = ref(false);
const albums = ref<YotpoAlbum[]>([]);
const error = ref<string | null>(null);
const currentProduct = ref<Item | null>(null);
const selectedAlbum = ref<YotpoAlbum | null>(null);

const currentPhotos = computed(() => {
  return selectedAlbum.value?.images || [];
});

const allProductsAlbum = computed((): YotpoAlbum => {
  const allImages = albums.value.reduce((acc, album) => {
    return acc.concat(album.images);
  }, [] as YotpoAlbum['images']);

  const totalCount = albums.value.reduce((acc, album) => {
    return acc + album.total;
  }, 0);

  return {
    name: 'All products',
    total: totalCount,
    images: allImages
  };
});

async function loadAlbums (page: number = 1) {
  pending.value = true;
  error.value = null;

  try {
    const response = await $fetch<YotpoAlbumsResponse>(`/nuxt-api/yotpo-albums?page=${page}`);

    if (response.success && response.albums) {
      albums.value = response.albums;

      if (albums.value.length > 0) {
        await nextTick();
        selectedAlbum.value = allProductsAlbum.value;
      }
    } else {
      throw new Error('Failed to load albums');
    }
  } catch (err) {
    error.value = 'Failed to load albums. Please try again.';
    console.error('Error loading albums:', err);
  } finally {
    pending.value = false;
  }
}

await loadAlbums();

function getMasonryItemClass (index: number) {
  const position = index % 18;

  switch (position) {
    case 0: return 'masonry-item-1';
    case 1: return 'masonry-item-2';
    case 2: return 'masonry-item-3';
    case 3: return 'masonry-item-4';
    case 4: return 'masonry-item-5';
    case 5: return 'masonry-item-6';
    case 6: return 'masonry-item-7';
    case 7: return 'masonry-item-8';
  }
}

function getMasonryItemStyle (index: number) {
  const cycle = Math.floor(index / 8);
  const rowOffset = cycle * 9;

  return { '--row-offset': rowOffset };
}

function getProductModel (productUrl: string) {
  const match = productUrl.match(/\/(\d+),([jws]),\//);

  return match ? match[2] : null;
}

async function handleShowProducts (photo: YotpoAlbum['images'][number]) {
  const productModel = getProductModel(photo.taggedProducts[0].product_link);
  const properModel = SHORT_MODEL_TO_MODEL(productModel as 'j' | 'w' | 's');

  const data = await GET_UGC_PRODUCT(photo.taggedProducts[0].domain_key.split('-')[0], properModel as FurnitureModel);

  if (data) {
    const product = data as Item;

    currentProduct.value = {
      ...product,
      price: product.regionPrice?.toString() || '0',
      variant: [],
      trackingList: '',
      omnibusPrice: product.regionPriceWithDiscount || 0
    } as Item;
  } else {
    console.error(error);
  }

  isDrawerOpen.value = true;
}
</script>

<style lang="scss" scoped>
$grid-gap: 16px;
$mobile-gap: 24px;
$mobile-breakpoint: 1023px;

$aspect-medium: 292/384;
$aspect-small: 292/307;

@mixin grid-position($column, $row, $column-span: 1, $row-span: 1) {
  grid-column: #{$column} #{'/'} span #{$column-span};
  grid-row: calc(#{$row} + var(--row-offset, 0)) #{'/'} span #{$row-span};
}

@mixin masonry-item($column, $row, $aspect-ratio, $column-span: 1, $row-span: 1) {
  @include grid-position($column, $row, $column-span, $row-span);
  aspect-ratio: $aspect-ratio;
}

.masonry-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: auto;
  gap: $grid-gap;

  @media (max-width: $mobile-breakpoint) {
    grid-template-columns: 1fr;
    gap: $mobile-gap;

    article {
      grid-column: 1;
      grid-row: auto;

      &:nth-child(odd) {
        aspect-ratio: $aspect-medium;
      }

      &:nth-child(even) {
        aspect-ratio: $aspect-small;
      }
    }
  }
}

.masonry-item-1 {
  @include masonry-item(1, 1, $aspect-medium, 1, 2);
}

.masonry-item-2 {
  @include masonry-item(2, 1, $aspect-small);
}

.masonry-item-3 {
  @include masonry-item(3, 1, $aspect-medium, 1, 2);
}

.masonry-item-4 {
  @include masonry-item(4, 1, $aspect-small);
}

.masonry-item-5 {
  @include masonry-item(1, 3, $aspect-small);
  @apply md-max:hidden;
}

.masonry-item-6 {
  @include masonry-item(2, 2, $aspect-medium, 1, 2);
  @apply md-max:hidden;
}

.masonry-item-7 {
  @include masonry-item(3, 3, $aspect-small);
  @apply md-max:hidden;
}

.masonry-item-8 {
  @include masonry-item(4, 2, $aspect-medium, 1, 2);
  @apply md-max:hidden;
}
</style>
