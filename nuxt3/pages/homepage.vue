<template>
  <main class="reset-modern-navigation-page-padding">
    <Head>
      <Title>{{ $t('hp.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        :content="$t('hp.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        :content="$t('hp.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        :content="$t('hp.meta.description')"
      />
    </Head>

    <div data-dark-navigation>
      <LazySectionVideo
        id="hp-hero-video"
        hydrate-on-visible
        data-observe-view="hero-video"
        data-section="hero-video"
        data-testid="hero-video"
        has-sound
        fill-viewport
        disable-lazy
        v-bind="{
          provider: 'cloudflare',
          videoPlaceHolder: 'homepage/video-placeholders/halloween2025',
          placeholderType: 'M D',
          videoParams:{
            videoId: {
              mobile: '862027dbb3d2bd48cbd6e6daee2b47b4',
              desktop: 'f3897aa34e878bb5b5eb8bde800c3cef'
            },
            aspectRatio: {
              mobile: '758/1056',
              desktop: '2880/1472'
            }
          },
          ctaUrl: $addLocaleToPath('plp'),
          ctaCopy: $t('hp.hero_cta.halloween'),
          title: $t('hp.hero_headline.halloween')
        }"
      />

      <LazySectionButtonCategories hydrate-on-visible />
    </div>

    <LazySectionVideo
      id="hp-brand-video"
      hydrate-on-visible
      data-light-navigation
      data-observe-view="brand-video"
      data-testid="brand-video"
      data-section="brand-video"
      v-bind="{
        videoParams:{
          videoId: {
            mobile: 'l8a027mrx5',
            desktop: 'ttz1slufs7'
          },
          aspectRatio: {
            mobile: '1178/1768',
            desktop: '1920/960'
          }
        },
        videoPlaceHolder: 'homepage/video-placeholders/sofa-brand',
        placeholderType: 'M D'
      }"
    />

    <LazySectionChooseLine
      id="hp-choose-line"
      hydrate-on-visible
      data-dark-navigation
      data-observe-view="choose-line"
      data-section="choose-line"
      data-testid="choose-line"
      v-bind="{
        tagline: $t('hp.choose_line.tagline'),
        headline: $t('hp.choose_line.headline'),
        carouselName: 'choose-line',
        buttonHref: $addLocaleToPath('plp')
      }"
    />

    <LazySectionBrandImage
      id="hp-brand-image"
      hydrate-on-visible
      data-observe-view="brand-image"
      data-section="brand-image"
      data-testid="brand-image"
      data-light-navigation
      v-bind="{
        imagePath: 'homepage/brand-image/leathersofa25',
        imageType: 'M T SD LD',
        title: $t('hp.ecomm_image.title'),
        description: $t('hp.ecomm_image.subtitle'),
        additionalCtaCopy: $t('hp.ecomm_image.cta'),
        additionalCtaUrl: $addLocaleToPath('change-category'),
        ctaCopy: $t('hp.brand_image.cta'),
        ctaUrl: $addLocaleToPath('plp') + $t('common.category.smooth.all.plp_url'),
        aspectRatio: {
          mobile: '393/590',
          desktop: '1536/768'
        }
      }"
    />

    <div data-dark-navigation>
      <SectionMinigrid
        hydrate-on-visible
        data-observe-view="hp-minigrid"
        data-section="hp-minigrid"
        data-testid="hp-minigrid"
        class="bg-beige-100 lg:!py-96"
        v-bind="{
          id: 'hp_minigrid-browse-by-room',
          itemListName: 'mini_grid',
          boardId: 'hp_rooms_living-room',
          additionalClasses: '!mt-0'
        }"
      />

      <section>
        <div
          class="py-48 md:py-64 xl:py-96 bg-beige-200"
          data-testid="hp-brand-highlights"
        >
          <div class="grid-container">
            <p
              class="uppercase semibold-12 text-neutral-750 mb-8"
              data-testid="hp-brand-highlights-subtitle"
            >
              {{ $t('hp.brand_highlights.subtitle') }}
            </p>
            <h3
              class="semibold-28 lg:semibold-44 xl:semibold-54 text-neutral-900 mb-32 lg:mb-48 max-w-[635px]"
              data-testid="hp-brand-highlights-title"
            >
              {{ $t('hp.brand_highlights.title') }}
            </h3>
          </div>

          <SectionFourTiles data-testid="hp-brand-highlights-tiles">
            <template #tile1>
              <CardDesign
                data-testid="hp-brand-highlights-tile1"
                v-bind="{
                  imgPath: 'homepage/quality/1',
                  shouldDisplayIndex: true,
                  imgAlt: $t('hp.brand_highlights.carousel.title1'),
                  imgType: 'M T SD LD XLD',
                  index: 1,
                  heading: $t('hp.brand_highlights.carousel.title1'),
                  copy: $t('hp.brand_highlights.carousel.body1')
                }"
              />
            </template>
            <template #tile2>
              <CardDesign
                data-testid="hp-brand-highlights-tile2"
                v-bind="{
                  imgPath: 'homepage/quality/2',
                  shouldDisplayIndex: true,
                  imgAlt: $t('hp.brand_highlights.carousel.title2'),
                  imgType: 'M T SD LD XLD',
                  index: 2,
                  heading: $t('hp.brand_highlights.carousel.title2'),
                  copy: $t('hp.brand_highlights.carousel.body2')
                }"
              />
            </template>
            <template #tile3>
              <CardDesign
                data-testid="hp-brand-highlights-tile3"
                v-bind="{
                  imgPath: 'homepage/quality/3',
                  shouldDisplayIndex: true,
                  imgAlt: $t('hp.brand_highlights.carousel.title3'),
                  imgType: 'M T SD LD XLD',
                  index: 3,
                  heading: $t('hp.brand_highlights.carousel.title3'),
                  copy: $t('hp.brand_highlights.carousel.body3')
                }"
              />
            </template>
            <template #tile4>
              <CardDesign
                data-testid="hp-brand-highlights-tile4"
                v-bind="{
                  imgPath: 'homepage/quality/4',
                  shouldDisplayIndex: true,
                  imgAlt: $t('hp.brand_highlights.carousel.title4'),
                  imgType: 'M T SD LD XLD',
                  index: 4,
                  heading: $t('hp.brand_highlights.carousel.title4'),
                  copy: $t('hp.brand_highlights.carousel.body4')
                }"
              />
            </template>
          </SectionFourTiles>
        </div>
      </section>

      <LazySectionCreators
        id="hp-creators"
        hydrate-on-visible
        data-observe-view="creators"
        data-section="creators"
        data-testid="hp-creators"
        v-bind="{
          tagline: $t('hp.creators.tagline'),
          headline: $t('hp.creators.headline'),
          buttonHref: $addLocaleToPath('lp.influencers'),
          carouselName: 'creators',
          items: creators
        }"
      />
    </div>

    <LazySectionSustainability
      id="hp-sustainability"
      hydrate-on-visible
      data-light-navigation
      data-observe-view="sustainability"
      data-section="sustainability"
      data-testid="hp-sustainability"
      v-bind="{
        imagePath: 'homepage/sustainability',
        imageType: 'M T SD LD',
        title: $t('hp.sustainability.title'),
        description: $t('hp.sustainability.description'),
        ctaCopy: $t('hp.sustainability.cta'),
        ctaUrl: '/Tylko_ESG_report_digital_EN_19.pdf'
      }"
    />

    <div data-dark-navigation>
      <LazySectionBrandImage
        id="showrooms-section"
        class="py-[50px] md:py-96"
        hydrate-on-visible
        data-observe-view="showrooms-section"
        data-section="showrooms-section"
        data-testid="showrooms-section"
        v-bind="{
          imagePath: 'homepage/showrooms-section',
          imageType: 'M T SD LD',
          title: $t('hp_showrooms_section_headline'),
          description: $t('hp_showrooms_section_description'),
          ctaCopy: $t('hp_showrooms_section_link'),
          ctaUrl: $addLocaleToPath('showrooms'),
          aspectRatio: {
            mobile: '393/590',
            desktop: '1536/768'
          }
        }"
      />

      <LazySectionMinigridRecommedation
        hydrate-on-visible
        campaign-id="zsZ7MAX6USCh"
        :title="$t('homepage.recommendations.title')"
        :label="$t('common.see_also')"
        data-observe-view="section-recommendation"
        data-section="section-recommendation"
        data-testid="section-recommendation"
        class="bg-beige-100"
      />

      <LazySectionLifeOfTylko
        id="hp-life-of-tylko"
        hydrate-on-visible
        data-observe-view="life-of-tylko"
        data-section="life-of-tylko"
        data-testid="hp-life-of-tylko"
        v-bind="{
          tagline: $t('hp.life_of_tylko.tagline'),
          headline: $t('hp.life_of_tylko.headline'),
          carouselName: 'life-of-tylko',
          items:[
            {
              id: 1,
              title: $t('hp.life_of_tylko.title1'),
              description: $t('hp.life_of_tylko.description1'),
              picturePath: 'homepage/lifeOfTylko/1',
              trackLabel: 'Life of Tylko 1'
            },
            {
              id: 2,
              title: $t('hp.life_of_tylko.title2'),
              description: $t('hp.life_of_tylko.description2'),
              picturePath: 'homepage/lifeOfTylko/2',
              trackLabel: 'Life of Tylko 2'
            },
            {
              id: 3,
              title: $t('hp.life_of_tylko.title3'),
              description: $t('hp.life_of_tylko.description3'),
              picturePath: 'homepage/lifeOfTylko/3',
              trackLabel: 'Life of Tylko 3'
            },
            {
              id: 4,
              title: $t('hp.life_of_tylko.title4'),
              description: $t('hp.life_of_tylko.description4'),
              picturePath: 'homepage/lifeOfTylko/4',
              href: $addLocaleToPath('lp.colab-andy'),
              trackLabel: 'Life of Tylko 4'
            }
          ]
        }"
        v-on:cta-action="(id) => handleLifeOfTylkoCtaAction(id)"
      />
    </div>

    <ClientOnly>
      <LazyBaseModal
        v-if="isModalOpen"
        v-model="isModalOpen"
        :hydrate-when="isModalOpen"
        hydrate-on-visible
      >
        <DialogPanel class="fixed top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 w-[90%] max-w-[768px] max-h-[90vh] bg-white rounded-24 overflow-hidden z-1">
          <button
            class="box-content absolute top-0 right-0 p-12 group z-1 custom"
            v-on:click="isModalOpen = false"
          >
            <IconClose class="w-32 h-32 transition-rotate basic-transition hover:-rotate-45" />
          </button>
          <div class="flex flex-col lg:flex-row">
            <BasePicture
              picture-classes="w-full h-full lg:min-w-[480px]"
              type="M D"
              disable-lazy
              loading="lazy"
              data-testid="life-of-tylko-modal-picture"
              v-bind="{
                imgClasses: ['w-full h-full'],
                path: `homepage/lifeOfTylko/modal/${modalId}`,
                alt: $t('hp.life_of_tylko.modal.title' + modalId)
              }"
            />
            <aside class="py-24 lg:py-96 px-16 lg:px-24">
              <h2 class="semibold-20 text-neutral-900">
                {{ $t('hp.life_of_tylko.popup' + modalId + '.title') }}
              </h2>
              <p class="normal-16 text-neutral-900 mt-16">
                {{ $t('hp.life_of_tylko.popup' + modalId + '.description') }}
              </p>
            </aside>
          </div>
        </DialogPanel>
      </LazyBaseModal>
    </ClientOnly>
  </main>
</template>

<script lang="ts" setup>
import { DialogPanel } from '@headlessui/vue';
import { useTrackSectionView } from '~/composables/useTracking';
import { trustPilotScript } from '~/consts/head';
import { creators } from '~/consts/homepage/creators';

const { $addLocaleToPath } = useNuxtApp();

const modalId = ref(0);
const isModalOpen = ref(false);
const i18n = useI18n();
const { IS_SMOOTH_AVAILABLE } = storeToRefs(useGlobal());
const plpLink = computed(() => IS_SMOOTH_AVAILABLE.value
  ? `${$addLocaleToPath('plp')}${i18n.t('common.category.smooth.all.plp_url')}/`
  : `${$addLocaleToPath('plp')}`);

const handleLifeOfTylkoCtaAction = (id: number) => {
  isModalOpen.value = true;
  modalId.value = id;
};

const { GLOBAL_PROMO } = storeToRefs(usePromoStore());
const globalPromoValue = computed(() => parseInt(GLOBAL_PROMO.value?.value));

useHead({ script: [trustPilotScript] });

useTrackSectionView('hp_section_view', 'view');
const headerStore = useHeaderStore();
let scrollTriggerInstance;

onMounted(async () => {
  const [{ gsap }, { ScrollTrigger }] = await Promise.all([
    import('gsap'),
    import('gsap/ScrollTrigger')
  ]);
  gsap.registerPlugin(ScrollTrigger);
  scrollTriggerInstance = ScrollTrigger;
  const start = `top ${headerStore.ribbonHeight + 50}px`;
  const darkTriggers = document.querySelectorAll('[data-dark-navigation]');

  darkTriggers.forEach((trigger) => {
    gsap.timeline({
      scrollTrigger: {
        trigger,
        start,
        onEnter: self => headerStore.setIsDarkNavigation(true),
        onLeaveBack: self => headerStore.setIsDarkNavigation(false)
      }
    });
  });

  const lightTriggers = document.querySelectorAll('[data-light-navigation]');

  lightTriggers.forEach((trigger) => {
    gsap.timeline({
      scrollTrigger: {
        trigger,
        start,
        onEnter: self => headerStore.setIsDarkNavigation(false),
        onLeaveBack: self => headerStore.setIsDarkNavigation(true)
      }
    });
  });
});

onBeforeUnmount(() => {
  headerStore.setIsDarkNavigation(false);

  if (scrollTriggerInstance) {
    scrollTriggerInstance.killAll();
  }
});

</script>
