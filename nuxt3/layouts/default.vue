<template>
  <main
    id="default-layout"
    class="[--navigation-modern-padding:88px] md:[--navigation-modern-padding:96px]"
    v-bind="{ style: `--ribbon-height:${ribbonHeight}px` }"
  >
    <TheHeader :variant="themeVariant" />

    <NuxtPage class="modern-navigation-page-padding" />

    <slot name="content" />

    <template v-if="isNewsletterVisible">
      <LazySectionNewsletter hydrate-on-visible />
    </template>

    <LazyTheFooter2025 hydrate-on-visible />

    <ClientOnly>
      <TheGlobal />
      <UiNewDixaButton />
      <UiNewDixaWrapper />
    </ClientOnly>

    <ScartLazyLoader />

    <nav id="feed">
      <div class="content-card-background" />
    </nav>
  </main>
</template>

<script setup lang="ts">
const { isNewsletterVisible } = useNewsletter();
useI18SeoHead();
const route = useRoute();
const getRouteBaseName = useRouteBaseName();
const routeBaseName = computed(() => getRouteBaseName(route) || '');
const { ribbonHeight } = storeToRefs(useHeaderStore());
const themeVariant = computed(() => {
  const routesWithLightTheme = ['sofa-teaser', 'lp-review-list', 'inspirations'];
  const routesWithTransparentTheme = ['homepage'];

  if (routesWithLightTheme.includes(routeBaseName.value)) {
    return 'light';
  } else if (routesWithTransparentTheme.includes(routeBaseName.value)) {
    return 'transparent';
  }

  return 'dark';
});

</script>
