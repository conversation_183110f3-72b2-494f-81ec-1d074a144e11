const IS_DEV_BUILD = process.env.NODE_ENV === 'development';
const nitro = IS_DEV_BUILD ? {} : {
  publicAssets: [
    {
      baseURL: 'nuxt3-statics',
      dir: 'public/nuxt3-statics',
      maxAge: 60 * 60 * 24 * 30 // 30 days
    }
  ]
};

const hooks = {
  'build:manifest': (manifest: any) => {
    for (const key in manifest) {
      const item = manifest[key];

      if (item.isEntry ||
        item.resourceType === 'style' ||
        item.file.includes('entry.')) {
      } else {
        item.dynamicImports = [];
        item.preload = false;
        item.prefetch = false;
        item.css = [];
      }
    }
  }
};

export { hooks, nitro };
