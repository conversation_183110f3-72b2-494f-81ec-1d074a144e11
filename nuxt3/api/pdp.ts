import { useApi } from '~/composables/useApi';

export const INITIAL = (productModel: TYPE_MODEL, productID: string, locale: string, path: string) => useApi(`/api/v1/${productModel}_pdp/${productID}/`, {
  method: 'get',
  headers: {
    'Accept-Language': locale,
    referer: path
  }
});

export const INITIAL_PRELOAD = (productModel: TYPE_MODEL, productID: string, locale: string, path: string) => useApi(`/api/v1/${productModel}_configurator_preload/${productID}/`, {
  method: 'get',
  headers: {
    'Accept-Language': locale,
    referer: path
  }
});

export const INITIAL_SOFA = (productID: string, locale: string, path: string) => useApi(`/api/v1/sotty_pdp/${productID}/`, {
  method: 'get',
  headers: {
    'Accept-Language': locale,
    referer: path
  }
});
export const INITIAL_SKU = (skuId: number, locale: string, path: string, regionName: string) => useApi(`/api/v1/skus/sku-variants/${skuId}/?regionName=${regionName}`, {
  method: 'get',
  headers: {
    'Accept-Language': locale,
    referer: path
  }
});

export const COVERS = (productID: string, locale: string, path: string) => useApi(`/api/v1/sotty_pdp/single_modules/${productID}/`, {
  headers: {
    'Accept-Language': locale,
    referer: path
  }
});

export const ADD_TO_CART = (productModel: TYPE_MODEL, item: object) => useApi(`/api/v1/gallery/${productModel}/add_to_cart/`, {
  method: 'post',
  body: item
});

export const ECO_TAX = ({ geom, model }: any) => useApi(`/api/v1/gallery/${model}/check_recycle_tax/`, {
  method: 'post',
  body: geom
});

export const PROMO = async (furnitureType: string, material: number, shelfType: number, shelfCategory: string, region: string) =>
  await useApi(`/api/v1/global_promo/discount/?furniture_type=${furnitureType}&material=${material}&shelf_type=${shelfType}&shelf_category=${shelfCategory}&region=${region}`);

export const ADD_TO_WISHLIST = ({
  furnitureModel,
  geom,
  screenshot
}: {
  furnitureModel: string,
  geom: object,
  screenshot: string
}) => $fetch<{
  id: number,
  category: string,
  configurator_type: number,
  furniture_type: FurnitureModel,
}>(`/api/v1/gallery/${furnitureModel}/add_to_wishlist/?mm=true`, {
  method: 'post',
  body: {
    geom,
    magic_preview: screenshot.split(',')[1] || screenshot
  }
});

interface S4LForm {
  furnitureModel: string,
  marketingPermission: boolean,
  screenshot: string,
  geom: object,
  email: string,
  formSrc: string,
  base_preset?: number,
  productId?: string
}

export const S4L_SUBMIT_FORM = ({
  furnitureModel,
  geom,
  screenshot,
  email,
  marketingPermission,
  formSrc,
  base_preset,
  productId
}: S4LForm) => $fetch<{
  category: string,
  seo_slug: string,
  id: number
}>(`/api/v1/gallery/${furnitureModel}/${productId ? productId + '/' : ''}add_to_wishlist_popup/`, {
  method: 'post',
  body: {
    email,
    geom,
    marketing_permission: marketingPermission,
    magic_preview: screenshot.split(',')[1] || screenshot,
    outside_eu: '0',
    popup_src: formSrc,
    base_preset
  }
});

export const SHARE_SHELF = ({
  endpoint,
  furnitureId,
  screenshot,
  geom,
  outsideEU,
  shelfCategory
}: {
  endpoint: TYPE_MODEL,
  furnitureId: number,
  screenshot: string,
  geom: Object,
  outsideEU: number,
  shelfCategory: string
}) => $fetch<{
  furniture_category: string,
  id: number,
  seo_slug: string
}>(`/api/v1/gallery/${endpoint}/${furnitureId}/share_on_social_media_popup/`, {
  method: 'POST',
  body: {
    geom,
    magic_preview: screenshot,
    outside_eu: outsideEU,
    popup_src: 'share btn popup',
    shelf_category: shelfCategory
  }
}
);

export const ADD_TO_CART_PRESET = (id: string, model: string, cartId: string, quantity: number = 1) => $fetch(`/api/v2/cart/${cartId}/add_to_cart/`, {
  method: 'post',
  body: {
    sellable_item_id: id,
    content_type: model,
    quantity
  }
});
export const ADD_TO_CART_CART_ID = (id: string, model: string) => useApi(`/api/v1/gallery/${model}/add_to_cart/`, {
  method: 'post',
  body: {
    sellable_item_id: id,
    content_type: model
  }
});

export const GET_UGC_PRODUCT = (furnitureId: string, furnitureType: FurnitureModel) => $fetch(`/api/v1/gallery/${furnitureType}/${furnitureId}/ugc/`, {
  method: 'get'
});

export const GET_SHOWROOMS = (region?: string) => useApi(`api/v1/showrooms/${region ? `?region__name=${region}` : ''}`);
