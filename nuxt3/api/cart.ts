import { useApi } from '~/composables/useApi';
import type { EcoTaxResponse } from '~/types/cart';

export const ECO_TAX = (cartId: string) => useApi<EcoTaxResponse>(`/api/v2/checkout/cart/${cartId}/set_recycle_tax_value/`, {
  method: 'patch'
});

export const CREATE_CART = () => useApi<{id: string}>('/api/v2/cart/create_cart/', {
  method: 'post'
});

export const INCREASE_ITEM_QUANTITY = (itemId:number) =>
  $fetch<{id: number, new_quantity: number}>(`/api/v2/cart/item/${itemId}/increase/`, { method: 'post' });

export const DECREASE_ITEM_QUANTITY = (itemId:number) =>
  $fetch<{id: number, new_quantity: number}>(`/api/v2/cart/item/${itemId}/decrease/`, { method: 'post' });

export const TOGGLE_ITEM_ASSEMBLY_SERVICE = (itemId:number, isAssemblyEnabled: boolean) =>
  $fetch<{id: number, assembly_enabled: boolean}>(`/api/v2/cart/item/${itemId}/assembly/`, {
    method: 'patch',
    body: { assembly_enabled: isAssemblyEnabled }
  });

export const REMOVE_ITEM = (cartId:number | string, itemId: boolean, model: FurnitureModel | 'skuvariant') =>
  $fetch<{id: number, assembly_enabled: boolean}>(`/api/v2/cart/${cartId}/delete_from_cart/`, {
    method: 'post',
    body: {
      sellable_item_id: itemId,
      content_type: model
    }
  });
