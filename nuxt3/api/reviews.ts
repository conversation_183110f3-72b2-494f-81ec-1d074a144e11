import { useApi } from '~/composables/useApi';

export const REVIEWS_GLOBAL = () => useApi<{ avgScore: number, reviewsCount:number, categories: string[] }>('api/v1/global-review-score/', {
  transform: (response: {avg_score: number, reviews_count: number, categories: string[]}) => {
    return {
      avgScore: response.avg_score,
      reviewsCount: response.reviews_count,
      categories: response.categories || []
    };
  }
});

export const REVIEWS = (regionCode: string, page: number, category?: string, ordering?: string, tags?: string[], hasImages?: boolean) => useApi<{ results: any[] }>(
  `/api/v2/reviews/?language=${regionCode}&page=${page}${category ? `&category=${category}` : ''}${ordering ? `&ordering=${ordering}` : ''}${tags && tags.length > 0 ? `&tags=${tags.join(',')}` : ''}${hasImages ? '&has_images=true' : ''}`
);

export const PDP_REVIEWS = (category: string, shelf_type: number, material?: number, has_images?: boolean, tags?: string[]) =>
  useApi<{ results: any[] }>(`/api/v2/reviews/pdp/?category=${category}&shelf_type=${shelf_type}&material=${material}${has_images ? '&has_images=true' : ''}${tags && tags.length > 0 ? `&tags=${tags.join(',')}` : ''}`);

export const CATEGORY_SCORE = (category?: string) =>
  useApi<{ averageScore: number, reviewsCount: number, activeTags: string[] }>(`/api/v2/reviews/review-score/${category ? `?category=${category}` : ''}`);

export const GET_REVIEW_ORDER = (email: string, orderNumber: string) =>
  useApi<{ email: string, orderNumber: string }>(`api/v2/reviews/order-items/unreviewed/?email=${email}&order_number=${orderNumber}`);
