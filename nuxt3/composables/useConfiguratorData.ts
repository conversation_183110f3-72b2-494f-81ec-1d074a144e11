import { usePdpStore } from '~/stores/pdp';
import { useGlobal } from '~/stores/global';
import { SHORT_MODEL_TO_MODEL } from '~/utils/configuratorTypeToModel';

export function useConfiguratorData () {
  const { t } = useI18n();
  const route = useRoute();
  const { locale } = useLocale();

  const {
    regionName,
    isSignedIn,
    t03Available,
    currencyCode,
    countryLocale,
    isSalesEnabled,
    reviewsAverageScore,
    waitingListTokenActive,
    waitingListTokenExpired,
    s01Available
  } = storeToRefs(useGlobal());

  const {
    width,
    depth,
    title,
    price,
    height,
    preview,
    pricing,
    material,
    delivery,
    shelfType,
    seoTitles,
    patternName,
    priceInEuro,
    gridAllColors,
    deliverySummary,
    isGlobalPromoOn,
    configuratorType,
    priceWithDiscount,
    furnitureCategory,
    configuratorPreview,
    priceWithDiscountInEuro
  } = storeToRefs(usePdpStore());

  const {
    code,
    value,
    endDate,
    extraData,
    percentage,
    minThreshold,
    maxThreshold,
    categories: categoriesInPromotion,
    strikethroughPricing
  } = storeToRefs(usePromoStore());

  const currentCategoryPromotionValue = computed(() =>
    categoriesInPromotion.value.find(item => item.categoryName === furnitureCategory.value)?.value);

  const promotionValue = computed(() =>
    currentCategoryPromotionValue.value || `-${value.value}%`);

  let { furniture, id } = route.params;

  if (!furniture && id) {
    furniture = id;
  }

  const furnitureParams = (furniture as string).split('?').join(',').split(',');
  const [productID] = furnitureParams;
  const productModel = SHORT_MODEL_TO_MODEL(furnitureParams[1] as keyof typeof SHORT_MODEL_TO_MODEL);
  const globalStore = useGlobal();
  const isOriginalRowABTest = computed(() => globalStore.AB_TEST_VALUE('original_row_configurator')?.isActive);
  const isOriginalColABTest = computed(() => globalStore.AB_TEST_VALUE('original_col_configurator_v2')?.isActive);

  const typeToConfiguratorType = () => {
    if (shelfType.value === 6 || shelfType.value === 7 || shelfType.value === 8) {
      return 'expressions';
    } else if (shelfType.value === 4 || shelfType.value === 5) {
      return 'edge';
    } else if (shelfType.value === 10) {
      return 'sotty';
    } else if (configuratorType.value === 2 && (shelfType.value === 0 || shelfType.value === 1 || shelfType.value === 2)) {
      return isOriginalColABTest.value ? 'col' : 2;
    } else if (configuratorType.value === 1 && (shelfType.value === 0 || shelfType.value === 1 || shelfType.value === 2)) {
      return isOriginalRowABTest.value ? 'row' : 1;
    } else {
      return configuratorType.value;
    }
  };

  const configuratorProps = {
    language: locale.value,
    locale: countryLocale.value,
    currency: pricing.value ? pricing.value.priceIso : 'EUR',
    configuratorType: typeToConfiguratorType(),
    furnitureType: productModel,
    itemId: productID, // todo
    price: price.value,
    priceeee: pricing.value ? pricing.value.priceeee : '0',
    priceeel: pricing.value ? pricing.value.priceeel : '0',
    isGlobalPromoOn: isGlobalPromoOn.value,
    isSignedIn: isSignedIn.value,
    region: regionName.value,
    furnitureTitle: seoTitles.value &&
      material.value !== null &&
      shelfType.value !== null &&
      seoTitles.value[shelfType.value]?.[material.value]
      ? seoTitles.value[shelfType.value][material.value]
      : title.value,
    defaultProductPrice: price.value,
    countryLocale: countryLocale.value,
    currencyCode: currencyCode.value,
    isSalesEnabled: isSalesEnabled.value,
    t03Available: t03Available.value,
    waitingListTokenActive: waitingListTokenActive.value,
    waitingListTokenExpired: waitingListTokenExpired.value,
    pricing: pricing.value,
    delivery: delivery.value,
    deliverySummary: deliverySummary.value,
    gridAllColors: gridAllColors.value,
    shelfType: shelfType.value,
    preview: preview.value,
    width: width.value,
    height: height.value,
    depth: depth.value,
    material: material.value,
    priceWithDiscount: priceWithDiscount.value,
    deliveryTimesArray: deliverySummary.value,
    priceInEuro: priceInEuro.value,
    patternName: patternName.value,
    furnitureCategory: furnitureCategory.value,
    priceWithDiscountInEuro: priceWithDiscountInEuro.value,
    code: code.value,
    value: value.value,
    percentage: percentage.value,
    strikethroughPricing: strikethroughPricing.value,
    endDate: endDate.value,
    minThreshold: minThreshold.value,
    maxThreshold: maxThreshold.value,
    userReview: `${reviewsAverageScore.value}/5`,
    configuratorPreview: configuratorPreview.value,
    seasonSale: {
      seasonSaleOn: shelfType.value === 10 && extraData.value?.customBadges,
      primaryColor: extraData.value?.backgroundColor,
      secondaryColor: extraData.value?.copyColor,
      pdpBadgeLabel: '',
      badgeLabelWithoutPercentage: extraData.value?.translateLabel ? t(extraData.value?.customLabelSofa) : extraData.value?.customLabelSofa,
      customBadgeTextColor: extraData.value?.customBadgeTextColor,
      customBadgeBackgroundColor: extraData.value?.customBadgeBackgroundColor
    },
    popupBadge: {
      event: '',
      label: '',
      textColor: '',
      backgroundColor: ''
    },
    disableAddToCart: !s01Available.value
  };

  return {
    configuratorProps
  };
}
