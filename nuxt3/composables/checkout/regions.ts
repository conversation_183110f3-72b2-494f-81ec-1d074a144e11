import { useToast } from 'vue-toastification';
import type { RegionName } from '~/consts/regions';
import { useScartStore } from '~/stores/scart';
import { useGlobal } from '~/stores/global';

import useRegions from '~/composables/useRegions';
import useCartStatus from '~/composables/useCartStatus';

export const checkoutRegions = (formValues: {value: {region: string}}, otherCountry: boolean = false, submitForm: (() => void) | null = null) => {
  const global = useGlobal();
  const checkoutStore = useCheckoutStore();
  const { hasT03 } = useScartStore();
  const { $logException, $i18n } = useNuxtApp();
  const toast = useToast();
  const { fetchUserStatus } = useCartStatus();
  const checkoutSwitchRegionModal = ref(false);

  const { regionsForSelect, changeRegionWithoutReloading } = useRegions();

  let newRegion = '';

  function getRegion (val: string = formValues.value.region) {
    return regionsForSelect.find(r => r.value === val);
  }

  const changeRegion = (val: string, oldValue: any) => {
    if (!oldValue && !otherCountry) { return; }
    if (val === global.regionName) { return; }
    newRegion = val;

    if (!otherCountry) {
      formValues.value.region = global.regionName;
    }

    checkoutStore.SET_REGION_TO_SET(getRegion(newRegion) as { label: string; value: string });
    checkoutSwitchRegionModal.value = true;
  };

  watch(() => formValues.value.region, changeRegion);
  const router = useRouter();
  const i18n = useI18n();
  const { createLangCode } = useLocale();
  const switchLocalePath = useSwitchLocalePath();

  const onSuccess = async () => {
    if (global.t03Available && hasT03) { return; }
    if (global.s01Available && global.hasS01) { return; }
    if (global.corduroyAvailable && global.hasCorduroy) { return; }
    const region = getRegion();

    if (region !== undefined) {
      try {
        const { data } = await changeRegionWithoutReloading(newRegion);

        global.UPDATE_REGION({ ...data.value, region: newRegion });
        formValues.value.region = newRegion;
        checkoutSwitchRegionModal.value = false;

        const newLangCode = createLangCode(data.value?.language, data.value?.region_code);
        const newPath = switchLocalePath(newLangCode);
        await i18n.setLocale(newLangCode);
        await router.replace(newPath);

        if (submitForm) {
          await submitForm();
        }

        await fetchUserStatus();
      } catch (e) {
        console.error(e);
        toast.error(String($i18n.t('common.error.connection')));
        $logException(e);
      }
    }
  };

  return {
    regionsForSelect,
    checkoutSwitchRegionModal,
    onSuccess
  };
};

export const regionsUtils = () => {
  const toast = useToast();
  const { $i18n, $logException } = useNuxtApp();
  const { changeRegionWithoutReloading } = useRegions();
  const global = useGlobal();

  const forceChangeRegion = async (region: RegionName) => {
    try {
      const payload = await changeRegionWithoutReloading(region);
      global.UPDATE_REGION({ ...payload, region });
    } catch (e) {
      console.error(e);
      toast.error(String($i18n.t('common.error.connection')));
      $logException(e);
    }
  };

  return {
    forceChangeRegion
  };
};
