import { getQuery } from 'h3';
import { CATEGORIES } from '~/consts/categories';

interface YotpoImage {
  image_id: string;
  original_image_url: string;
  medium_image_url: string;
  image_url: string;
  thumbnail_url?: string;
  standard_resolution_url?: string;
  media_type: string;
  post?: {
    username?: string;
    content?: string;
    created_time?: string;
  };
  tagged_products?: Array<{
    id: number;
    name: string;
    product_link: string;
  }>;
}

interface YotpoAlbumResponse {
  status: {
    message: string;
    code: number;
  };
  response: {
    pagination: {
      page: number;
      per_page: number;
      total: number;
    };
    images: YotpoImage[];
  };
}

export default defineCachedEventHandler(async (event) => {
  const query = getQuery(event);
  const page = Number(query.page) || 1;

  const config = useRuntimeConfig();

  const albumNames = ['sideboard', 'bookcase', 'wallstorage', 'wardrobe', 'tvstand', 'bedside_table', 'chest', 'vinyl_storage', 'shoerack', 'desk'];

  try {
    const albumPromises = albumNames.map(async (albumName) => {
      try {
        const albumResponse = await $fetch<YotpoAlbumResponse>(
          `https://api.yotpo.com/v1/widget/${config.public.yotpoId}/albums/by_name`,
          {
            method: 'GET',
            query: {
              album_name: albumName,
              per_page: 18,
              page
            }
          }
        );

        const images = albumResponse.response?.images || [];
        const categories = CATEGORIES();

        const pagination = albumResponse.response?.pagination;
        const totalPages = pagination ? Math.ceil(pagination.total / pagination.per_page) : 0;
        const hasMorePages = pagination ? pagination.page < totalPages : false;

        return {
          name: albumName,
          prettyName: categories[albumName]?.pluralNameKey || null,
          total: pagination?.total || 0,
          viewMore: hasMorePages,
          images: images.map(image => ({
            id: image.image_id,
            imageUrl: `https:${image.original_image_url}`,
            mediumUrl: `https:${image.medium_image_url}`,
            thumbnailUrl: `https:${image.image_url}`,
            username: image.post?.username || '',
            caption: image.post?.content || '',
            createdAt: image.post?.created_time || '',
            mediaType: image.media_type,
            taggedProducts: image.tagged_products || [],
            ...(image.media_type === 'video' && {
              videoUrl: image.standard_resolution_url,
              thumbnailUrl: image.thumbnail_url
            })
          }))
        };
      } catch (albumError) {
        console.warn(`Failed to fetch album "${albumName}":`, albumError);
        return {
          name: albumName,
          imagesCount: 0,
          photos: []
        };
      }
    });

    const albums = await Promise.all(albumPromises);

    return {
      success: true,
      albums: albums.filter(album => album !== null)
    };
  } catch (error) {
    console.error('Yotpo albums fetch error:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch Yotpo albums'
    });
  }
}, {
  base: 'redis',
  maxAge: 60 * 10
});
